{
  "extends": [
    "@tencent/eslint-config-mole/ts",
    "@tencent/eslint-config-mole/ts/vue"
  ],
  "plugins": [],
  "overrides": [
    {
      "files": [
        "**/test/unit/**/*.spec.{j,t}s?(x)"
      ],
      "env": {
        "jest": true
      }
    }
  ],
  "rules": {
    "max-lines-per-function": "off",
    "simple-import-sort/imports": ["error", {
      "groups": [
        [
          // Node.js 内置模块
          "^(assert|buffer|child_process|cluster|console|constants|crypto|dgram|dns|domain|events|fs|http|https|module|net|os|path|punycode|querystring|readline|repl|stream|string_decoder|sys|timers|tls|tty|url|util|vm|zlib|freelist|v8|process|async_hooks|http2|perf_hooks)(/.*|$)",
          // 外部第三方模块
          "^(?!@tencent\/)",
          // 公司内部模块
          "^@tencent\/"
        ],
        [
          // common
          "^common\/",
          // 其他绝对路径（目前新项目需要手动加进去）
          "^(buff-center|app|agreement|yyb-platform|ysdk|cloud-game|cloud-game-center|growth|mini-game|new-game|red-envelope-activity)\/"
        ],
        [
          "^src\/"
        ],
        [
          // 相对路径
          "^\\."
        ]
      ]
    }],
    "simple-import-sort/exports": "error",
    "vue/no-v-html": "off", // 原来是 warn 级别，需保证传入 v-html 已转义
    "quotes": ["error", "single"],
    "vue/multi-word-component-names": "off"
  }
}