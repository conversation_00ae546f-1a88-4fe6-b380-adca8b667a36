# MOKA-CLI

MOKA CLI 是一个命令行界面工具，可用于
直接从命令 shell 初始化、开发、搭建和维护 MOKA平台组件库 应用程序。

## 环境准备

node.js >= 16

pnpm >= 8

先安装 pnpm

```bash
$ npm install -g pnpm
```
## 使用CLI工具开发组件库
然后安装依赖

```bash
$ pnpm bootstrap
```

## 创建模块
通过moka命令行快速创建对应模块

### 创建组件
```bash
$ moka create --c name
```
#### 组件目录结构说明
```
├─components 组件目录
|  ├─test-component 测试组件
|  | ├─moka.config.json moka组件描述文件
|  | ├─package.json
|  | ├─vite.config.js 本地调试的vite配置
|  | ├─src
|  | |  ├─index.ts 组件主入口
|  | |  ├─meta.ts 表单配置描述协议
|  | |  ├─report.ts 上报描述协议
|  | |  ├─use-cases 用例层
|  | |  |  ├─index.ts
|  | |  ├─view 视图层
|  | |  |  ├─index.scss
|  | |  |  └-index.vue
|  | ├─example 本地调试案例
|  | |    ├─index.html
|  | |    ├─index.vue
|  | |    └-main.ts
```


### 创建插件
```bash
$ moka create --p name
```
#### 插件目录结构说明
```
├─plugins 插件目录
|    ├─test-plugin 测试插件
|    |   ├─moka.config.json moka模块描述
|    |   ├─package.json
|    |   ├─src
|    |   |  ├─index.ts 插件入口文件
|    |   |  └-meta.ts 表单配置描述协议
```


### 创建domain
```bash
$ moka create --d
```
#### 组件目录结构说明
```
├─domains
|  ├─moka.config.json moka模块描述
|  ├─package.json
|  ├─src
|  |  ├─index.ts domain入口文件
|  |  ├─modules domain 模块
|  |  |    ├─common
|  |  |    |   ├─use-case 用例层
|  |  |    |   ├─entities 实体层
|  |  |    |   ├─apis 接口层
|  |  |    |   ├─anti-corruptions 防腐层
```


## 运行项目

### 调试组件 - 本地调试
```bash
$ cd components/demo
$ pnpm dev
```

### 调试组件 - 在线调试
- 1.启动moka调试命令
  ```bash
  $ moka dev --c componentName --p pluginName --d
  ```

  |选项|描述|
  |---|---|
  |--c,--component [name]|指定调试的组件名，模糊搜索|
  |--p,--plugin [name]|指定调试的插件名，模糊搜索|
  |--d,--domain |是否调试domain|

- 2.打开MOKA编辑器，启动本地调试能力，配置调试地址
  

## 组件发布
### 1.配置组件流水线地址, ./moka.config.json
```json
"devOps": {
  "test" : {
    "buildApiUrl": "",
    "detailUrl": ""
  },
  "prod": {
    "buildApiUrl": "",
    "detailUrl": ""
  }
}
```
### 2.组件发布
```bash
$ moka push --c componentName -SSR
```
|选项|描述|默认值|
|---|---|---|
|--e,--env |发布环境 test/prod | prod |
|--c,--component [name]|指定发布的组件名||
|--p,--plugin [name]|指定发布的插件名||
|--d,--domain |发布domain||
|--iv,--ignoreVerify|忽略本地校验，npm白名单、git信息、git分支|false|

### 4.发布生产/测试环境
  - 新增组件配置：https://act.woa.com/xy/app/test/hdzt/moka-frontend-comps?appid=45
  - 组件构建UI版本，流水线手动触发，版本号使用毫秒级时间戳 Date.now()，流水线地址：https://devops.woa.com/console/pipeline/yyb/p-7a7cc6b3540c410e9e5dae26313e00bb/history
  - 编辑器使用：在MOKA编辑器左侧工具栏的“版本”刷新，选中构建时间戳版本

## 项目目录
```
─.gitignore
├─README.md
├─package.json
├─moka.config.json moka项目描述
├─plugins 插件目录
|    ├─test-plugin 测试插件
|    |   ├─moka.config.json moka模块描述
|    |   ├─package.json
|    |   ├─src
|    |   |  ├─index.ts 插件入口文件
|    |   |  └-meta.ts 表单配置描述协议
├─domains
|    ├─moka.config.json moka模块描述
|    ├─package.json
|    ├─src
|    |  ├─index.ts domain入口文件
|    |  ├─modules domain 模块
|    |  |    ├─common
|    |  |    |   ├─use-case 用例层
|    |  |    |   ├─entities 实体层
|    |  |    |   ├─apis 接口层
|    |  |    |   ├─anti-corruptions 防腐层
├─components
|     ├─test-component 测试组件
|     |  ├─moka.config.json moka组件描述文件
|     |  ├─package.json
|     |  ├─vite.config.js 本地调试的vite配置
|     |  ├─src
|     |  |  ├─index.ts 组件主入口
|     |  |  ├─meta.ts 表单配置描述协议
|     |  |  ├─report.ts 上报描述协议
|     |  |  ├─view
|     |  |  |  ├─index.scss
|     |  |  |  └-index.vue
|     |  ├─example 本地调试案例
|     |  |    ├─index.html
|     |  |    ├─index.vue
|     |  |    └-main.ts
```
