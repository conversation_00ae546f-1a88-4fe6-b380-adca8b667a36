{"name": "@tencent/moka-ui-domain", "version": "0.0.34", "description": "活动中台组件模板", "main": "dist/index.umd.js", "module": "src/index", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"dev": "vite", "build": "vite build"}, "author": "yamachen<<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "", "directory": "/domains"}, "dependencies": {"@tencent/mesh-sdk": "^1.1.15", "@tencent/moka-athena-next-v2": "latest", "@tencent/moka-data-core": "latest", "@tencent/moka-schema": "^0.0.3", "@tencent/mole-jsbridge": "^1.0.4", "@tencent/mole-logger": "^1.0.10", "@tencent/mole-report": "^1.0.11", "@tencent/mole-utils-lib": "^2.0.4", "@tencent/yyb-login-core": "^1.0.8", "js-cookie": "^3.0.5"}, "devDependencies": {"@tencent/mole-jsbridge-core": "^1.0.8", "@tencent/yyb-partition-role-select": "^2.0.2", "@types/js-cookie": "^3.0.6", "cross-env": "^7.0.3", "pinia": "^2.1.6", "vite-plugin-dts": "^4.0.2"}}