import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
  },
  server: {
    open: '/example/index.html',
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    lib: {
      entry: 'src/index.ts',
      name: 'moka-domain',
      fileName: format => `index.${format}.js`,
      formats: ['umd', 'es'],
      minify: true,
    },
    rollupOptions: {
      input: 'example/index.html',
    },
  },
  plugins: [
    dts({
      outputDir: 'dist',
      entryRoot: 'src',
    }),
  ],
});
