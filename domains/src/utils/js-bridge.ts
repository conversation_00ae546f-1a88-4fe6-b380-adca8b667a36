import MoleJSBridge from '@tencent/mole-jsbridge';
import { getAppType } from '@tencent/mole-utils-lib';

const moleJSBridge = MoleJSBridge.getJSBridge();

globalThis.moleJSBridge = moleJSBridge;

enum JsbridgeName {
  GetChannelIdInfo = 'getChannelIdInfo',
}

/**
 * 获取渠道号
 * @param packageName 游戏包名
 * return 渠道号信息
 */
export async function getChannelIdInfo(packageName: string) {
  const result = await moleJSBridge?.call<unknown, {[key: string]: { channeldId: string; install: number }}
  >(JsbridgeName.GetChannelIdInfo, { packagenames: packageName });

  // 调用 Jsbridge 失败
  if (!result) {
    console.error('[getChannelIdInfo] 调用 getChannelIdInfo Jsbridge 失败', result);
    return;
  }

  console.log('[getChannelIdInfo] 调用结果', result);

  // 获取渠道号
  const { channeldId: channelId, install } = result.data?.[packageName] || {};
  if (!channelId) {
    console.error('[getChannelIdInfo] 获取游戏渠道号异常', result);
    return;
  }

  return { channelId, installed: Boolean(install) };
}

/**
 * 打开新窗口
 * @param url 链接参数
 */
export function openNewWindow(url: string) {
  try {
    const { isYYB, isMobileQQ } = getAppType(navigator.userAgent);
    if (isYYB) {
      void moleJSBridge!.ui!.openWebview(url);
      return;
    }

    if (isMobileQQ) {
      void moleJSBridge!.ui!.openWebview(url, {
        target: 1, // 新开webview跳转
        style: 2,
      });

      return;
    }

    window.location.href = url;
  } catch (e) {
    window.location.href = url;
  }
}
