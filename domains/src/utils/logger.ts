import { LogParams, MoleLogger } from '@tencent/mole-logger';

export const logger: MoleLogger = new Proxy<MoleLogger>({} as unknown as <PERSON><PERSON><PERSON>og<PERSON>, {
  get(_target, prop: keyof MoleLogger) {
    return (...args: LogParams[]) => {
      const logMethod = MoleLogger.getInstance('hdzt')?.[prop];
      if (typeof logMethod === 'function') {
        MoleLogger.getInstance('hdzt')![prop](...(args as LogParams));
      }
    };
  },
});
