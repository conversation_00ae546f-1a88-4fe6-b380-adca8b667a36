import { actUtils } from '@tencent/moka-data-core';

import { openNewWindow } from './js-bridge';

/**
 * 从当前URL中获取指定key的参数值
 * @param key - 要获取的参数的键名
 * @returns - 返回键对应的值，如果不存在则返回null
 */
export function fetchQueryParam(key: string) {
  const urlParams = new URLSearchParams(location.search);

  return urlParams.get(key);
}

/**
 * 根据shareKey生成分享链接
 * @param shareKey - 分享的key
 * @param key - 默认 share_key
 * returns 返回生成分享链接之后的URL
 */
export function getShareUrl(shareKey: string, key = 'share_key') {
  const urlObj = new URL(location.href);
  urlObj.searchParams.set(key, shareKey);
  return urlObj.toString();
}

/**
 * 获取助力 id
 * @param modID - 模块id
 * returns boostId
 */
export function getBoostId(modID: string|number) {
  // ciid 格式: iid_yybfissionpoint_z5pgpmtslw
  const splitCIID = actUtils.getCIIDByModID({ modID: String(modID) }).split('_');
  return splitCIID[splitCIID.length - 1];
}

/**
 * 跳转处理（宝内、QQ优先openNewWindow）
 * @param url 链接参数
 */
export function jumpUrl(url: string) {
  openNewWindow(url);
}

/**
 * 根据生成分享链接
 * @param params 分享链接参数
 * @param needClearParams 需要清除的参数
 * returns 返回生成分享链接之后的 URL
 */
export function getShareUrlWithParams(
  params: Record<string, string | number | boolean>,
  needClearParams: string[] = [],
) {
  const urlObj = new URL(location.href);
  // 清除参数
  if (needClearParams.length > 0) {
    needClearParams.forEach((param) => {
      urlObj.searchParams.delete(param);
    });
  }

  Object.keys(params).forEach(key => urlObj.searchParams.set(key, String(params[key])));
  return urlObj.toString();
}
