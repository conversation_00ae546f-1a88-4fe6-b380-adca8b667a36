import { isPC } from '@tencent/mole-utils-lib';

import { PageStyle } from '../constants/common';

/**
 * 是否是应用宝 app
 */
export const isYYB = () => {
  const ua = window.navigator.userAgent;

  return /qqdownloader\/(\d+)/i.test(ua);
};

/**
 * 判断是否是微信
 */
export const isWechat = () => {
  const ua = window.navigator.userAgent;

  return ua.indexOf('MicroMessenger') > -1;
};

/**
 * 判断是否是手Q
 */
export const isQQ = () => {
  const ua = window.navigator.userAgent;

  return (/QQ[\s/]*(\d+(\.\d+)*)/i).test(ua);
};

/**
 * 判断是否是 PC 宽屏样式：当且仅当 PC 端 UA 且页面配置开启了 PC 宽屏
 * @returns 是否为 PC 宽屏样式
 */
export const isPCStyle = () => isPC(navigator.userAgent)
  && (window as any).magicUiconfig?.[0]?.items[0]?.pageStyle === PageStyle.PC;
