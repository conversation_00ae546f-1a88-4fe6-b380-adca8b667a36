// @ts-ignore 允许的导入方式
import Cookies from 'js-cookie';
import { getCookie, getUUIDV4, setCookie } from '@tencent/mole-utils-lib/client';
import { getLoginInfoFromCookie, LoginType } from '@tencent/yyb-login-core';

import { USER_INFO_KEY_PREFIX } from '../constants';

/** 登录类型 */
export enum ELoginType {
  /** 微信 */
  Wx = 'wx',
  /** 宝内手Q */
  Mobileq = 'mobileq',
  /** 宝外游游多手 q */
  YYDMobileq = 'qq',
  /** 宝内未登录 */
  None = 'none',
  /** 其他可能未登录 */
  Null = 'null',
}

/** 获取用户登录态 */
export function getLoginType() {
  const cookies = Cookies.get();
  const loginType = cookies.logintype || cookies.yyb_logintype || '';
  const formatLoginType = loginType.toLowerCase() as ELoginType;

  return formatLoginType;
}

/** 获取用户 OpenID */
export function getLoginUserOpenID() {
  const info = getLoginInfoFromCookie();
  return info.loginType === LoginType.None ? '' : info.openID;
}

/**
 * 获取用户信息
 * @param openID 用户 openID
 * @returns 用户信息
 */
export function getUserNameInfo(openID: string) {
  const userInfoCacheStr = localStorage.getItem(`${USER_INFO_KEY_PREFIX}${openID}`);
  if (!userInfoCacheStr) return null;

  try {
    const userInfoData = JSON.parse(userInfoCacheStr);
    return userInfoData?.data?.result as {
      nickname: string;
      avatar: string;
    } || null;
  } catch (e) {
    return null;
  }
}

/**
 * h5 侧生成用户的 uuid
 * @description cookie 里有直接取 cookie 里的 yyb_pvid 字段，没有则生成并种入 cookie
 */
export function generateUserUUID() {
  const yearTimestamp = 360 * 24 * 60 * 60;
  let pgvPvid = getCookie(document.cookie, 'yyb_pvid');
  if (!pgvPvid) {
    pgvPvid = getUUIDV4();
    setCookie('yyb_pvid', pgvPvid, { domain: '.qq.com', path: '/', maxAge: yearTimestamp });
  }

  return pgvPvid;
}
