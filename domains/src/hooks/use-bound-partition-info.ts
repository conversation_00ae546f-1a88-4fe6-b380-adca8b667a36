import { ref } from 'vue';
import { getGamePartition, openPartitionRoleSelect, PartitionType } from '@tencent/yyb-partition-role-select';

import { usePartitionRoleStore } from '../store';
import { isTestEnv } from '../utils/env';
import { getLoginUserOpenID } from '../utils/user';

/**
 * 获取绑定区服信息的 hooks
 * @param packageName 包名
 * @returns 绑定的区服信息
 */
export function useBoundPartitionInfo(packageName: string, oPartitionType?: PartitionType) {
  const partitionType = ref<PartitionType | undefined>(oPartitionType);
  const { getPartitionRole, setPartitionRole } = usePartitionRoleStore();

  async function updatePartitionType() {
    if (partitionType.value !== undefined) return;
    partitionType.value = await getGamePartition(packageName, isTestEnv());
  }

  async function getPartitionRoleInfo() {
    const openID = getLoginUserOpenID();
    const boundPartitionAndRole = getPartitionRole(packageName, openID);
    if (boundPartitionAndRole) return { data: boundPartitionAndRole };

    await updatePartitionType();

    if (partitionType.value === PartitionType.NOT_PARTITION) return {};

    const result = await openPartitionRoleSelect({
      pkgName: packageName,
      partitionType: partitionType.value!,
      popupTitle: '区服选择',
      needSaveAfterSuccess: true,
    });
    if (result.ret !== 0) return;

    setPartitionRole(packageName, openID, result.data!);

    return result;
  }

  return {
    partitionType,
    getPartitionRoleInfo,
  };
}
