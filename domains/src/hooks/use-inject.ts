import { computed, ComputedRef, getCurrentInstance, inject } from 'vue';
import { isYSDK } from '@tencent/mole-utils-lib';

import { sleep } from '../utils/time';

interface UserInfo {
  headImgUrl: string;
  nickname: string;
  type: string;
}

interface Env {
  os: string;
  app: string;
  device: string;
  userAgent: string;
  platform: string;
  isIphone: boolean;
  isIpad: boolean;
  isIos: boolean;
  isAndroid: boolean;
  isApad: boolean;
  isMac: boolean;
  isWin: boolean;
  isTenvideoApp: boolean;
  isWeb: boolean;
  isLocalPkg: boolean;
  isWechat: boolean;
  isWeChatMiniProgram: boolean;
  isWetv: boolean;
  isLiteApp: boolean;
  isHDApp: boolean;
  isKidApp: boolean;
  isTenvideoTadChid: boolean;
  isBaseApp: boolean;
  isMqq: boolean;
  isMqb: boolean;
  isPCClient: boolean;
  isWeishi: boolean;
  isSports: boolean;
  isYyb: boolean;
  isPdd: boolean;
  isAp: boolean;
  isBbg: boolean;
  isAc: boolean;
}

interface App {
  shareConfig: Record<string, string>;
  userInfo: UserInfo;
  $openLogin: () => Promise<void> | void;
  getUserInfo: () => Promise<UserInfo>;
  loginReady: Promise<boolean>;
  switchLogin: () => Promise<void>;
  checkLogin: () => Promise<boolean>;
  setShareConfig: (shareConfig: unknown) => void;
}

interface Bus {
  $on: (...args: any) => any;
  $once: (...args: any) => any;
  $off: (...args: any) => any;
  $emit: (...args: any) => any;
}

interface UseMokaInjectReturn {
  app?: App;
  tvfJsapi?: { env: Env};
  userInfo: ComputedRef<UserInfo|undefined>;
  inMagic?: boolean;
  isLogin: ComputedRef<boolean|undefined>;
  loginReady?: Promise<boolean>;
  openLogin?: () => void;
  switchLogin?: () => void;
  getUserInfo?: () => Promise<UserInfo>;
  toast?: (text: string) => void;
  $bus?: Bus;
}

const inYSDK = isYSDK(navigator.userAgent);

/**
 * 注入 moka 挂载的全局变量
 * @returns UseMokaInjectReturn 包含isLogin、userInfo、openLogin、toast等变量的对象
 */
export const useMokaInject = (): UseMokaInjectReturn => {
  const inMagic = inject<boolean>('inMagic');
  const toast = inject<(text: string) => void>('toast');
  const app = inject<App>('app');
  const tvfJsapi = inject<{env: Env}>('tvfJsapi');
  const commonStore = inject<{isLogin: boolean; $patch: (op: any) => void}>('store');
  const $bus = inject<Bus>('$bus');

  const userInfo = computed(() => app?.userInfo);
  // YSDK 默认已登录
  const isLogin = computed(() => inYSDK || commonStore?.isLogin);

  const instance = getCurrentInstance();

  // FIXME: 会员需求需要移除iwan-login插件，所以需要临时对涉及到登录插件的相关能力进行补丁处理
  if (app && !instance?.appContext.config.globalProperties?.$magicLogin) {
    const waitLoginReady = async (): Promise<void> => {
      if (!(window as any).vipCommonLogic) {
      // 等待 vipCommonLogic 加载完成，延迟 100ms
        await sleep(100);
        await waitLoginReadyPromiseWrapper();
      }

      const isLoginInner = await (window as any).vipCommonLogic.checkLogin();
      commonStore?.$patch({
        isLogin: isLoginInner,
      });
    };

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    const waitLoginReadyPromiseWrapper = () => new Promise<void>(async (resolve) => {
      await waitLoginReady();
      resolve();
    });

    app.loginReady = (async () => {
      await waitLoginReadyPromiseWrapper();
      return true;
    })();

    app.$openLogin = async () => {
      await waitLoginReadyPromiseWrapper();
      (window as any).vipCommonLogic?.openLogin();
    };

    // 适配原来的 iwan 登录插件
    if (instance?.appContext.config.globalProperties) {
      instance.appContext.config.globalProperties.$magicLogin = {
        ...(instance.appContext.config.globalProperties.$magicLogin || {}),
        openLogin: app.$openLogin,
        getUserInfo: async () => {
          const info = await (window as any).vipCommonLogic?.getUserInfo();
          return info;
        },
      };
    }
  }

  return {
    app,
    tvfJsapi,
    userInfo,
    inMagic,
    isLogin,
    $bus,
    loginReady: app?.loginReady,
    openLogin: app?.$openLogin,
    switchLogin: app?.switchLogin,
    getUserInfo: app?.getUserInfo,
    toast,
  };
};
