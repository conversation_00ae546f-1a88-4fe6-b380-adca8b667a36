import type MeshSdk from '@tencent/mesh-sdk';
import type { InitParam } from '@tencent/mesh-sdk/dist/types';

import { NO_INSTALL_VERSION, YYB_PKG_NAME } from '../constants';
import { InstallState } from '../types';

export class DownloadEntity {
  private loadSDKPromise?: Promise<typeof MeshSdk>;
  private meshSdk?: typeof MeshSdk;
  private yybVersion?: string;
  private downloadTaskMaps: Map<number, MeshSdk> = new Map();

  public async getDownloadTask(params: InitParam): Promise<MeshSdk> {
    const taskId = this.getTaskId(params);
    const downloadTask = this.downloadTaskMaps.get(taskId);
    if (downloadTask) {
      return downloadTask;
    }

    const downloader = new (await this.getMeshSDK())(params);
    this.downloadTaskMaps.set(taskId, downloader);
    return downloader;
  }

  /** 获取应用宝在装版本号 */
  public async getYYBVersion() {
    if (this.yybVersion) {
      return this.yybVersion;
    }

    const [version = NO_INSTALL_VERSION] = await (await this.getMeshSDK()).getAppInstalledVersions([YYB_PKG_NAME]);
    this.yybVersion = version;
    return version;
  }

  /** 获取meshSDK， 避免重复请求 */
  public async getMeshSDK() {
    if (this.meshSdk) {
      return this.meshSdk;
    }

    if (this.loadSDKPromise) {
      return this.loadSDKPromise;
    }

    this.loadSDKPromise = this.loadMeshSDK();

    return this.loadSDKPromise;
  }

  /** 是否已安装应用宝 */
  public async isYYBInstalled() {
    try {
      const version = await this.getYYBVersion();
      return version === NO_INSTALL_VERSION ? InstallState.UnInstalled : InstallState.Installed;
    } catch (error) {
      return InstallState.UnInstalled;
    }
  }

  private async loadMeshSDK() {
    return (await import('@tencent/mesh-sdk')).default;
  }

  private getTaskId(params: InitParam) {
    // 生成 hash code
    const string = JSON.stringify(params);
    let hash = 0;
    if (string.length === 0) {
      return hash;
    }

    for (let i = 0; i < string.length; i++) {
      const code = string.charCodeAt(i);
      hash = ((hash << 5) - hash) + code;
      hash |= 0; // Convert to 32bit integer
    }

    return hash;
  }
}

export const downloadEntity = new DownloadEntity();
