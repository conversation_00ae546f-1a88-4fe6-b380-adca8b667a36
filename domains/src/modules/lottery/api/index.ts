import { actExecForwardRequester, actUtils } from '@tencent/moka-data-core';
import { camelizeKeys } from '@tencent/mole-utils-lib';

import type { GetUserLotteryResp } from '../types';

/**
 * 通过抽奖 ID 获取用户抽奖结果信息
 * @param modId 抽奖模块 ID
 * @returns 抽奖模块 ID 对应的抽奖结果信息
 */
export async function getUserLotteryResultByModID(modId: string): Promise<GetUserLotteryResp | undefined> {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const resp = await actExecForwardRequester.request({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/GetUserLotteryResult',
      data: {
        lottery_iid: actUtils.getCIIDByModID({ modID: modId }),
      },
    },
  });

  if (resp.code !== 0 || !resp.body?.data) {
    console.error(`[getUserLotteryResultByModID] 获取用户单模块抽奖信息失败，code: ${resp.code}, tip: ${resp.tip}`);
    return;
  }

  return camelizeKeys(resp.body.data) ;
}
