/** 用户抽奖结果状态 */
export enum UserLotteryResultStatus {
  /** 未知状态 */
  UNKNOWN = 0,
  /** 中奖（用于 DoLottery、GetUserLotteryResult） */
  WON = 1,
  /** 未中奖（用于 DoLottery、GetUserLotteryResult） */
  NOT_WON = 2,
  /** 已参与待开奖（针对开奖模式，用于 DoLottery、GetUserLotteryResult） */
  TO_BE_REVEALED = 3,
  /** 用户未参与（针对开奖模式，用于 GetUserLotteryResult） */
  NOT_PARTICIPATED = 4,
}

/** 订单状态 */
export enum OrderStatus {
  /** 订单状态预留 */
  ORDER_STATUS_UNKNOWN = 0,
  /** 订单已发货 */
  ORDER_STATUS_DELIVERED = 1,
  /** 订单未发货（等待用户二次交互，如输入区服信息等） */
  ORDER_STATUS_UNDELIVERED = 2,
  /** 订单发货失败 */
  ORDER_STATUS_DELIVERED_FAILED = 3,
  /** 订单发货中（订单在处理过程中尚未落库） */
  ORDER_STATUS_DELIVERING = 4,
}

/** 订单详情 */
export interface OrderDetail {
  /** 订单id */
  orderId: string;
  /** 物品实例id */
  propertyIid: string;
  /** 物品应用到的场景实例id，比如 lottery_iid */
  sceneIid: string;
  /** unsafe_str C端带过来的用户订单额外信息 */
  extend: string;
  /** 虚拟物品返回的发货信息，如cdkey信息 */
  deliveryInfo: { [key: string]: string };
  /** 订单生成时间 */
  orderTs: string;
  /** 订单状态 */
  status: OrderStatus;
  /** 对于填写地址的实物，结构见 PhysicalPropertyAddrCfg，json 格式 */
  userAddrInfo: string;
  /** 发货平台错误码 */
  sendErrorCode: number;
  /** 发货平台错误信息 */
  sendErrorMsg: string;
}

/** 物品基本信息 */
interface InstanceInfo {
  /** 物品实例 id */
  iid: string;
  /** 物品名称 */
  name: string;
  /** 物品描述 */
  desc: string;
  /** 物品图片url */
  picUrl: string;
  /** 物品价值（元） */
  price: number;
  /** 物品h5跳转链接 */
  h5Link: string;
}

/** 物品描述（物品类型数据见无极表：http://wuji.oa.com/p/edit?appid=activity_plat_test&schemaid=t_property_type_info） */
export interface PropertyDesc {
  /** 物品场景 */
  scene: unknown;
  /** 物品类型 id */
  typeId: number;
  /** 物品类型名称 */
  typeName: string;
  /** 物品基础类型（实物 or 虚拟物品） */
  baseType: number;
  /** 物品支持的账户类型 */
  supportAccountType: unknown;
  /** 发货平台 */
  deliveryPlat: {
    /** 拓展信息 */
    extends: string;
    /** 发货平台 ID */
    platId: number;
  };
  /** 物品信息是否依赖外部平台（比如 id 查询、限量读取） */
  isDepsExternalPlat: boolean;
}

/** 倒计时数据 */
interface CountDownData {
  /** 天 */
  day: number;
  /** 小时 */
  hour: number;
  /** 分钟 */
  min: number;
  /** 秒 */
  sec: number;
}

/** 物品过期信息 */
interface PropertyExpirationInfo {
  /** 物品过期类型 */
  type: unknown;
  /** 描述 */
  desc: string;
  /** 过期时间 */
  expirationTime: {
    /** 倒计时过期时间 */
    countDown?: CountDownData;
    /** 绝对过期时间 */
    expirationTs?: string;
  };
  /** 物品过期显示类型 */
  displayType: unknown;
}

/** 实物物品地址配置信息 */
interface PhysicalPropertyAddrCfg {
  /** 字段名称，比如：地址 */
  name: string;
  /** 字段名称，比如：请填写地址 */
  desc: string;
}

/** 用户抽奖结果信息 */
export interface PropertyResult {
  instanceInfo: InstanceInfo;
  propertyDetail: {
    desc: PropertyDesc;
    expireInfo: PropertyExpirationInfo;
    physicalAddrCfg: PhysicalPropertyAddrCfg[];
  };
  orderDetail: OrderDetail;
}

/** 用户抽奖结果返回信息 */
export interface GetUserLotteryResp {
  status: UserLotteryResultStatus;
  propertyResults: PropertyResult[];
}

/** LotteryOnline 抽奖每个奖池配置信息 */
export interface LotteryOnline {
  instanceInfo: unknown;
  properties: {
    property: {
      detail: {
        desc: PropertyDesc;
        expireInfo: PropertyExpirationInfo;
        physicalAddrCfg: PhysicalPropertyAddrCfg[];
      };
      instanceInfo: InstanceInfo;
    };
    /** 抽中概率配置 */
    realTimeConfig: {
      winRate: number;
    };
  }[];
}

/** 应用宝福利礼包 */
export interface YYBGiftInfo {
  /** 福利礼包信息 */
  giftDetail: {
    /** 福利礼包类型 */
    type: number;
  };
}
