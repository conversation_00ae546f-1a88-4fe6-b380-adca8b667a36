import { computed, ref } from 'vue';
import { Consts, Lottery, PropertyReceive, Utils } from '@tencent/moka-athena-next-v2';
import type { PropertyResult, YYBGiftInfo } from '@tencent/moka-ui-domain';
import { camelizeKeys, decamelizeKeys, safeJsonParse } from '@tencent/mole-utils-lib';

import { useActCommonStore } from '../../../store';

/** 解析抽奖物品信息 */
export function useLotteryRewardInfoHook(info?: PropertyResult) {
  const actCommonState = useActCommonStore();
  /** 原始信息 */
  const originInfo = ref<PropertyResult | undefined>(info);

  /** 物品信息 */
  const propertyData = computed(() => originInfo.value?.propertyDetail.desc);
  /** 订单信息 */
  const orderData = computed(() => originInfo.value?.orderDetail);
  /** 发货平台信息 */
  const deliveryPlat = computed(() => propertyData.value?.deliveryPlat);

  /** 是否为虚拟物品 */
  const isVirtual = computed(() => propertyData.value?.baseType === Consts.PROPERTY_BASE_TYPE.VIRTUAL);
  /** 订单状态：用于标识相关物品已发货 */
  const orderStatus = computed(() => orderData.value?.status);
  /** 应用宝礼包 */
  const isYYBGift = computed(() => isVirtual.value && deliveryPlat.value?.platId === Consts.PLAT_ID_TYPE.YYB_GIFT);
  /** 应用宝礼包信息 */
  const yybGiftInfo = computed(() => {
    if (!isYYBGift.value) {
      return;
    }

    // 返回应用宝礼包信息
    const presentInfo = safeJsonParse<YYBGiftInfo>(deliveryPlat.value?.extends ?? '{}');
    return camelizeKeys<YYBGiftInfo>(presentInfo) ;
  });

  /** 礼包类型 */
  const presentType = computed(() => yybGiftInfo.value?.giftDetail?.type);
  /** QB */
  const isQB = computed(() => presentType.value === Consts.PRESENT_TYPE.QB);
  /** 应用宝积分 */
  const isPoint = computed(() => presentType.value === Consts.PRESENT_TYPE.VIP_POINT);
  /** 是否已发货  */
  const isDelivered = computed(() => orderStatus.value === Consts.ORDER_STATUS.DELIVERED);

  /** 处理二次发货逻辑 */
  function handleSecondeRelease(
    successCallback?: () => void | Promise<void>,
    errorCallback?: () => void | Promise<void>,
  ) {
    if (!originInfo.value) {
      console.log('[handleSecondeRelease] 奖励信息为空');
      return;
    }

    // 判断是否为直接发货类型
    if (Utils.isPropertyDirectRelease(presentType.value ?? 0)) {
      console.log('[handleSecondeRelease] 当前奖励无需二次发货', presentType.value);
      return;
    }

    // 领取逻辑
    new PropertyReceive({
      propertyData: {
        instanceInfo: decamelizeKeys(originInfo.value.instanceInfo ?? {}),
        propertyDetail: decamelizeKeys(originInfo.value.propertyDetail ?? {}),
        orderDetail: decamelizeKeys(originInfo.value.orderDetail ?? {}),
      },
      loadingText: '领取中，请稍后~',
      type: Consts.RECEIVE_TYPE.DEFAULT, // 中台组件要传，指定为default
      // 活动
      scene: 'act',
      doReceiveFn: async (params) => {
        const { ext } = params;
        const { orderId } = orderData.value ?? {};

        try {
          // 用户类型
          const userType = await actCommonState.getUserType();
          // 抽奖绑定用户信息并发货
          await Lottery.services.bindUserAdditionalInfo({
            userType,
            orderId,
            additionalInfo: ext,
          });

          void successCallback?.();

          return {
            code: 0,
            msg: '',
            orderId,
            showResult: {
              title: '奖励领取成功~',
              desc: '',
              btnText: '确认',
              orderId,
            },
          };
        } catch (err) {
          console.error('[handleSecondeRelease] 奖励二次发货失败, err:', err);

          void errorCallback?.();

          return {
            code: -1,
            msg: '奖励领取失败',
            orderId,
            showResult: {
              title: '奖励领取失败',
              desc: '',
              btnText: '确认',
            },
          };
        }
      },
    });
  }

  return {
    originInfo,
    orderStatus,
    presentType,
    isDelivered,
    isQB,
    isPoint,
    handleSecondeRelease,
  };
}
