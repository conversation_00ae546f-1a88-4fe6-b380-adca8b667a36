import { DownloaderEntity } from '../entities/downloader';

/** 下载用例 */
export class DownloadUseCase {
  /** 游戏的下载器 */
  private downloader: DownloaderEntity | undefined;

  /**
   * 根据当前应用状态，执行对应动作
   * 下载、打开、继续、暂停
   */
  public async downloadAction(): Promise<void> {
    await this.downloader?.doAction();
  }

  /**
   * 监听下载状态变更
   * @param observer 状态变化监听器
   */
  public observe(observer: Function): void {
    observer();
    throw new Error('Method not implemented.');
  }
}
