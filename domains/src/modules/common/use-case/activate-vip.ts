import { accessRequester } from '@tencent/moka-data-core';

import { ACCESS_KEY, BUSINESS_ID } from '../../../constants/yyb-access';

export async function activateVip(data: {
  /** 启用场景号（默认为空，表示宝内启用 */
  scene_id: string;
  /** 启用场景信息说明 */
  scene_info?: string;
  /** 是否代为轮询 */
  is_long_query?: boolean;
  /** 轮询时长，默认3000ms，可填（0-5000ms） */
  long_query_time_ms?: number;
  /** 轮询时查询启用状态未成功时休眠时长，默认200ms，范围：>=100ms */
  long_query_sleep_time_ms?: number;
}) {
  const resp = await accessRequester.request<typeof data, {ret_code: number;msg: string}>({
    cmd: 'activate_for_h5_with_long_query',
    data,
    businessID: BUSINESS_ID,
    accessKey: ACCESS_KEY,
    needAuthHeader: true,
  });

  if (resp.code !== 0) {
    console.error(`[accessRequest: activateVip] 请求异常，code: ${resp.code}, tip: ${resp.tip}`);
    return;
  }

  return resp.body;
}
