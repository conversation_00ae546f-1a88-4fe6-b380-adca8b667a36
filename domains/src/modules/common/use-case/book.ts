export class BookUseCase {
  /**
   * 预约
   * @param appID 应用 id
   */
  public async book(appID: string) {
    throw new Error(`Method not implemented.${appID}`);
  }

  /**
   * 执行关注公众号逻辑
   * @param actID 活动 id
   */
  public async guideSubscribe(actID: string): Promise<void> {
    throw new Error(`Method not implemented.${actID}`);
  }

  /**
   * 检查应用是否已到首发时间
   * @returns 是否已到达首发时间
   */
  private async checkIsAppReleased(): Promise<boolean> {
    return true;
  }
}
