import { ParamCollect } from '@tencent/moka-athena-next-v2';
import { actExecRequester } from '@tencent/moka-data-core';

import { isTestEnv } from '../../../utils/env';

/** 检查接口返回的条件是否满足 */
export function checkQualifier(result: {
  data?: any | undefined;
  qualifier_result: any;
  server_ts: string;
  qualifierResult: any;
}, needToast = true) {
  let isPass = true;
  let msg = '';
  const { qualifier_result: qualifierResult } = result;

  const { expression_result: expressResult, udf_expr_results: udfExprResults } = qualifierResult;
  if (!expressResult) {
    // 查找第一个不满足条件的提示信息
    const firstFalseExpr = udfExprResults
      .find((item: any) => !item.expression_result && ((item.error_code === 0 && item.udf_result) || item.biz_name.startsWith('idip_')));
    const { customized_error_msg: customizedErrorMsg = '' } = firstFalseExpr || {};
    isPass = false;
    msg = customizedErrorMsg || '条件不通过';
  }

  if (!isPass && needToast) {
    (window as any).magicApp.toast(msg);
  }

  return { isPass, msg };
}

interface ExecParams<T> {
  modID: number;
  componentID: string;
  methodName: string;
  data: T;
}

/**
 * exec 请求和已有助力等组件保持一致 在该方法内部 toast 错误
 * @param config ExecParams
 * return execResult.body?.data
 */
export async function exec<T, U>(config: ExecParams<T>) {
  const execResult = await execReturnWithCode<T, U>(config);
  if (!execResult) return;

  const { code, tip, body } = execResult;
  if (code !== 0) {
    console.error('[exec:错误提示]', `methodName: ${config.methodName}, code: ${code}, tip: ${tip}`);
    (window as any).magicApp.toast(`${tip}${isTestEnv() ? code : ''}`);
    return;
  }

  const { data } = body || {};
  if (!data || !checkQualifier(data).isPass || !data?.data) return;

  return data;
}

/**
 * execReturnWithCode 请求
 * @param config ExecParams
 * return execResult
 */
export async function execReturnWithCode<T, U>(config: ExecParams<T>) {
  const { modID, componentID, methodName, data } = config;

  try {
    const paramCollect = new ParamCollect();
    const [params, isAuth, isCancel] = await paramCollect.getQualifierParamByModId(modID, methodName);

    if (!isAuth || isCancel) {
      return;
    }

    const result = await actExecRequester.request<typeof data, U>({
      componentInfo: { modID: String(modID) },
      componentID,
      invocation: {
        name: methodName,
        data,
      },
      qualifier_params: params as any,
    });
    console.log('[execReturnWithCode:返回数据]', methodName, result);

    if (result.body?.data) {
      const { isPass, msg } = checkQualifier(result.body.data, false);
      if (!isPass) {
        console.log('[execReturnWithCode:条件校验不通过]', methodName, msg);
        return {
          ...result,
          tip: msg || '网络异常，请稍后再试',
        };
      }
    }

    return result;
  } catch (e) {
    console.error('[execReturnWithCode:catch]', methodName, e, config);
  }
}
