import { actExecForwardRequester } from '@tencent/moka-data-core';

/** 激活会员并授权游戏 */
export async function ActivateAndAuthGame(data: {
  /** 活动页面的后台 id */
  activityIID: string;
  /** 授权游戏 appid */
  app_id: number;
  /** 启用场景号（默认为空，表示宝内启用 */
  scene_id: string;
  /** 支持批量游戏授权 */
  appids: number[];
  /** 是否只查询激活授权结果 默认 false */
  is_only_query?: boolean;
  /** 是否授权所有游戏 */
  is_all_app?: boolean;
  /** 等待轮询时间 单位 ms */
  wait_time?: number;
}) {
  const resp = await actExecForwardRequester.request({
    activity_iid: data.activityIID,
    invocation: {
      name: '/trpc.yybgame.welfare_zone.WelareZone/ActivateAndAuthForH5',
      data: {
        ...data,
        is_only_query: data.is_only_query ?? false,
        wait_time: data.wait_time ?? 1000,
      },
    },
  });

  if (resp.code !== 0) {
    console.error(`[accessRequest: activateVipAndAuthGame] 请求异常，code: ${resp.code}, tip: ${resp.tip}`);
    return;
  }

  return resp.body?.data;
}
