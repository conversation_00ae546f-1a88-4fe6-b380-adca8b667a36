import { ActInterfaceName, actOtherRequester, defineStore } from '@tencent/moka-data-core';

/** 活动通用 Store */
export interface ActCommonState {
  /** 用户类型 */
  userType: number;
}

/** Store ID */
const storeID = 'act-common';

export const useActCommonStore = () => {
  const useStore = defineStore({
    id: storeID,
    state: (): ActCommonState => ({
      userType: -1,
    }),
    actions: {
      async getUserType() {
        // 已获取直接返回
        if (this.userType !== -1) return this.userType;

        const { body } = await actOtherRequester.request<{}, { main_account_type: number }>({
          interfaceName: ActInterfaceName.GetActivityConfig,
          reuse: true,
          data: {},
        });

        this.userType = Number(body?.data?.main_account_type) - 1;
        return this.userType;
      },
    },
  }, {
    isolate: false,
  });

  return useStore(storeID);
};
