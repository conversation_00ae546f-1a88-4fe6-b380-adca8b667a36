import { _GettersTree } from 'pinia';
import { defineStore } from '@tencent/moka-data-core';

import { exec, execReturnWithCode } from '../../modules/common/use-case/exec';
import { getBoostId } from '../../utils/url';
import type {
  ActivityConfig,
  DownloadRegisterStatus,
  GetComebackJudgeInfoRsp,
  SendDownloadRegisterPointsReq,
  UserDetail,
  VipInfo,
} from './type';
import { ActivationStatus, FissionMethodName, VisualAngle } from './type';

interface State {
  activityInfo?: UserDetail & { activityConfig?: ActivityConfig };
  vipInfo?: VipInfo;
  launcherInfo?: {
    avatar: string;
    nickname: string;
  };
  myShareKey?: string;
  shareKey?: string;
  visualAngle: VisualAngle;
  downloadRegisterStatus?: DownloadRegisterStatus;
  isReportHeld: boolean;
  getUserDetailOptions: {
    id: string;
    modId: number;
    appId: string;
    boostId: string;
  } | null;
  comebackInfo?: GetComebackJudgeInfoRsp | null;
}

const storeID = 'fissionBoost';

/** 多个裂变助力相关组件共享的数据 */
export const useFissionBoostStore = () => {
  const useStore = defineStore({
    id: storeID,
    state: (): State => ({
      visualAngle: VisualAngle.UNKNOWN,
      isReportHeld: true,
      getUserDetailOptions: null,
      comebackInfo: null,
    }),
    getters: {
      isMaster: (state: State) => !state.shareKey
      || state.visualAngle !== VisualAngle.GUEST
      || state.shareKey === state.myShareKey,
      hasSendToInviter: (state: State) => state.downloadRegisterStatus?.has_get
        && state.downloadRegisterStatus?.has_send,
      hasAuthorized: (state: State) => state.activityInfo?.authorization_info?.has_authroized ?? false,
      hasBind: (state: State) => Boolean(state.activityInfo?.bind_info?.share_key)
      && state.activityInfo?.bind_info?.share_key !== state.myShareKey,
      hasActivatedVip: (state: State) => state.vipInfo?.activation_status === ActivationStatus.ACTIVATE_SCHUSSES,
      totalPoints: (state: State) => Number(state.activityInfo?.point_info?.total_points ?? 0),
      ladderPoints: (state: State) => Number(state.activityInfo?.point_info?.ladder_points ?? 0),
    },
    actions: {
      async getUserDetail(componentID: string, modID: number, appId: string, boostId: string) {
        const isLogin = await (window as any).vipCommonLogic?.checkLogin();
        if (!isLogin) return;
        const userDetail = await getUserDetail(componentID, modID, appId, boostId);
        if (userDetail) {
          this.activityInfo = {
            ...this.activityInfo,
            ...userDetail,
          };

          this.vipInfo = userDetail.vip_info;
        }

        return userDetail;
      },
      async sendDownloadRegisterPoints(componentID: string, modID: number, data: SendDownloadRegisterPointsReq) {
        const result = await sendDownloadRegisterPoints(componentID, modID, data);
        const hasSend = result?.body?.data?.data;

        // 已发放刷新领取状态
        if (hasSend) {
          void this.getDownloadRegisterStatus(componentID, modID);
        }

        return result;
      },
      async getDownloadRegisterStatus(componentID: string, modID: number) {
        this.downloadRegisterStatus = await getDownloadRegisterStatus(componentID, modID, getBoostId(modID));
        // 已绑定助力 领取默认发放成功
        if (this.hasBind) {
          this.downloadRegisterStatus = {
            ...this.downloadRegisterStatus,
            has_send: true,
          };
        }
      },
      async openVipCommonPopup() {
        const isLogin = await (window as any).vipCommonLogic?.checkLogin();
        if (isLogin && this.hasActivatedVip && this.hasAuthorized) return true;

        try {
          await (window as any).vipCommonLogic?.tryLoginAndAuthGameAndActivate();
        } catch (error) {
          console.error('openVipCommonPopup', error);
        } finally {
          if (!this.getUserDetailOptions) return;

          const { id, modId, appId, boostId } = this.getUserDetailOptions;
          await this.getUserDetail(id, modId, appId, boostId);
        }
      },
      async getComebackJudgeInfo(componentID: string, modID: number, boostId: string, pkgName: string) {
        this.comebackInfo = await getComebackJudgeInfo(componentID, modID, boostId, pkgName);
        return this.comebackInfo;
      },
      async getActivityInfo(componentID: string, modID: number, boostId: string) {
        const activityConfig = await getActivityConfig(componentID, modID, boostId);
        this.activityInfo = {
          ...this.activityInfo,
          activityConfig,
        };
      },
    },
  }, {
    isolate: false,
  });
  return useStore(storeID);
};

/** 获取用户详情 */
async function getUserDetail(
  componentID: string,
  modID: number,
  appId: string,
  boostId: string,
) {
  const result = await exec<unknown, UserDetail>({
    componentID,
    modID,
    methodName: FissionMethodName.GetUserDetail,
    data: {
      boost_id: boostId,
      appid: appId,
    },
  });

  return result?.data;
}

/** 客态领下载注册积分主态发放失败助力 */
async function sendDownloadRegisterPoints(
  componentID: string,
  modID: number,
  data: SendDownloadRegisterPointsReq,
) {
  const result = await execReturnWithCode({
    componentID,
    modID,
    methodName: FissionMethodName.SendDownloadRegisterPoints,
    data,
  });

  return result;
}

/** 获取下载注册任务领取状态 */
async function getDownloadRegisterStatus(
  componentID: string,
  modID: number,
  boostId: string,
) {
  const result = await exec<unknown, DownloadRegisterStatus>({
    componentID,
    modID: Number(modID),
    methodName: FissionMethodName.GetDownloadRegisterStatus,
    data: {
      boost_id: boostId,
    },
  });

  return result?.data;
}

/** 获取用户回流信息 */
async function getComebackJudgeInfo(
  componentID: string,
  modID: number,
  boostId: string,
  pkgName: string,
) {
  const result = await exec<unknown, GetComebackJudgeInfoRsp>({
    componentID,
    modID: Number(modID),
    methodName: FissionMethodName.GetComebackJudgeInfo,
    data: {
      boost_id: boostId,
      pkg_name: pkgName,
    },
  });

  return result?.data;
}

/** 获取活动信息 */
async function getActivityConfig(
  componentID: string,
  modID: number,
  boostId: string,
) {
  const result = await exec<unknown, ActivityConfig>({
    componentID,
    modID,
    methodName: FissionMethodName.GetActivityConfig,
    data: {
      boost_id: boostId,
    },
  });

  return result?.data;
}
