export enum VisualAngle {
  UNKNOWN = 0,
  /** 主态 */
  MASTER = 1,
  /** 客态 */
  GUEST = 2,
}

/** 会员启用状态 */
export enum ActivationStatus {
  /** 未启用过 */
  NO_ACTIVATION = 0,
  /** 启用中 */
  ACTIVATING = 1,
  /** 启用成功 */
  ACTIVATE_SCHUSSES = 2,
  /** 启用失败 */
  ACTIVATE_FAILED = 3,
}

/** 会员等级 */
export enum VipLevel {
  LevelOne = 1,
  LevelTwo,
  LevelThree,
  LevelFour,
  LevelFive,
  LevelSix,
  LevelSeven,
  LevelEight,
}

/** 会员信息 */
export interface VipInfo {
  activation_status?: ActivationStatus;
  curr_level?: VipLevel;
}

/** 用户详情 */
export interface UserDetail {
  vip_info?: VipInfo;
  point_info?: {
    total_points?: string;
    total_worth?: string;
    ladder_points?: string;
  };
  authorization_info?: {
    appid?: string;
    has_authroized?: boolean;
  };
  bind_info?: {
    order_id: string;
    share_key: string;
    share_key_avatar: string;
    share_key_nick_name: string;
  };
}

/** 游戏上线阶段 */
export enum GameStage {
  /** 预约期 */
  Reserve = 1,
  /** 预下载期 */
  PreDownload = 2,
  /** 正式上线 */
  Formal = 3,
}

/** 活动配置信息 */
export interface ActivityConfig {
  max_earn_points: string;
  is_open_register: number;
  is_open_comeback: number;
  channel_group: string;
  appid: string;
  game_stage: GameStage;
}

/** 系统类型 */
export enum OSType {
  /** 安卓系统 */
  ANDROID = 1,
  /** IOS系统 */
  IOS = 2,
}

/** 区服信息 */
export interface PartitionInfoType {
  device_type: OSType;
  role_id?: string;
  role_name?: string;
  partition_id?: string;
}

/** 下载注册积分发放请求参数 */
export interface SendDownloadRegisterPointsReq {
  boost_id: string;
  pkg_name: string;
  partition_info: PartitionInfoType;
}

/** 下载注册状态 */
export interface DownloadRegisterStatus {
  /** 是否已为主态发放下载注册积分 */
  has_send?: boolean;
  /** 是否有绑定的主态信息 */
  has_bind_boosted?: boolean;
  /** 是否自己已领取 */
  has_get?: boolean;
}

/** 积分明细弹窗类型 */
export enum DetailPopupType {
  Default,
  Points,
  Boost,
  /** 批量领取物品 */
  Property,
}

/** 助力弹窗类型 */
export enum BoostPopupType {
  Receive,
  Boost,
  Bind,
  Lottery,
}

/** 裂变跨组件事件 */
export enum BoostEvent {
  /** 打开裂变助力明细弹窗 */
  OpenDetailPopup = 'fission-boost:open-detail-popup',
  /** 获取批量组件物品信息 */
  GetFissionBatchObtainInfo = 'fission-obtain:get-fission-batch-obtain-info',
  /** 触发助力弹窗 */
  OpenBoostPopup = 'fission-boost:open-boost-popup',
  /** 拉起裂变分享 */
  OpenFissionShare = 'fission-boost:open-share',
}

export enum FissionMethodName {
  /** 获取用户详情 */
  GetUserDetail = '/trpc.activity.fission_point.FissionPoint/GetUserDetail',
  /** 发放用户注册奖励 */
  SendDownloadRegisterPoints = '/trpc.activity.fission_point.FissionPoint/SendDownloadRegisterPoints',
  /** 获取用户注册奖励状态 */
  GetDownloadRegisterStatus = '/trpc.activity.fission_point.FissionPoint/GetDownloadRegisterStatus',
  /** 获取用户回流信息 */
  GetComebackJudgeInfo = '/trpc.activity.fission_point.FissionPoint/GetComebackJudgeInfo',
  /** 获取活动配置信息 */
  GetActivityConfig = '/trpc.activity.fission_point.FissionPoint/GetActivityInfo',
}

/** 物品实例信息 */
export interface InstanceInfo {
  channel: number;
  desc: string;
  h5_link: string;
  iid: string;
  name: string;
  pic_url: string;
  price: number;
}

/** 物品详情 */
export interface PropertyDetail {
  desc: {
    base_type: number;
    delivery_plat: {
      extends: string;
      plat_id: number;
      property_type_id: number;
      user_runtime_extends: string;
    };
    is_deps_external_plat: boolean;
    scene: number;
    support_account_type: number[];
    type_id: number;
    type_name: string;
  };
  expire_info: {
    count_down: {
      day: number;
      hour: number;
      min: number;
      sec: number;
    };
    desc: string;
    display_type: number;
    type: number;
  };
  physical_addr_cfg: {
    desc: string;
    name: string;
  }[];
}

/** 领取物品信息 */
export interface ObtainPropertyInfo {
  obtain_online: {
    basic_info: {
      instance_info: {
        desc: string;
        name: string;
        obtain_iid: string;
      };
      property: {
        detail: PropertyDetail;
        extend_limit: {
          qq: {
            global: {
              day_limit: number;
              month_limit: number;
              total_limit: number;
              week_limit: number;
            };
            individual: {
              day_limit: number;
              month_limit: number;
              total_limit: number;
              week_limit: number;
            };
          };
          wx: {
            global: {
              day_limit: number;
              month_limit: number;
              total_limit: number;
              week_limit: number;
            };
            individual: {
              day_limit: number;
              month_limit: number;
              total_limit: number;
              week_limit: number;
            };
          };
        };
        instance_info: InstanceInfo;
        limit: {
          global: {
            day_limit: number;
            month_limit: number;
            total_limit: number;
            week_limit: number;
          };
          individual: {
            day_limit: number;
            month_limit: number;
            total_limit: number;
            week_limit: number;
          };
        };
      };
    };
    release_info: {
      display_end_ts: string;
      display_start_ts: string;
      obtain_end_ts: string;
      obtain_start_ts: string;
      release_ts: null;
    };
    status: number;
    user_obtain_status: number;
    user_records: any[];
  };
}

/** 领取状态 */
export enum UserObtainStatus {
  USER_OBTAIN_STATUS_UNKNOWN,
  /** 待领取 */
  USER_OBTAIN_STATUS_TO_BE_COLLECTED,
  /** 已领取（已达到个人限量） */
  USER_OBTAIN_STATUS_RECEIVED,
  /** 已领完（物品已无库存） */
  USER_OBTAIN_STATUS_FINISHED,
}

/** 领取信息 */

export interface ObtainInfo {
  orderId?: string;
  userObtainStatus: UserObtainStatus;
  isPersonalOutOfStock: boolean;
  isGlobalOutOfStock: boolean;
  hasReceiveRecord: boolean;
  property: {
    instanceInfo: InstanceInfo;
    propertyDetail: PropertyDetail;
  };
  isAMS: boolean;
  isQb: boolean;
  isRealGood: boolean;
  isCDK: boolean;
  buttonText: string;
  cdkeyCode?: string;
}

/** 领取信息和物品门槛信息 */
export interface ObtainProperty {
  threshold: string | number;
  obtainID: string;
  obtainInfo?: ObtainInfo;
}

/** 获取回流信息响应 */
export interface GetComebackJudgeInfoRsp {
  /** 是否满足回流条件 */
  is_comeback_condition: boolean;
  /** 是否已注册 */
  is_register: boolean;
  /** 是否活动开始后活跃过 */
  is_comeback_login: boolean;
}
