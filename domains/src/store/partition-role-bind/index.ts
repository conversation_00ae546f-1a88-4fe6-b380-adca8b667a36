import { defineStore, PARTITION_ROLE_STORE_ID } from '@tencent/moka-data-core';
import type { PartitionRoleQQ } from '@tencent/yyb-partition-role-select';

/** 区服 Store 角色信息 */
export interface PartitionRoleStoreState {
  /** 区服角色信息 */
  partitionRolesByOpenIDAndPackageName: Record<string, PartitionRoleQQ>;

  /** 记录配置的区服绑定组件 */
  partitionRoleBindsByPackageName: Record<string, true>;
}

/**
 * 获取用户 key
 * @param packageName 游戏包名
 * @param openID 用户 openID
 * @returns
 */
export function getUserKey(packageName: string, openID: string) {
  return `partition-role-comp.${packageName}.${openID}`;
}

export const usePartitionRoleStore = () => {
  const useStore = defineStore({
    id: PARTITION_ROLE_STORE_ID,
    state: (): PartitionRoleStoreState => ({
      partitionRolesByOpenIDAndPackageName: {},
      partitionRoleBindsByPackageName: {},
    }),
    actions: {
      /**
       * 设置选中的区服角色
       * @param packageName 游戏包名
       * @param openID 用户 openID
       * @param partitionRole 区服角色信息
       */
      setPartitionRole(packageName: string, openID: string, partitionRole: PartitionRoleQQ) {
        this.partitionRolesByOpenIDAndPackageName[getUserKey(packageName, openID)] = partitionRole;
      },
      /**
       * 获取区服和角色
       * @param packageName 游戏包名
       * @param openID 用户 openID
       */
      getPartitionRole(packageName: string, openID: string) {
        return this.partitionRolesByOpenIDAndPackageName[getUserKey(packageName, openID)];
      },
      /**
       * 设置配置的区服绑定组件
       * @param packageName 游戏包名
       */
      setPartitionRoleBind(packageName: string) {
        this.partitionRoleBindsByPackageName[packageName] = true;
      },
    },
  }, {
    isolate: false,
  });

  return useStore(PARTITION_ROLE_STORE_ID);
};
