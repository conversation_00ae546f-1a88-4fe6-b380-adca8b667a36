import { defineStore } from '@tencent/moka-data-core';

/** 应用宝更新相关的 state */
export interface YybUpdateState {
  isVersionValid: boolean;
}

const YYB_UPDATE = 'yybUpdate';

export const useYybUpdateStore = () => {
  const useStore = defineStore({
    id: YYB_UPDATE,
    state: (): YybUpdateState => ({
      isVersionValid: true,
    }),
    actions: {
      checkVersion(minYybVersion: number) {
        const isVersionValid = checkYybVersionGreaterThanVersionCode(minYybVersion);
        this.isVersionValid = isVersionValid;
      },
    },
  }, {
    isolate: false,
  });

  return useStore(YYB_UPDATE);
};

/**
 * 检查应用宝版本是否大于指定版本号
 * @param versionCode 指定版本号
 * @returns 是否符合
 */
function checkYybVersionGreaterThanVersionCode(versionCode: number) {
  const yybVersion = ((/yyb_version\/(\d+)\//i.exec(navigator.userAgent)) || [])[1];
  return parseInt(yybVersion, 10) >= versionCode;
}
