import { camelizeKeys } from 'humps';
import { accessRequester, defineStore } from '@tencent/moka-data-core';
import { utils } from '@tencent/moka-ui-domain';

/** API 路径 */
enum ApiPath {
  GetUserAccount = '/get_user_account',
}

/** 接入层鉴权 */
const BUSINESS_ID = 'yyb_h5';
const ACCESS_KEY = 'yyb_h5_123';

/** 获取用户账号信息响应 */
interface GetUserAccountResp {
  /** 返回码，0表示成功 */
  ret: number;
  /** 返回信息 */
  msg: string;
  /** 用户账号数据，键为场景ID（字符串形式），值为QQ号 */
  data: Record<string, string>;
  /** 默认账号 */
  default?: string;
}

interface AccountResult {
  default?: string;
  data?: Record<string, string>;
}

/**
 * 获取用户账号信息
 * @returns 用户账号信息
 */
async function getUserAccount() {
  const resp = await accessRequester.request({
    cmd: ApiPath.GetUserAccount,
    businessID: BUSINESS_ID,
    accessKey: ACCESS_KEY,
    needAuthHeader: true,
    isTest: utils.isTestEnv(),
    data: {},
  });

  return camelizeKeys<GetUserAccountResp>(resp.body as any);
}

/** QQ 绑定 Store ID */
const QQ_BINDING_STORE_ID = 'qq-binding';

/** QQ 绑定 Store 状态 */
interface QQBindingStoreState {
  /** 账号信息 */
  accountResult: AccountResult | null;
}

export const useQQBindingStore = () => {
  const useStore = defineStore({
    id: QQ_BINDING_STORE_ID,
    state: (): QQBindingStoreState => ({
      accountResult: null,
    }),
    actions: {
      /**
       * 获取用户账号信息
       * @param sceneId QQ绑定场景ID，默认使用天龙场景号
       * @returns 账号信息
       */
      async fetchAccountInfo(sceneId: string) {
        try {
          console.log('[moka-ui-domain] 开始获取用户账号信息...');
          const result = await getUserAccount();
          
          if (!result) {
            console.log('[moka-ui-domain] 获取账号信息失败');
            this.accountResult = null;
            return null;
          }
          
          console.log('[moka-ui-domain] 获取账号信息成功:', result);
          this.accountResult = result;
          
          // 获取指定场景的QQ号
          let qqNumber = '';
          if (result.data?.[sceneId]) {
            console.log(`[moka-ui-domain] 找到场景${sceneId}对应的QQ号:`, result.data[sceneId]);
            qqNumber = result.data[sceneId];
          } else {
            console.log(`[moka-ui-domain] 未找到场景${sceneId}对应的QQ号`);
          }
          
          return {
            accountResult: result,
            qqNumber,
          };
        } catch (err) {
          console.error('[moka-ui-domain] 获取账号信息出错:', err);
          return null;
        }
      },

      /**
       * 获取指定场景的QQ号
       * @param sceneId QQ绑定场景ID，默认使用天龙场景号
       * @returns QQ号
       */
      getQQNumberByScene(sceneId: string): string {
        if (!this.accountResult?.data?.[sceneId]) {
          return '';
        }
        return this.accountResult.data[sceneId];
      },
    },
  }, {
    isolate: false,
  });

  return useStore(QQ_BINDING_STORE_ID);
};
