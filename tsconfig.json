{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    // this enables stricter inference for data properties on `this`
    "strict": true,
    "jsx": "preserve",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "baseUrl": ".",
    "paths": {
      "components/*": [
        "components/*"
      ],
      "common-components/*": [
        "common-components/*"
      ],
      "plugins/*": [
        "plugins/*"
      ],
      "domains/*": [
        "domains/*"
      ],
      "shared/*": [
        "shared/*"
      ]
    },
    "typeRoots": [
      "types/*"
    ],
    "types": [
      "vitest/globals"
    ]
  },
  "include": [
    "**/.entry/*",
    "**/components/**/*",
    "**/common-components/**/*",
    "**/plugins/**/*",
    "**/domains/**/*",
    "**/*.config.js",
    "**/*.config.ts",
    "**/*.d.ts",
    "**/shared/**/*",
  ],
  "exclude": [
    "node_modules"
  ],
}
