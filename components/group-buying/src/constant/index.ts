/** 访问页匠店铺中转页地址 */
export enum ShopTransferURL {
  /** 测试环境 */
  Test = 'https://m-test.iwan.yyb.qq.com/goods-mall/mobile/transfer',
  /** 正式环境 */
  Production = 'https://m.iwan.yyb.qq.com/goods-mall/mobile/transfer',
}

/**
 * 页面参数索引
 */
export enum QueryKey {
  /** 应用宝 appId */
  YybAppId = 'appid',
  /** 商品 ID */
  ProductId = 'product_id',
  /** 拼团 ID */
  GroupId = 'group_id',
  /** 营销玩法模式 */
  Mode = 'mode',
  /** trace ID */
  TraceId = 'trace_id',
  /** 渠道号 */
  ChannelID = 'channel_id',
}

/** 默认头像，兜底无头像场景 */
export const DEFAULT_AVATAR = 'https://cdn.yyb.gtimg.com/wupload/xy/yybtech/QxvaWFRd.png';

/** 编辑器预览数据 */
export const EDITOR_PREVIEW_DATA = {
  group_id: 'ed84bf52',
  start_time: '1747397490',
  end_time: '1747401090',
  left_time: 0,
  group_user: [
    {
      openid: 'E07113D489BD06772CC622CE96844B14',
      icon: 'https://thirdqq.qlogo.cn/g?b=oidb&k=1gGCDz7gmiaQOVuUbyoby3w&kti=aCcrcgv3DKI&s=100&t=1556532120',
      nick_name: 'all-in=true',
      is_master: true,
    },
  ],
  status: 1,
  goods_info: {
    id: 2,
    limit: 3,
    value: 4880,
    goods_info: {
      goods_id: 'IEGAMS',
      goods_name: '张飞-五福同心',
      goods_icon: 'https://midas.gtimg.cn/vmall/SH2019080715651597125986/d92d465ade2324468e738e191b1e1f56.jpg',
      origin_price: 4880,
      price: 4880,
      desc: '',
      discount: 0,
      app_info: {
        app_id: '12127266',
        icon: 'http://pp.myapp.com/ma_icon/0/icon_12127266_1743037529/256',
        name: '王者荣耀',
      },
      bonus: 0,
    },
  },
};

/** 提示文本 */
export const TOAST_TEXT = {
  GroupNotExist: '当前拼团不存在，快去看看其他拼团吧～',
  GroupExpiries: '当前拼团已结束，快去看看其他拼团吧～',
  GroupFulled: '当前拼团已满人，快去看看其他拼团吧～',
};

/** 组件类型 */
export const COMPONENT_TYPE = 'moka-ui-group-buying';

/** 成功状态码 */
export const SUCCESS_CODE = 0;

/** 默认错误信息 */
export const DEFAULT_ERROR_MSG = '系统繁忙，请稍候再试';

/** 暂无可参与拼团 */
export const EMPTY_TEXT = '暂无可参与的拼团订单';

/** 组件内部方法，提供外部调用 */
export const COMPONENT_METHODS = {
  refresh: 'act:component:groupBuying:refresh',
};

/** 组件 MOD_ID */
export const MOE_ID = 'activity_group_buying_card';

/** 组件预览状态 */
export const PREVIEW_STATE_VALUE = {
  Empty: 'empty',
  Default: 'default',
};

/** 默认用户头像 */
export const DEFAULT_USER_ICON = 'https://cms.myapp.com/xy/yybtech/mE7XH3v6.svg';
