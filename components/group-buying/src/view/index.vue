<template>
  <div
    v-if="isInit"
    class="group-buying"
    :class="{
      'group-buying-fit-content': !config.isFixHeight,
    }"
    :style="globalStyle"
  >
    <!-- 拼团订单 -->
    <div
      v-if="!isEmpty && !isShowEmptyInPreview"
      class="group-buying-items"
    >
      <group-order-item
        v-for="(item, productID) in groupBuyingOrdersByProductID"
        :key="productID"
        :order-info="item"
        :product-icon="getDiyProductIcon(productID)"
        :style-config="config"
        :channel-i-d="config.channelID"
        @group-buy="handleJoinGroup"
        @refresh="init"
      />
    </div>
    <div
      v-else
      class="group-buying-items-empty"
    >
      {{ config.emptyText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, ref, watch } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';

import GroupOrderItem from '../components/group-order-item/index.vue';
import { COMPONENT_METHODS, PREVIEW_STATE_VALUE } from '../constant';
import { JoinGroupParams, useGroupBuying } from '../hooks/use-group-buying';
import type { ComponentConfig, ProductConfig } from '../types';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { inMagic } = hooks.useMokaInject();

const isInit = ref(false);

const {
  groupBuyingOrdersByProductID,
  isEmpty,
  initGroupBuying,
  handleGroupBuy,
  initPreview,
} = useGroupBuying(props.config.id);

/** 根据商品 ID 获取商品配置 */
const productConfigsByProductID = computed(() => {
  const { productConfigs } = props.config;
  const configs: Record<string, ProductConfig> = {};
  productConfigs?.forEach((config) => {
    configs[config.id] = config;
  });

  return configs;
});

/** 获取自定义商品图片 */
const getDiyProductIcon = computed(() => (productID: string) => productConfigsByProductID.value[productID]?.picture);

/** 初始化 */
const init = async () => {
  try {
    await initGroupBuying(props.config.productConfigs);
  } catch (err) {
    console.error('[initGroupBuying] 初始化失败', err);
  } finally {
    isInit.value = true;
  }
};

/** 处理参与拼团 */
const handleJoinGroup = (params: JoinGroupParams) => {
  void handleGroupBuy(params);
};

/** 监听配置变化 */
const unWatch = watch(() => props.config.productConfigs, (newValue) => {
  if (!inMagic) {
    unWatch();
    return;
  }

  initPreview(newValue ?? []);
});

/** 是否需要展示空态 */
const isShowEmptyInPreview = computed(() => {
  if (!inMagic) return false;
  return props.config.previewStatusInEditor === PREVIEW_STATE_VALUE.Empty;
});

/** 组件全局样式 */
const globalStyle = computed(() => ({
  backgroundColor: props.config.backgroundColor ?? '',
  color: props.config.textColor ?? '',
}));

defineExpose({
  [COMPONENT_METHODS.refresh]: init,
});

void init();
</script>

<style lang="scss" src="./index.scss" scoped />
