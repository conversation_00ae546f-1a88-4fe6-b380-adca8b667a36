/** 商品配置信息 */
export interface ProductConfig {
  /** 商品 ID */
  id: string;
  /** 商品图片 */
  picture: string;
}

/** 组件配置项 */
export interface ComponentConfig {
  /** 组件 ID */
  id: string;
  /** 商品 ID 列表 */
  productConfigs: ProductConfig[];
  /** 是否需要固定高度 */
  isFixHeight: boolean;
  /** 订单为空文本 */
  emptyText: string;
  /** 全局背景颜色 */
  backgroundColor: string;
  /** 全局文案颜色 */
  textColor: string;
  /** 商品名称颜色 */
  nameColor: string;
  /** 商品卡片背景颜色 */
  cardBgColor: string;
  /** 拼团按钮背景颜色 */
  groupBtnBgColor: string;
  /** 拼团按钮文本颜色 */
  groupBtnTextColor: string;
  /** 预览状态 */
  previewStatusInEditor: string;
  /** 空态用户默认头像 */
  unjoinedPlaceholderIcon: string;
  /** 渠道号 */
  channelID: string;
}

/** 拼团用户信息 */
export interface GroupUser {
  /** 用户 openid */
  openid: string;
  /** 用户头像 */
  icon: string;
  /** 用户名 */
  nickName: string;
  /** 是否是创建者 */
  isMaster: boolean;
}

/** 拼团订单状态 */
export enum GroupOrderStatus {
  /** 默认值,不使用 */
  Default = 0,
  /** 进行中 */
  Processing = 1,
  /** 已完成 */
  Finished = 2,
  /** 已结束 */
  End = 3,
}

/** 游戏信息 */
export interface GameInfo {
  /** 游戏 appId */
  appId: number;
  /** 游戏 ICON */
  icon: string;
  /** 游戏名 */
  name: string;
  /** 包名 */
  packageName: string;
}

/** 拼团商品信息 */
export interface IGroupProduct {
  /** 商品 ID */
  goodsId: string;
  /** 商品名称 */
  goodsName: string;
  /** 图标 */
  goodsIcon: string;
  /** 商品原价，单位分 */
  originPrice: number;
  /** 当前价格，单位分 */
  price: number;
  /** 描述 */
  desc: string;
  /** 折扣 */
  discount: number;
  /** 游戏信息 */
  appInfo: GameInfo;
  /** 奖励积分价值，单位分 */
  bonus: number;
}

/** 拼团商品信息 */
export interface IGroupGoodsInfo {
  /** 拼团基本 ID */
  id: number;
  /** 拼团限制人数 */
  limit: number;
  /** 拼团奖励价值 */
  value: number;
  /** 拼团关联的商品信息 */
  goodsInfo: IGroupProduct;
}

/** 拼团订单 */
export interface IGroupOrderInfo {
  /** 拼团 ID */
  groupId: string;
  /** 拼团开始时间 */
  startTime: string;
  /** 拼团结束时间 */
  endTime: string;
  /** 剩余时间 */
  leftTime: string;
  /** 拼团状态 */
  status: GroupOrderStatus;
  /** 拼团用户 */
  groupUser: GroupUser[];
  /** 拼团基本信息 */
  goodsInfo: IGroupGoodsInfo;
}
