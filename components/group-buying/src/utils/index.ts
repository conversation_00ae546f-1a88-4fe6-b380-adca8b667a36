import { utils } from '@tencent/moka-ui-domain';
import { getCookie, md5 } from '@tencent/mole-utils-lib';

/**
 * 根据 GUID 和商品 ID 生成追踪 ID(traceId)
 * @param productID 商品 ID 字符串
 * @description 没有 guid 会生成 uuid
 * @returns 返回由 GUID 和商品 ID 组合后生成的 MD5 哈希值作为追踪 ID
 */
export const generateTraceID = (productID: string) => {
  const guid = getCookie(document.cookie, 'guid');
  return md5(`${guid || utils.generateUserUUID()}-${productID}`);
};
