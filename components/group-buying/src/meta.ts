import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

import { COMPONENT_METHODS, PREVIEW_STATE_VALUE } from './constant';
/**
 * 组件使用 formily 协议
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        channelID: {
          type: 'string',
          title: '拼团渠道号',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          default: '',
          'x-decorator-props': {
            labelWrap: true,
            labelWidth: 'auto',
          },
        },
        voidProductsConfig: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '商品信息配置',
          },
          properties: {
            productConfigs: {
              type: 'array',
              minItems: 1,
              'x-decorator': 'FormItem',
              'x-component': 'ArrayTable',
              'x-component-props': {
                emptyText: '暂无数据',
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '商品 ID',
                      width: 150,
                    },
                    properties: {
                      id: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          placeholder: '请输入商品 ID',
                        },
                        'x-validator': [
                          { required: true, message: '请输入商品 ID' },
                        ],
                      },
                    },
                  },
                  column2: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '商品图片(未上传默认使用接口返回)',
                    },
                    properties: {
                      picture: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Upload',
                        'x-decorator-props': {
                          labelWrap: true,
                        },
                      },
                    },
                  },
                  column3: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '操作',
                      prop: 'operations',
                      width: 50,
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  title: '新增',
                },
              },
            },
          },
        },
        voidPreviewConfig: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '预览状态配置（编辑器）',
          },
          properties: {
            previewStatusInEditor: {
              type: 'string',
              title: '预览组件状态',
              default: PREVIEW_STATE_VALUE.Default,
              enum: [
                { label: '默认', value: PREVIEW_STATE_VALUE.Default },
                { label: '空态', value: PREVIEW_STATE_VALUE.Empty },
              ],
              'x-decorator': 'FormItem',
              'x-component': 'Radio.Group',
              'x-decorator-props': {
                tooltip: '仅在编辑器中生效',
                labelWrap: true,
                labelWidth: 'auto',
              },
            },
          },
        },
        voidTextConfig: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '组件文案配置',
          },
          properties: {
            emptyText: {
              type: 'string',
              title: '无拼团商品提示文案',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              default: '暂无可参与的拼团订单',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
            },
          },
        },
        voidStyleConfig: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '全局样式配置',
          },
          properties: {
            isFixHeight: {
              type: 'boolean',
              title: '是否需要固定高度',
              default: false,
              'x-decorator-props': {
                labelWidth: 'auto',
                labelWrap: true,
              },
              'x-decorator': 'FormItem',
              'x-component': 'Switch',
            },
            textColor: {
              type: 'string',
              title: '全局文本颜色',
              'x-decorator': 'FormItem',
              'x-component': 'ColorPocker',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
              default: '',
            },
            backgroundColor: {
              type: 'string',
              title: '容器背景颜色',
              'x-decorator': 'FormItem',
              'x-component': 'ColorPocker',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
              default: '',
            },
          },
        },
        voidProductConfig: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '商品样式配置',
          },
          properties: {
            nameColor: {
              type: 'string',
              title: '商品名称颜色',
              'x-decorator': 'FormItem',
              'x-component': 'ColorPocker',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
              default: '',
            },
            cardBgColor: {
              type: 'string',
              title: '卡片背景图颜色',
              'x-decorator': 'FormItem',
              'x-component': 'ColorPocker',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
              default: '',
            },
            groupBtnBgColor: {
              type: 'string',
              title: '拼团按钮背景图颜色',
              'x-decorator': 'FormItem',
              'x-component': 'ColorPocker',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
              default: '',
            },
            groupBtnTextColor: {
              type: 'string',
              title: '拼团按钮文本颜色',
              'x-decorator': 'FormItem',
              'x-component': 'ColorPocker',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
              default: '',
            },
            unjoinedPlaceholderIcon: {
              type: 'string',
              title: '未拼团占位背景图',
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              default: '',
              'x-decorator-props': {
                labelWrap: true,
                labelWidth: 'auto',
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [
      {
        label: '刷新',
        value: COMPONENT_METHODS.refresh,
        desc: '刷新商品对应的拼团订单',
      },
    ],
  },
};

export default meta;
