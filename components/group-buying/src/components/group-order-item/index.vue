<template>
  <div
    dt-eid="card"
    :dt-params="transformObjectToQueryString({
      ...baseReportParams,
    })"
    class="order-item"
    :style="cardBackgroundStyle"
  >
    <!-- 头像 -->
    <div
      class="product-icon"
      :style="productIconStyle"
    />
    <!-- 内容区域 -->
    <div class="order-item__main">
      <div
        class="product-name"
        :style="nameTextStyle"
      >
        {{ productInfo.goodsName }}
      </div>
      <div class="group-users">
        <div
          v-for="user in groupUsers"
          :key="user.openid"
          class="user-icon"
          :style="{
            backgroundImage: `url(${user.icon ? replaceURLProtocol(user.icon) : DEFAULT_AVATAR})`
          }"
        />
        <div
          v-for="index in leftMemberNum"
          :key="index"
          class="user-icon left-user"
          :style="leftUserStyle"
        />
      </div>
    </div>
    <!-- 拼团按钮 -->
    <div class="order-item__operation">
      <div
        dt-eid="button"
        :dt-params="transformObjectToQueryString({
          ...baseReportParams,
          button_title: '一键拼成'
        })"
        class="group-buy-button"
        :style="btnBackgroundStyle"
        @click.stop="handleGroupBuy"
      >
        一键拼成
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant';
import { computed } from 'vue';
import { utils } from '@tencent/moka-ui-domain';
import { addParamsToURL, replaceURLProtocol, transformObjectToQueryString } from '@tencent/mole-utils-lib';

import { DEFAULT_AVATAR, DEFAULT_USER_ICON, MOE_ID, QueryKey, ShopTransferURL } from '../../constant';
import type { GroupUser, IGroupOrderInfo, IGroupProduct } from '../../types';
import { generateTraceID } from '../../utils';

const props = defineProps<{
  orderInfo: IGroupOrderInfo;
  /** 渠道号 */
  channelID: string;
  /** 商品图片 */
  productIcon?: string;
  /** 样式颜色 */
  styleConfig?: {
    /** 商品名称颜色 */
    nameColor: string;
    /** 商品卡片背景颜色 */
    cardBgColor: string;
    /** 拼团按钮背景颜色 */
    groupBtnBgColor: string;
    /** 拼团按钮文本颜色 */
    groupBtnTextColor: string;
    /** 未拼团占位背景图 */
    unjoinedPlaceholderIcon: string;
  };
}>();

const emits = defineEmits(['groupBuy', 'refresh']);

/** 拼团用户 */
const groupUsers = computed<GroupUser[]>(() => {
  const { orderInfo } = props;
  return orderInfo?.groupUser ?? [];
});

/** 剩余可参与拼团人数 */
const leftMemberNum = computed(() => {
  const diff = props.orderInfo.goodsInfo.limit - groupUsers.value.length;
  if (diff <= 0) return 0;
  return diff;
});

/** 商品信息 */
const productInfo = computed<IGroupProduct>(() => {
  const { orderInfo } = props;
  return orderInfo?.goodsInfo?.goodsInfo ?? {};
});

/** 图标样式 */
const productIconStyle = computed(() => {
  const { productIcon } = props;
  // 存在自定义商品图片
  if (props.productIcon) {
    return {
      backgroundImage: `url(${productIcon})`,
    };
  }

  const { goodsIcon } = productInfo.value;
  const icon = goodsIcon ? replaceURLProtocol(goodsIcon) : '';
  return {
    backgroundImage: `url(${icon})`,
  };
});

/** 处理拼团：生成拼团链接 */
const handleGroupBuy = () => {
  const transferURL = utils.isTestEnv() ? ShopTransferURL.Test : ShopTransferURL.Production;
  const productID = productInfo.value.goodsId;
  const { groupId } = props.orderInfo;

  if (!groupId || !productID) {
    console.error(`[handleGroupBuy] 拼团信息不完整, groupId: ${groupId}, productID: ${productID}`);
    showToast('拼团参数异常，请稍后再试');
    emits('refresh');
    return;
  }

  const addParams: Record<string, string | number> = {
    [QueryKey.ProductId]: productID,
    [QueryKey.YybAppId]: productInfo.value.appInfo.appId,
    [QueryKey.TraceId]: generateTraceID(productID),
    [QueryKey.GroupId]: props.orderInfo.groupId,
    [QueryKey.Mode]: 'group',
  };

  if (props.channelID) {
    addParams[QueryKey.ChannelID] = props.channelID;
  }

  const url = addParamsToURL(transferURL, addParams);
  emits('groupBuy', {
    groupID: groupId,
    productID,
    url,
  });
};

/** 基础上报信息 */
const baseReportParams = computed(() => {
  const { orderInfo } = props;
  return {
    mod_id: MOE_ID,
    product_id: orderInfo?.goodsInfo?.goodsInfo?.goodsId ?? '',
    group_buying_progress: `${groupUsers.value.length}/${orderInfo.goodsInfo.limit}`,
  };
});

/** 卡片背景颜色 */
const cardBackgroundStyle = computed(() => ({
  backgroundColor: props.styleConfig?.cardBgColor ?? '',
}));

/** 商品文本颜色 */
const nameTextStyle = computed(() => ({
  color: props.styleConfig?.nameColor ?? '',
}));

/** 按钮背景颜色 */
const btnBackgroundStyle = computed(() => ({
  backgroundColor: props.styleConfig?.groupBtnBgColor ?? '',
  color: props.styleConfig?.groupBtnTextColor ?? '',
}));

/** 剩余未拼团用户样式 */
const leftUserStyle = computed(() => ({
  backgroundImage: `url(${props.styleConfig?.unjoinedPlaceholderIcon || DEFAULT_USER_ICON})`,
}));

</script>

<style lang="scss" scoped>
.order-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  width: 100%;
  height: 0.78rem;
  border-radius: 0.08rem;

  .product-icon {
    width: 0.78rem;
    height: 0.78rem;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    border-radius: 0.08rem;
    margin-right: 0.12rem;
    flex-shrink: 0;
  }

  &__main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    max-width: calc(100% - 1.72rem);
    height: 100%;

    .product-name {
      font-family: Noto Sans CJK SC;
      font-weight: 500;
      font-size: 0.16rem;
      margin-bottom: 0.10rem;
      letter-spacing: 0rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: rgba(0, 0, 0, 1);
    }

    .group-users {
      flex-shrink: 0;
      box-sizing: border-box;
      width: 100%;
      height: 0.32rem;
      padding: 0;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      overflow: auto;

      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }

      .user-icon {
        flex-shrink: 0;
        width: 0.32rem;
        height: 0.32rem;
        border-radius: 0.32rem;
        margin-right: 0.05rem;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
      }

      .left-user {
        background-image: url('https://cms.myapp.com/xy/yybtech/mE7XH3v6.svg');
      }

      .user-icon:last-of-type {
        margin-right: 0;
      }
    }
  }

  &__operation {
    width: 0.64rem;
    height: 100%;
    margin-left: 0.06rem;
    margin-right: 0.12rem;
    display: flex;
    justify-content: center;
    align-items: center;

    .group-buy-button {
      font-family: Noto Sans CJK SC;
      font-weight: 500;
      font-size: 0.12rem;
      letter-spacing: 0%;
      text-align: center;
      width: 0.64rem;
      height: 0.24rem;
      line-height: 0.24rem;
      text-align: center;
      border-radius: 0.12rem;
      background: rgba(238, 60, 56, 1);
      color: rgba(255, 255, 255, 1);
    }
  }
}
</style>
