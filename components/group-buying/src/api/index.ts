import { camelizeKeys } from 'humps';
import { actExecForwardRequester } from '@tencent/moka-data-core';
import { hooks } from '@tencent/moka-ui-domain';
import { safeJsonStringify } from '@tencent/mole-utils-lib';

import { COMPONENT_TYPE, DEFAULT_ERROR_MSG, SUCCESS_CODE } from '../constant';
import type { IGroupOrderInfo } from '../types';

/** 拼团服务名称 */
const GroupServerName = 'trpc.activity.team_server.GroupServer';

enum ApiPath {
  /** 根据商品 ID 获取拼团订单 */
  GetGroupsByGoodID = 'GetGroupsByGoodID',
  /** 根据拼团 ID 获取拼团订单 */
  GetGroupInfoByID = 'GetGroupInfoByID',
}

/** 根据商品 ID 获取拼团订单信息请求体 */
interface GetGroupsByGoodIDReq {
  /** 商品 ID 列表 */
  good_ids: string[];
  /** 是否只需要满足条件的一个 */
  is_need_one: boolean;
}

/** 拼团信息 */
interface Groups {
  groupInfo: IGroupOrderInfo[];
}

/** 根据商品 ID 获取拼团订单信息响应体 */
interface GetGroupsByGoodIDResp {
  /** 商品ID正在进行中的拼团信息 */
  groups: Record<string, Groups>;
}

/** 根据拼团 ID 获取最新拼团信息请求体 */
interface GetGroupInfoByIDReq {
  /** 拼团 ID */
  group_id: string;
}

/** 根据商品 ID 获取拼团订单信息响应体 */
interface GetGroupInfoByIDResp {
  /** 拼团订单信息 */
  group_info: IGroupOrderInfo;
}

/**
 * 根据商品 ID 获取拼团订单信息
 * @param params 请求参数
 */
export async function GetGroupsByGoodIDs(params: {
  goodsIDs: string[];
  componentID: string;
}) {
  const { toast } = hooks.useMokaInject();
  const { activity_iid: backendID } = (window as any).mappingInfo || {};
  const { goodsIDs, componentID } = params;

  try {
    const resp = await actExecForwardRequester.request<GetGroupsByGoodIDReq, GetGroupsByGoodIDResp>({
      componentID,
      activity_iid: backendID,
      componentType: COMPONENT_TYPE,
      invocation: {
        name: `/${GroupServerName}/${ApiPath.GetGroupsByGoodID}`,
        data: {
          good_ids: goodsIDs,
          is_need_one: true,
        },
      },
    });

    const { code, tip, body } = resp;

    const originData = body?.data?.groups;
    if (code !== SUCCESS_CODE || !originData) {
      console.error(`[initGroupBuying] 获取拼团订单信息失败, code: ${code}, msg: ${tip}, data: ${originData ? safeJsonStringify(originData) : ''}`);
      toast?.(tip ?? DEFAULT_ERROR_MSG);
      return;
    }

    const formattedData: Record<string, IGroupOrderInfo> = {};
    Object.keys((originData)).forEach((key) => {
      // 只取第一个订单
      const data = camelizeKeys<Groups>(originData[key]);
      if (!data?.groupInfo?.length) return;

      [formattedData[key]] = data.groupInfo;
    });

    console.log('[initGroupBuying] 获取拼团订单信息成功, formattedData:', formattedData);
    return formattedData;
  } catch (err) {
    console.error(`[initGroupBuying] 获取拼团订单信息失败, err: ${err}`);
    toast?.(DEFAULT_ERROR_MSG);
    return;
  }
}

/**
 * 根据拼团订单 ID 获取拼团订单信息
 * @param params 请求参数
 */
export async function GetGroupInfoByGroupID(params: {
  groupID: string;
  componentID: string;
}) {
  const { toast } = hooks.useMokaInject();
  const { activity_iid: backendID } = (window as any).mappingInfo || {};
  const { groupID, componentID } = params;

  try {
    const resp = await actExecForwardRequester.request<GetGroupInfoByIDReq, GetGroupInfoByIDResp>({
      componentID,
      activity_iid: backendID,
      componentType: COMPONENT_TYPE,
      invocation: {
        name: `/${GroupServerName}/${ApiPath.GetGroupInfoByID}`,
        data: {
          group_id: groupID,
        },
      },
    });

    const { code, tip, body } = resp;
    const originData = body?.data?.group_info;
    if (code !== SUCCESS_CODE || !originData) {
      console.error(`[handleGroupBuy] 获取最新拼团信息失败, code: ${code}, msg: ${tip}, data: ${originData ? safeJsonStringify(originData) : ''}}`);
      toast?.(tip ?? DEFAULT_ERROR_MSG);
      return;
    }

    const formattedData = camelizeKeys<IGroupOrderInfo>(originData);
    console.log('[handleGroupBuy] 获取最新拼团信息成功, value:', formattedData);
    return formattedData;
  } catch (err) {
    console.error(`[handleGroupBuy] 获取最新拼团信息失败, err: ${err}`);
    toast?.(DEFAULT_ERROR_MSG);
    return;
  }
}
