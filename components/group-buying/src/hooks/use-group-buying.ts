import { camelizeKeys } from 'humps';
import { computed, ref } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import { throttle } from '@tencent/mole-utils-lib';

import { GetGroupInfoByGroupID, GetGroupsByGoodIDs } from '../api';
import { EDITOR_PREVIEW_DATA, TOAST_TEXT } from '../constant';
import type { IGroupOrderInfo, ProductConfig } from '../types';

/** 加入拼团参数 */
export interface JoinGroupParams {
  /** 拼团 ID */
  groupID: string;
  /** 商品 ID */
  productID: string;
  /** 链接 */
  url: string;
}

/**
 * 拼团购买相关逻辑
 * @param componentID 组件 ID
 */
export function useGroupBuying(componentID: string) {
  const { isLogin, app, loginReady, inMagic, toast } = hooks.useMokaInject();

  /** 拼团订单 */
  const groupBuyingOrdersByProductID = ref<Record<string, IGroupOrderInfo>>({});
  /** 订单为空 */
  const isEmpty = computed(() => Object.values(groupBuyingOrdersByProductID.value).filter(Boolean).length === 0);

  /**
   * 初始化预览状态
   * @param config 商品配置
   */
  const initPreview = throttle((config: Pick<ProductConfig, 'id'>[]) => {
    const { length } = config;
    // 重置预览状态
    if (!length) {
      groupBuyingOrdersByProductID.value = {};
      return;
    }

    const previewData: Record<string, IGroupOrderInfo> = {};
    Array(length).fill({})
      .forEach((_, index) => {
        const data = camelizeKeys({
          ...EDITOR_PREVIEW_DATA,
          groupId: index,
        }) ;

        previewData[`${config[index].id}`] = data as IGroupOrderInfo;
      }) ;

    groupBuyingOrdersByProductID.value = previewData;
  }, 100);

  /**
   * 初始化
   * @param config 商品配置
   */
  const initGroupBuying = async (config: Pick<ProductConfig, 'id'>[]) => {
    // 编辑器预览状态
    if (inMagic) {
      initPreview(config);
      return;
    }

    // 获取商品 ID，去重
    const productIDs = Array.from(new Set(config?.map(info => info.id).filter(Boolean) ?? []));
    console.log('[initGroupBuying] productIDs:', productIDs);

    if (!productIDs.length) {
      console.log('[initGroupBuying] 拼团商品 ID 为空');
      return;
    }

    const data = await GetGroupsByGoodIDs({
      componentID,
      goodsIDs: productIDs,
    });

    groupBuyingOrdersByProductID.value = data ?? {};
  };

  /**
   * 校验拼团订单信息
   * @param info 商品信息
   */
  const validateGroupInfo = (info?: IGroupOrderInfo) => {
    if (!info) {
      toast?.(TOAST_TEXT.GroupNotExist);
      return false;
    }

    // 拼团已结束
    if (Number(info.leftTime) <= 0) {
      toast?.(TOAST_TEXT.GroupExpiries);
      return false;
    }

    // 拼团已满人
    if (info.groupUser.length >= info.goodsInfo.limit) {
      toast?.(TOAST_TEXT.GroupFulled);
      return false;
    }

    return true;
  };

  /**
   * 拼团购买
   * @param params 拼团购买参数
   */
  const handleGroupBuy = async (params: JoinGroupParams) => {
    if (inMagic) {
      return;
    }

    // 1. 未登录需要先登录
    await loginReady;
    if (!isLogin.value) {
      void app?.$openLogin?.();
      return;
    }

    // 2. 获取订单最新状态
    const { url, groupID } = params;
    const data = await GetGroupInfoByGroupID({
      groupID,
      componentID,
    });

    // 3. 校验订单最新状态
    const isValidate = validateGroupInfo(data);
    if (!isValidate) {
      const ids = Object.keys(groupBuyingOrdersByProductID.value).map(id => ({ id }));
      void initGroupBuying(ids);
      return;
    }

    // 4. 打开新窗口
    utils.openNewWindow(url);
  };

  return {
    groupBuyingOrdersByProductID,
    isEmpty,
    initPreview,
    initGroupBuying,
    handleGroupBuy,
  };
}
