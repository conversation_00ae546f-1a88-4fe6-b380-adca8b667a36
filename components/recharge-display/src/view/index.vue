<template>
  <div :style="textStyle">
    {{ amountWithoutUnit }}
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';

import { getOSType, OS_TYPE_NAME, OSType } from '../utils/os';
import { getUserPayInfo } from '../api';
import { ComponentConfig, DisplayMode } from '../types';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { isLogin, loginReady } = hooks.useMokaInject();
const iosAmount = ref<number>(0);
const androidAmount = ref<number>(0);
const osType = ref<OSType>(getOSType());

// 获取当前平台名称
const platformName = computed(() => OS_TYPE_NAME[osType.value]);

// 根据显示模式计算显示的金额
const currentAmount = computed(() => {
  const displayMode = props.config.displayMode || DisplayMode.ALL;
  console.log('[recharge-display] 显示模式', displayMode);
  // 按照显示模式返回不同的金额
  switch (displayMode) {
    case DisplayMode.ALL:
      return iosAmount.value + androidAmount.value;
    case DisplayMode.ANDROID_ONLY:
      return androidAmount.value;
    case DisplayMode.IOS_ONLY:
      return iosAmount.value;
    case DisplayMode.CURRENT_PLATFORM:
    default:
      // 默认按照当前系统类型显示
      return osType.value === OSType.IOS ? iosAmount.value : androidAmount.value;
  }
});

// 格式化金额，将分转换为元，保留一位小数
const amountWithoutUnit = computed(() => Math.floor((currentAmount.value / 100) * 10) / 10);

// 文字样式
const textStyle = computed(() => ({
  fontSize: `${(props.config.fontSize || 14) / 100}rem`,
  color: props.config.textColor || 'rgba(0, 0, 0, 1)',
}));

// 获取用户充值信息
const fetchUserPayInfo = async () => {
  try {
    await loginReady;

    // 如果用户未登录，直接返回0
    if (!isLogin.value) {
      console.log('[recharge-display] 用户未登录，充值金额默认为0');
      iosAmount.value = 0;
      androidAmount.value = 0;
      return;
    }

    // 获取用户充值信息
    const { pkgName, bizKey, startDate, endDate } = props.config;
    console.log('[recharge-display] 获取用户充值信息', pkgName, bizKey, startDate, endDate);
    const result = await getUserPayInfo(pkgName, bizKey, startDate, endDate);

    // 更新充值金额
    iosAmount.value = result.iosAmount;
    androidAmount.value = result.androidAmount;
    console.log(`[recharge-display] 获取用户充值信息成功: iOS=${result.iosAmount}分, Android=${result.androidAmount}分`);
    console.log(`[recharge-display] 显示模式: ${props.config.displayMode || DisplayMode.ALL}, 显示金额: ${currentAmount.value}分`);
  } catch (error) {
    console.error('[recharge-display] 获取用户充值信息失败:', error);
    iosAmount.value = 0;
    androidAmount.value = 0;
  }
};

// 组件挂载时获取用户充值信息
onMounted(() => {
  void fetchUserPayInfo();
});
</script>
