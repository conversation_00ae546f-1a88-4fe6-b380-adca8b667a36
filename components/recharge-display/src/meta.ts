import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        pkgName: {
          type: 'string',
          title: '应用包名',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入应用包名',
          },
        },
        bizKey: {
          type: 'string',
          title: '业务场景号',
          required: true,
          default: 'boc_pay',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入业务场景号',
          },
        },
        startDate: {
          type: 'number',
          title: '统计开始时间',
          required: true,
          default: 20250101,
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入统计开始时间，格式：20250101',
          },
        },
        endDate: {
          type: 'number',
          title: '统计结束时间',
          required: true,
          default: 20251231,
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入统计结束时间，格式：20251231',
          },
        },
        displayMode: {
          type: 'number',
          title: '显示模式',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          default: 0,
          enum: [
            { label: '安卓+iOS总流水', value: 0 },
            { label: '仅安卓流水', value: 1 },
            { label: '仅iOS流水', value: 2 },
            { label: '根据当前平台显示', value: 3 },
          ],
        },
        fontSize: {
          type: 'number',
          title: '文字大小',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入文字大小',
          },
          default: 14,
        },
        textColor: {
          type: 'string',
          title: '文字颜色',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          'x-decorator-props': {
            labelWrap: true,
          },
          default: 'rgba(0, 0, 0, 1)',
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
