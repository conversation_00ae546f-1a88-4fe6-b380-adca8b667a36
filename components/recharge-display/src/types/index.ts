/** 显示模式 */
export enum DisplayMode {
  /** 安卓+iOS总流水 */
  ALL = 0,
  /** 仅安卓流水 */
  ANDROID_ONLY = 1,
  /** 仅iOS流水 */
  IOS_ONLY = 2,
  /** 根据当前系统类型显示 */
  CURRENT_PLATFORM = 3,
}

/** 组件配置 */
export interface ComponentConfig {
  /** 应用包名 */
  pkgName: string;
  /** 业务场景号 */
  bizKey: string;
  /** 统计开始时间，格式：20250204 */
  startDate: number;
  /** 统计结束时间，格式：20250303 */
  endDate: number;
  /** 显示模式：0-安卓+iOS总流水，1-仅安卓流水，2-仅iOS流水 */
  displayMode?: DisplayMode;
  /** 文字大小 */
  fontSize?: number;
  /** 文字颜色 */
  textColor?: string;
}

/** 游戏付费查询信息 */
export interface GamePayQueryInfo {
  /** 游戏包名 */
  pkg_name: string;
  /** 统计开始时间, eg. 20250204 */
  start_date: number;
  /** 统计结束时间, eg. 20250303 */
  end_date: number;
}

/** 付费统计请求 */
export interface PayQueryReq {
  /** 游戏付费情况 */
  game_pay_guery_info: GamePayQueryInfo[];
  /** 业务场景 ID */
  biz_key: string;
}

/** 付费统计响应 */
export interface PayQueryRsp {
  /** 返回码，0表示成功 */
  ret: number;
  /** 返回信息 */
  msg: string;
  /** iOS 付费结果，key-游戏包名，value-付费统计结果，单位：分，数据实时更新 */
  iosPayAmtMap: Record<string, number>;
  /** 安卓付费结果，key-游戏包名，value-付费统计结果，单位：分，数据离线更新 */
  androidPayAmtMap: Record<string, number>;
}
