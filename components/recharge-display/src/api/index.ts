import { actExecForwardRequester } from '@tencent/moka-data-core';

import type { GamePayQueryInfo, PayQueryReq, PayQueryRsp } from '../types';

/**
 * 获取用户充值信息
 * @param pkgName 游戏包名
 * @param bizKey 业务场景号
 * @param startDate 统计开始时间
 * @param endDate 统计结束时间
 * @returns 用户充值信息（单位：分）
 */
export async function getUserPayInfo(
  pkgName: string,
  bizKey: string,
  startDate: number,
  endDate: number,
): Promise<{ iosAmount: number; androidAmount: number }> {
  try {
    // 获取活动 ID
    const { activity_iid: backendId } = (window as any).mappingInfo || {};

    // 构建请求参数
    const gamePayQueryInfo: GamePayQueryInfo = {
      pkg_name: pkgName,
      start_date: startDate,
      end_date: endDate,
    };

    const requestData: PayQueryReq = {
      game_pay_guery_info: [gamePayQueryInfo],
      biz_key: bizKey,
    };

    // 发送请求
    const resp = await actExecForwardRequester.request({
      activity_iid: backendId,
      invocation: {
        name: '/trpc.data_server.user_feature.UserFeature/GetUserPayInfo',
        data: requestData,
      },
    });

    console.log('[recharge-display] 获取用户充值信息成功', resp);

    // 处理响应
    if (resp.code !== 0 || !resp.body?.data) {
      console.error(`[recharge-display] 获取用户充值信息失败，code: ${resp.code}, tip: ${resp.tip}`);
      return { iosAmount: 0, androidAmount: 0 };
    }

    const data = resp.body.data as PayQueryRsp;

    // 获取 iOS 和安卓的充值金额
    const iosPayAmtMap = data.iosPayAmtMap || {};
    const androidPayAmtMap = data.androidPayAmtMap || {};

    const iosAmount = iosPayAmtMap[pkgName] || 0;
    const androidAmount = androidPayAmtMap[pkgName] || 0;

    console.log(`[recharge-display] iOS充值: ${iosAmount}分, 安卓充值: ${androidAmount}分`);

    return { iosAmount, androidAmount };
  } catch (error) {
    console.error('[recharge-display] 获取用户充值信息出错:', error);
    return { iosAmount: 0, androidAmount: 0 };
  }
}
