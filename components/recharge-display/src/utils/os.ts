/** 系统类型 */
export enum OSType {
  /** 安卓系统 */
  ANDROID = 1,
  /** IOS系统 */
  IOS = 2,
}

/** 系统类型的文案映射 */
export const OS_TYPE_NAME = {
  [OSType.IOS]: '苹果',
  [OSType.ANDROID]: '安卓',
};

/**
 * 获取系统类型
 * 根据 UA 获取系统类型，非 IOS 统一认定为安卓
 * @returns 系统类型
 */
export const getOSType = (): OSType => {
  const ua = navigator.userAgent;
  const isIOS = /\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/.test(ua);
  if (isIOS) {
    return OSType.IOS;
  }

  return OSType.ANDROID;
};
