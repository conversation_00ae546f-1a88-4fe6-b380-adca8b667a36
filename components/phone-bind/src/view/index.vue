<template>
  <button
    v-if="isInit"
    dt-eid="phone-bind"
    :style="buttonStyle"
    :dt-params="utils.getReportParamsStr({
      module_status: maskPhoneNumber ? 1 : 0,
      is_login: utils.getLoginUserOpenID() ? 1 : 0,
    }, inMagic)"
    @click="bindHandle"
  >
    {{ buttonTitle }}
  </button>
</template>
<script setup lang="ts">
import { computed, defineProps, onMounted, ref, toRefs } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import YYBMoleJSBridge from '@tencent/mole-jsbridge/yyb';
import { throttle } from '@tencent/mole-utils-lib';
import { BindScene, PhoneBind } from '@tencent/yyb-phone-bind';

import { type Config, ShowStatus } from '../types/index';

const yybMoleJSBridge = new YYBMoleJSBridge();

const props = defineProps<{
  config: Config;
}>();
const { config } = toRefs(props);

const isInit = ref<boolean>(false);
/** 绑定手机号掩码 */
const maskPhoneNumber = ref<string | undefined>();
/** 是否已绑定活动通知 */
const hasBindInform = ref<boolean>(false);

const curPhoneBind = new PhoneBind({
  /** 业务场景类型 */
  bindScene: BindScene.Activity,
  /** 是否是测试环境 */
  isTest: utils.isTestEnv(),
  /** 强制勾选活动通知 */
  forceInform: config.value.forceInform,
  /** 绑定成功回调（弹窗关闭） */
  onBindSuccess: () => {
    void getBindInfo();
  },
  /** 拉起登录 */
  openLogin: () => {
    openLogin?.();
  },
  /** 业务方日志上报实例 */
  logger: undefined,
});

const { isLogin, toast, inMagic, openLogin } = hooks.useMokaInject();

const buttonStyle = computed(() => {
  if (inMagic) {
    return getStyle(config.value.previewStatus);
  }

  if (!maskPhoneNumber.value
    || (!hasBindInform.value && config.value.forceInform)
  ) {
    return getStyle(ShowStatus.UnBind);
  }

  return getStyle(ShowStatus.Bind);
});

const getStyle = (prefix: string) => ({
  backgroundColor: config.value[`${prefix}BgColor`],
  backgroundImage: `url(${config.value[`${prefix}BgImage`]})`,
  color: config.value[`${prefix}FontColor`],
});

const buttonTitle = computed(() => {
  if (inMagic) {
    return config.value[`${config.value.previewStatus}Title`];
  }

  const { unBindTitle, bindTitle, forceInform } = config.value;

  if (!maskPhoneNumber.value
    || (!hasBindInform.value && forceInform)
  ) {
    return unBindTitle;
  }

  return bindTitle?.replace(/\$phone/g, maskPhoneNumber.value);
});

onMounted(async () => {
  if (inMagic) {
    isInit.value = true;
    return;
  }

  await getBindInfo();
  isInit.value = true;
});

/**
 * 获取绑定信息
 */
const getBindInfo = async () => {
  if (!utils.getLoginUserOpenID()) {
    return;
  }

  const { maskPhone, isAuthorized } = await curPhoneBind.getBindInfo();
  maskPhoneNumber.value = maskPhone;
  hasBindInform.value = !!isAuthorized;
};

/**
 * 绑定按钮事件
 */
const bindHandle = throttle(async () => {
  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  if (yybMoleJSBridge.appEnv !== 'yyb') {
    toast?.('必须在应用宝内访问');
    return;
  }

  if (!maskPhoneNumber.value) {
    void curPhoneBind.doPhoneBind();
    return;
  }

  if (!hasBindInform.value && config.value.forceInform) {
    void curPhoneBind.doForceInform(maskPhoneNumber.value);
    return;
  }

  toast?.('您已绑定');
}, 500);
</script>

<style lang="scss" src="./index.scss" />
