import { type Field } from '@formily/core';
import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 状态文案和背景图联动校验 —— 文案和背景图是必须配一个
 * @param field 字段
 * @param linkageField 联动字段名
 */
const textBgImageValidator = (field: Field, linkageField: string) => {
  const curField = field;
  if (!field.value && !field.query(linkageField).value()) {
    curField.selfErrors = ['文案和背景图是必须配一个'];
  } else {
    curField.selfErrors = [];
  }
};

/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      forceInform: {
        type: 'boolean',
        title: '强制绑定活动通知',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        default: true,
      },
      previewStatus: {
        type: 'number',
        title: '预览组件状态',
        default: 'unBind',
        enum: [
          {
            label: '未绑定',
            value: 'unBind',
          },
          {
            label: '已绑定',
            value: 'bind',
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
      voidUnBindBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '未绑定状态配置',
        },
        properties: {
          unBindTitle: {
            type: 'string',
            title: '按钮文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-reactions': field => textBgImageValidator(field, 'unBindBgImage'),
          },
          unBindBgColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          unBindBgImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-reactions': field => textBgImageValidator(field, 'unBindTitle'),
          },
          unBindFontColor: {
            type: 'string',
            title: '文案颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
      voidBindBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '已绑定状态配置',
        },
        properties: {
          bindTitle: {
            type: 'string',
            title: '按钮文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-reactions': field => textBgImageValidator(field, 'bindBgImage'),
            'x-decorator-props': {
              extra: '$phone 最终会被替换为用户手机掩码',
            },
          },
          bindBgColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          bindBgImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-reactions': field => textBgImageValidator(field, 'bindTitle'),
          },
          bindFontColor: {
            type: 'string',
            title: '文案颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
