/** 组件展示状态 */
export enum ShowStatus {
  /** 未绑定 */
  UnBind = 'unBind',
  /** 已绑定 */
  Bind = 'bind',
}

/** 绑定信息 */
export interface BindInfo {
  /** 绑定手机号 */
  phoneNumber: string;
  /** 是否绑定 */
  isBind: boolean;
}

/** 组件配置信息 */
export interface Config {
  /** 组件id */
  id: string;
  /** 强制绑定活动通知 */
  forceInform: boolean;
  /** 预览状态 */
  previewStatus: ShowStatus;
  [key: string]: string | boolean;
}
