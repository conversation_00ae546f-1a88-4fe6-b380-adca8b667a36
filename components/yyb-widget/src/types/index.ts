/** 组件配置信息 */
export interface Config {
  /** 组件id */
  id: string;

  /** 应用宝游戏appId */
  appId: string;
  /** 活动页返回地址 */
  backJumpUrl?: string;
  /** 预览基础按钮 */
  previewAddedStatus: AddedStatus;

  /** 未添加按钮文案 */
  unAddedTitle: string;
  /** 未添加按钮字体颜色 */
  unAddedFontColor: string;
  /** 未添加按钮背景图 */
  unAddedBgImage: string;
  /** 未添加按钮背景色 */
  unAddedBgColor: string;

  /** 已添加按钮文案 */
  addedTitle: string;
  /** 已添加按钮字体颜色 */
  addedFontColor: string;
  /** 已添加按钮背景图 */
  addedBgImage: string;
  /** 已添加按钮背景色 */
  addedBgColor: string;

  /** 预览弹窗 */
  previewDialog: boolean;

  /** 弹窗背景图 */
  dialogBackgourundImage: string;
  /** 弹窗宽度 */
  dialogWidth: number;
  /** 弹窗高度 */
  dialogHeight: number;

  /** 确认按钮背景图 */
  confirmBackgourundImage: string;
  /** 确认按钮宽度 */
  confirmWidth: number;
  /** 确认按钮高度 */
  confirmHeight: number;

  [key: string]: any;
}

/** 组件添加状态 */
export enum AddedStatus {
  /** 未添加 */
  UnAdded = 'unAdded',
  /** 已添加 */
  Added = 'added',
}
