import type { ComponentConfig, IPublicComponentReportSchema } from '@tencent/moka-schema/dist/component-meta-types';
import { BuiltInReportEvent } from '@tencent/mole-report';

export default function getReportConfig(componentConfig: ComponentConfig): IPublicComponentReportSchema {
  const reportModule = (componentConfig as any).report?.module?._module;
  const baseReportData = {
    _module: reportModule,
    mod_title: reportModule,
    btn_title: reportModule,
  };

  return {
    schema: {
      // 自定义事件
      publicRules: [],
      nodes: [{
        description: 'widget添加弹窗',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: '.yyb-widget-dialog',
          data: {
            ...baseReportData,
            eid: 'yyb-widget-dialog',
          },
        },
      }, {
        description: 'widget添加弹窗按钮',
        rule: {
          events: [BuiltInReportEvent.Exposure, BuiltInReportEvent.Click],
          selector: '.yyb-widget-dialog-confirm-btn',
          data: {
            ...baseReportData,
            button_text: '立即添加',
            eid: 'yyb-widget-dialog-button',
          },
        },
      }],
    },
  };
}
