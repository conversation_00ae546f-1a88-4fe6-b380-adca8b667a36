<template>
  <div
    :dt-cmd="`hold=${holdReport}`"
    :dt-params="`button_text=${buttonTitle}`"
  >
    <button
      class="yyb-widget-button"
      :style="buttonStyle"
      @click="handleOpen"
    >
      {{ buttonTitle }}
    </button>
    <van-dialog
      v-model:show="showDialog"
      class="yyb-widget-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
      z-index="999"
      :style="dialogStyle"
      teleport="body"
    >
      <img
        class="yyb-widget-dialog-close"
        src="https://cdn.yyb.gtimg.com/wupload/xy/yybtech/rVh6XWWH.png"
        @click="close"
      >
      <button
        class="yyb-widget-dialog-confirm-btn"
        :style="confirmButtonStyle"
        @click="handleConfirm"
      />
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import 'vant/es/dialog/style';
import CryptoJS from 'crypto-js';
import Cookies from 'js-cookie';
import { throttle } from 'radash';
import { Dialog as VanDialog } from 'vant';
import { computed, defineProps, onMounted, ref, toRefs, watch } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import MoleJSBridge from '@tencent/mole-jsbridge';

import {
  widgetTaskReport,
} from '../api';
import {
  COMPONENT_EVENT,
  URL_FROM_WIDGET,
  YYB_VERSION_880,
} from '../constant';
import { useResize } from '../hooks/use-dialog-resize';
import {
  AddedStatus,
  type Config,
} from '../types';
import {
  addWidget,
  checkWidget,
  checkYybVersionGreaterThanVersionCode,
  createPromise,
  setUrlParam,
  updateWidget,
} from '../utils';

const { promise, resolve } = createPromise();
const { inMagic, toast } = hooks.useMokaInject();
const { getLoginUserOpenID } = utils;
const moleJSBridge = MoleJSBridge.getJSBridge();

const props = defineProps<{
  config: Config;
}>();
const { config } = toRefs(props);

/** 是否显示弹窗 */
const showDialog = ref(false);
/** 是否添加过 */
const isAdded = ref<boolean>(false);
/** 是否应用宝有效版本 880 */
const isValidVersion = moleJSBridge?.appEnv === 'yyb' && checkYybVersionGreaterThanVersionCode(YYB_VERSION_880);

const holdReport = ref(true);

/** 活动 widget 参数 */
let widgetParams = {
  widgetReqId: config.value.id,
  collect_id: '',
  app_id: config.value.appId,
  activity_iid: '',
  component_type: '',
  component_iid: '',
  jumpUrl: '',
};

const {
  clientHeight,
} = useResize();

const buttonStyle = computed(() => {
  if (inMagic) {
    return getStyle(config.value.previewAddedStatus);
  }

  return isAdded.value ? getStyle(AddedStatus.Added) : getStyle(AddedStatus.UnAdded);
});

const getStyle = (prefix: string) => ({
  backgroundColor: config.value[`${prefix}BgColor`],
  backgroundImage: `url(${config.value[`${prefix}BgImage`]})`,
  color: config.value[`${prefix}FontColor`],
});

const buttonTitle = computed(() => {
  const {
    previewAddedStatus,
    addedTitle,
    unAddedTitle,
  } = config.value;

  if (inMagic) {
    return previewAddedStatus === AddedStatus.Added ? addedTitle : unAddedTitle;
  }

  return isAdded.value ? addedTitle : unAddedTitle;
});

const close = () => {
  showDialog.value = false;
};

const dialogStyle = computed(() => {
  const {
    dialogBackgourundImage,
    dialogWidth,
    dialogHeight,
  } = config.value;

  // 当高度不足时，进行缩放; -50 是避免高度撑满 clientHeight 不美观
  let scaleHeight = 1;
  if (dialogHeight > clientHeight.value - 50) {
    scaleHeight = (clientHeight.value - 50) / dialogHeight;
  }

  return {
    backgroundImage: `url(${dialogBackgourundImage})`,
    width: `${dialogWidth / 100}rem`,
    height: `${dialogHeight / 100}rem`,
    transform: !inMagic ? `translateY(-50%) scale(${scaleHeight})` : 'translateY(-50%)',
    top: '50%',
  };
});

const confirmButtonStyle = computed(() => {
  const {
    confirmBackgourundImage,
    confirmWidth,
    confirmHeight,
  } = config.value;
  return {
    backgroundImage: `url(${confirmBackgourundImage})`,
    width: `${confirmWidth / 100}rem`,
    height: `${confirmHeight / 100}rem`,
  };
});

onMounted(async () => {
  if (inMagic) {
    return;
  }

  if (moleJSBridge?.appEnv !== 'yyb') {
    toast?.('必须在应用宝内访问');
    return;
  }

  if (!isValidVersion) {
    holdReport.value = false;
    return;
  }

  // 等待widget参数更新完成
  await promise;

  // 判断并初始化是否已添加jsbridge
  isAdded.value = await checkWidget({ widgetReqId: config.value.id });
  moleJSBridge?.event?.on('pageAppear', () => {
    (async () => {
      isAdded.value = await checkWidget({ widgetReqId: config.value.id });
    })();
  });

  // 设置guid的md5值，作为widget入口访问的标识
  const curMD5String = CryptoJS.MD5(Cookies.get('guid')).toString(CryptoJS.enc.Hex);
  let newUrl = setUrlParam(location.href, URL_FROM_WIDGET, curMD5String);
  // 补一个 ptag 区别 widget 来源
  newUrl = setUrlParam(newUrl, 'ptag', 'widget');
  // 补充 launchMode=1 launch_flag=1，防止用户通过 widget 同时打开多个活动页
  newUrl = setUrlParam(newUrl, 'launchMode', '1');
  widgetParams.jumpUrl = `tmast://webview?url=${encodeURIComponent(newUrl)}&launch_flag=1${
    config.value.backJumpUrl ? `&back_jump_url=${encodeURIComponent(config.value.backJumpUrl)}` : ''}`;

  const urlObject = new URL(location.href);
  const oldMD5String = urlObject.searchParams.get(URL_FROM_WIDGET);
  const openID = getLoginUserOpenID();
  if (curMD5String === oldMD5String && openID && isAdded.value) {
    void widgetTaskReport(openID);
  }

  holdReport.value = false;
});

if (inMagic) {
  watch(() => props.config.previewDialog, (newValue: boolean) => {
    showDialog.value = newValue;
  }, {
    immediate: true,
  });
}

/** 打开添加弹窗 */
const handleOpen = throttle({ interval: 500 }, async () => {
  if (!isValidVersion) {
    toast?.('当前版本过低，请先更新应用宝哦～');
    return;
  }

  if (isAdded.value) {
    void moleJSBridge?.call('goHomeLauncher');
    return;
  }

  if (!widgetParams.activity_iid) {
    // 说明没有配置事件传递widget参数，告警
    console.error('[yyb-widget] widgetParams error', widgetParams);
    return;
  }

  showDialog.value = true;
});

/** 确认添加 */
const handleConfirm = throttle({ interval: 500 }, async () => {
  const result = await addWidget(widgetParams);
  if (result) {
    toast?.('添加成功');
  }

  showDialog.value = false;
});

defineExpose({
  // 更新玩法组件绑定的模块信息
  [COMPONENT_EVENT.UpdateModInfo]: (_instance: any, data: Record<string, unknown>) => {
    widgetParams = {
      ...widgetParams,
      ...data,
    };

    resolve(true);
  },
  // 集卡卡片数量变化时更新 widget
  [COMPONENT_EVENT.UpdateWidgetInfo]: () => {
    if (isAdded.value && isValidVersion) {
      void updateWidget(widgetParams);
    }
  },
});
</script>
<style lang="scss" scoped>
.yyb-widget-button {
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  white-space: nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
}
</style>
<style lang="scss" src="./index.scss" />
