import { type Field } from '@formily/core';
import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 状态文案和背景图联动校验 —— 文案和背景图必须配一个
 * @param field 字段
 * @param linkageField 联动字段名
 */
const textBgImageValidator = (field: Field, linkageField: string) => {
  const curField = field;
  if (!field.value && !field.query(linkageField).value()) {
    curField.selfErrors = ['文案和背景图必须配一个'];
  } else {
    curField.selfErrors = [];
  }
};

/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      appId: {
        type: 'string',
        title: '应用宝游戏',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'AppSelect',
        'x-component-props': {
          bindType: 'appId',
        },
        'x-validator': [{ required: true, message: '请选择应用宝游戏' }],
      },
      backJumpUrl: {
        type: 'string',
        title: '活动页返回地址',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
      },
      previewAddedStatus: {
        type: 'number',
        title: '预览添加状态',
        default: 'unBook',
        enum: [
          {
            label: '未添加',
            value: 'unAdded',
          },
          {
            label: '已添加',
            value: 'added',
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
      voidUnAddedBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '未添加状态配置',
        },
        properties: {
          unAddedTitle: {
            type: 'string',
            title: '按钮文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-reactions': field => textBgImageValidator(field, 'unAddedBgImage'),
          },
          unAddedFontColor: {
            type: 'string',
            title: '文案颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          unAddedBgImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-reactions': field => textBgImageValidator(field, 'unAddedTitle'),
          },
          unAddedBgColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
      voidAddedBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '已添加状态配置',
        },
        properties: {
          addedTitle: {
            type: 'string',
            title: '按钮文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-reactions': field => textBgImageValidator(field, 'addedBgImage'),
          },
          addedFontColor: {
            type: 'string',
            title: '文案颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          addedBgImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-reactions': field => textBgImageValidator(field, 'addedTitle'),
          },
          addedBgColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
      previewDialog: {
        type: 'boolean',
        title: '预览弹窗效果',
        enum: [
          {
            label: '关闭',
            value: false,
          },
          {
            label: '显示',
            value: true,
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
      voidDialogOptionalBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          title: '弹窗配置',
        },
        properties: {
          dialogBackgourundImage: {
            type: 'string',
            title: '弹窗背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
                widthPath: 'dialogWidth',
                heightPath: 'dialogHeight',
              },
            },
            'x-decorator-props': {
              extra: '上传图片后将按比例锁定弹窗大小：图宽 * 0.5，图高 * 0.5',
            },
            'x-validator': [{ required: true, message: '请上传背景图' }],
          },
          dialogWidth: {
            type: 'number',
            title: '弹窗宽度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          dialogHeight: {
            type: 'number',
            title: '弹窗高度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          confirmBackgourundImage: {
            type: 'string',
            title: '确认按钮背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
                widthPath: 'confirmWidth',
                heightPath: 'confirmHeight',
              },
            },
            'x-decorator-props': {
              extra: '上传图片后将按比例锁定按钮大小：图宽 * 0.5，图高 * 0.5',
            },
            'x-validator': [{ required: true, message: '请上传背景图' }],
          },
          confirmWidth: {
            type: 'number',
            title: '确认按钮宽度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          confirmHeight: {
            type: 'number',
            title: '确认按钮高度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
