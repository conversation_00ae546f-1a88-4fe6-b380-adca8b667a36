import { commonRequester } from '@tencent/moka-data-core';

import { SUCCESS_CODE } from '../constant';

/**
 * 应用宝通用任务上报（链接）
 * @param openID 用户 openID
 */
export async function widgetTaskReport(openID: string) {
  const url = 'https://api.act.yyb.qq.com/user-mission-status/set';

  const res = await commonRequester.request<{
    openid: string;
    actid: string;
    task_key: string;
  }, { msg: string; ret: Boolean }>({
    url,
    method: 'POST',
    data: {
      openid: openID,
      actid: `${(globalThis as any).magicUiconfig[0].actId}`,
      task_key: 'visit_from_widget',
    },
  });
  const { code, body } = res;
  if (code !== SUCCESS_CODE || !body?.ret) {
    console.error('[yyb-widget] widgetTaskReport error', res);
  }
}
