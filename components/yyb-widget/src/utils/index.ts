import MoleJSBridge from '@tencent/mole-jsbridge';

const moleJSBridge = MoleJSBridge.getJSBridge();

/**
 * 检查应用宝版本是否大于指定版本号
 * @param versionCode 指定版本号
 * @returns 是否符合
 */
export function checkYybVersionGreaterThanVersionCode(versionCode: number) {
  const yybVersion = ((/yyb_version\/(\d+)\//i.exec(navigator.userAgent)) || [])[1];
  if (parseInt(yybVersion, 10) >= versionCode) return true;
  return false;
}

/** 通用 promise */
export function createPromise(): {
  promise: Promise<unknown>;
  resolve: (value: unknown) => void;
  reject: () => void;
} {
  let resolveFunction: (value: unknown) => void;
  let rejectFunction: () => void;

  const promise = new Promise((resolve, reject) => {
    resolveFunction = resolve;
    rejectFunction = reject;
  });

  return {
    promise,
    resolve: resolveFunction!,
    reject: rejectFunction!,
  };
}

/**
 * 设置 url 参数
 * @param urlString 指定url字符串
 * @param key 参数key
 * @param value 参数值
 * @returns 新 url
 */
export function setUrlParam(urlString: string, key: string, value: string) {
  const urlObject = new URL(urlString);
  const { searchParams } = urlObject;
  searchParams.set(key, value);
  return urlObject.toString();
}

/**
 * 检查是否已添加 widget
 * @param params 检查是否已添加 widget 参数
 * @returns 是否已添加
 */
export async function checkWidget(params: Record<string, unknown>) {
  const res = await moleJSBridge?.call('checkCollectCardWidget', params);
  const { code, data } = res || {};
  console.log('[yyb-widget] checkCollectCardWidget info', res);
  if (code !== 0) {
    console.error('[yyb-widget] checkCollectCardWidget error', res);
  }

  return !!data;
}

/**
 * 执行添加 widget
 * @param params 执行添加 widget 参数
 * @returns 是否添加成功
 */
export async function addWidget(params: Record<string, unknown>) {
  const res = await moleJSBridge?.call('addCollectCardWidget', params, {
    // 添加依赖系统弹窗等，超时改10s
    timeout: 10000,
  });
  const { code, data } = res || {};
  if (code !== 0) {
    console.error('[yyb-widget] addCollectCardWidget error', res);
  }

  return !!data;
}

/**
 * 更新 widget
 * @param params 更新 widget 参数
 * @returns 是否更新成功
 */
export async function updateWidget(params: Record<string, unknown>) {
  const res = await moleJSBridge?.call('updateCollectCardWidget', params);
  const { code, data } = res || {};
  console.log('[yyb-widget] updateCollectCardWidget info', res);
  if (code !== 0) {
    console.error('[yyb-widget] updateCollectCardWidget error', res);
  }

  return !!data;
}
