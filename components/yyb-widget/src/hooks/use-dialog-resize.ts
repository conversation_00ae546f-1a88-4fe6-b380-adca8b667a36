import { onMounted, onUnmounted, ref } from 'vue';

/**
 * 屏幕尺寸变化
 */
export function useResize() {
  const clientWidth = ref(globalThis.innerWidth);
  const clientHeight = ref(globalThis.innerHeight);

  const listener = () => {
    clientWidth.value = globalThis.innerWidth;
    clientHeight.value = globalThis.innerHeight;
  };

  onMounted(() => {
    globalThis.addEventListener('resize', listener);
  });

  onUnmounted(() => {
    globalThis.removeEventListener('resize', listener);
  });

  return {
    clientWidth,
    clientHeight,
  };
}
