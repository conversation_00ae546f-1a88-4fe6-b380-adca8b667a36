import { AddedStatus } from './types';

export default {
  type: 'moka-ui-yyb-widget',

  style: {
    left: '100',
    width: '120',
    height: '32',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
  },

  appId: '',
  backJumpUrl: '',
  previewAddedStatus: AddedStatus.UnAdded,

  /* 基础按钮的未添加状态配置 */
  unAddedTitle: '去添加',
  unAddedFontColor: '#000',
  unAddedBgImage: '',
  unAddedBgColor: '',

  /* 基础按钮的已添加状态配置 */
  addedTitle: '已添加',
  addedFontColor: '#000',
  addedBgImage: '',
  addedBgColor: '',

  previewDialog: false,

  /* 弹窗基础的相关配置 */
  dialogBackgourundImage: 'https://yyb.qpic.cn/moka-imgs/1725958838205-ows29xh1q8.png',
  dialogWidth: 287,
  dialogHeight: 341,

  /* 确认按钮的相关配置 */
  confirmBackgourundImage: 'https://yyb.qpic.cn/moka-imgs/1724990832367-fgmmgvmibht.png',
  confirmWidth: 167,
  confirmHeight: 41,

  dtEid: 'yyb-widget',
  dtTplID: 1413,
};
