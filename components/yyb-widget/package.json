{"name": "@tencent/moka-ui-yyb-widget", "version": "0.0.3", "description": "活动中台组件模板", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "scripts": {"dev": "vite"}, "author": "junfengchen<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-yyb-widget"], "repository": {"type": "git", "url": "", "directory": "components/yyb-widget"}, "dependencies": {"@tencent/moka-data-core": "^1.0.38", "@tencent/moka-ui-domain": "moka-latest", "@tencent/mole-jsbridge": "^1.0.4", "@tencent/mole-report": "^1.0.0", "crypto-js": "^4.2.0", "js-cookie": "^3.0.5", "radash": "^12.1.0", "vant": "^4.6.8", "vue": "^3.3.4"}, "devDependencies": {"@formily/core": "^2.3.2", "husky": "^8.0.3", "sass": "^1.43.4", "turbo": "^1.5.6"}}