/** 组件配置 */
export interface ComponentConfig {
  /** 业务唯一标识，可配置，默认为'boc_pay' */
  bizId: string;
  /** 限流弹窗 */
  zIndex: string;
  /** 限流弹窗 */
  dialogBg: string;
}

/** 查询用户活动参与数据请求 */
export interface QueryActivityDataRequest {
  /** 业务唯一标识，可配置，默认为'boc_pay' */
  biz_id: string;
  /** 1:曝光,2:点击，不可配置，这个组件设置恒为1 */
  report_type: number;
}

/** 查询用户活动参与数据返回值 */
export interface QueryActivityDataResponse {
  /** 如果 code 为 1101001，则说明限流 */
  code: number;
  /** 异常错误信息 */
  msg: string;
}

/** 上报用户活动参与数据请求 */
export interface ReportActivityDataRequest {
  /** 业务唯一标识，可配置，默认为'boc_pay' */
  biz_id: string;
  /** 1:曝光,2:点击，不可配置，这个组件设置恒为1 */
  report_type: number;
}

/** 上报用户活动参与数据返回值 */
export interface ReportActivityDataResponse {
  /** 返回码，0表示成功 */
  code: number;
  /** 返回信息 */
  msg: string;
}

/** 限流错误码 */
export const LIMIT_ERROR_CODE = 1101001;
export const CONFIG_ERROR_CODE = 1101002;
export const LIMIT_ERROR_CODES = [LIMIT_ERROR_CODE, CONFIG_ERROR_CODE];
