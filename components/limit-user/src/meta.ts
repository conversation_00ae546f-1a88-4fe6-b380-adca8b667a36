import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

import events from './event';

/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        bizId: {
          type: 'string',
          title: '业务唯一标识',
          required: false,
          default: 'boc_pay',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-decorator-props': {
            labelWrap: true,
          },
          description: '业务唯一标识，默认为boc_pay',
        },
        zIndex: {
          type: 'number',
          title: '限流弹窗z-index值',
          required: true,
          default: 1001,
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-decorator-props': {
            labelWrap: true,
          },
          description: '限流弹窗z-index值',
        },
        dialogBg: {
          type: 'string',
          title: '限流背景图',
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-component-props': {
            drag: true,
            lockImgNorm: {
              width: 0.5,
              height: 0.5,
            },
          },
          'x-validator': [
            { required: true, message: '请上传背景图' },
          ],
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: events.events,
    /** 组件内部方法，提供外部调用 */
    methods: events.methods,
  },
};

export default meta;
