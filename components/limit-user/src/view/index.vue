<template>
  <div>
    <div />
    <div
      v-if="isLimitUserDialogVisible"
      :style="{
        zIndex: config.zIndex
      }"
      class="limit-user-dialog-mask"
    >
      <div
        class="limit-user-dialog"
        :style="{
          backgroundImage: `url(${config.dialogBg})`,
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';

import { checkUserLimit, reportUserExposure } from '../api';
import { COMPONENT_EVENT } from '../constant';
import { ComponentConfig } from '../types';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { $bus, loginReady, isLogin, inMagic } = hooks.useMokaInject();
const instance = getCurrentInstance();
const isLimited = ref<boolean>(false);

const isLimitUserDialogVisible = computed(() => isLimited.value);

// 触发事件
const triggerEvent = (eventName: string) => {
  console.log(`[limit-user] 触发事件: ${eventName}`);
  $bus?.$emit(eventName, instance?.proxy);
};

// 处理限流状态逻辑
const handleLimitStatus = async (userIsLimited: boolean) => {
  isLimited.value = userIsLimited;

  if (userIsLimited) {
    // 用户被限流，触发限流事件
    triggerEvent(COMPONENT_EVENT.USER_LIMITED);
  } else {
    // 用户未被限流，上报用户曝光
    const { bizId } = props.config;
    console.log('[handleLimitStatus] 用户未被限流，上报用户曝光', bizId);
    const reportResult = await reportUserExposure(bizId);

    console.log(`[handleLimitStatus] 上报用户曝光${reportResult ? '成功' : '失败'}`);
  }
};

// 检查用户限流状态
const checkUserLimitStatus = async () => {
  await loginReady;
  if (!isLogin.value) {
    return;
  }

  try {
    // 获取用户限流状态
    const { bizId } = props.config;
    console.log('[checkUserLimitStatus] 检查用户限流状态', bizId);
    const result = await checkUserLimit(bizId);

    // 更新限流状态并触发相应事件
    await handleLimitStatus(result);
    console.log(`检查用户限流状态成功: ${result ? '已限流' : '未限流'}`);
  } catch (error) {
    console.error('检查用户限流状态失败:', error);
    await handleLimitStatus(false);
  }
};

// 组件挂载时检查用户限流状态
onMounted(() => {
  if (inMagic) return;
  void checkUserLimitStatus();
});
</script>

<style lang="scss" src="./index.scss" scoped />
