import { actExecForwardRequester } from '@tencent/moka-data-core';

import {
  LIMIT_ERROR_CODES,
  QueryActivityDataRequest,
  QueryActivityDataResponse,
  ReportActivityDataRequest,
  ReportActivityDataResponse,
} from '../types';

/**
 * 上报用户曝光
 * @param bizId 业务唯一标识
 * @returns 上报是否成功
 */
export async function reportUserExposure(bizId = 'boc_pay'): Promise<boolean> {
  try {
    // 获取活动 ID
    const { activity_iid: backendId } = (window as any).mappingInfo || {};

    // 构建请求参数
    const requestData: ReportActivityDataRequest = {
      biz_id: bizId,
      report_type: 1, // 固定为1，表示曝光
    };

    // 发送请求
    const resp = await actExecForwardRequester.request({
      activity_iid: backendId,
      invocation: {
        name: '/trpc.activity_plat.state_storage.StateStorageService/ReportActivityData',
        data: requestData,
      },
    });

    console.log('[limit-user] 上报用户曝光成功', resp);

    // 检查响应状态
    const data = (resp.body?.data || { code: -1, msg: '' }) as ReportActivityDataResponse;
    const isSuccess = data.code === 0;

    if (isSuccess) {
      console.log(`[limit-user] 上报结果: 成功, code: ${data.code}`);
    } else {
      console.error(`[limit-user] 上报结果: 失败, code: ${data.code}`);
    }

    return isSuccess;
  } catch (error) {
    console.error('[limit-user] 上报用户曝光出错:', error);
    return false;
  }
}

/**
 * 查询用户是否被限流
 * @param bizId 业务唯一标识
 * @returns 用户是否被限流
 */
export async function checkUserLimit(bizId = 'boc_pay'): Promise<boolean> {
  try {
    // 获取活动 ID
    const { activity_iid: backendId } = (window as any).mappingInfo || {};

    // 构建请求参数
    const requestData: QueryActivityDataRequest = {
      biz_id: bizId,
      report_type: 1, // 固定为1，表示曝光
    };

    // 发送请求
    const resp = await actExecForwardRequester.request({
      activity_iid: backendId,
      invocation: {
        name: '/trpc.activity_plat.state_storage.StateStorageService/QueryActivityData',
        data: requestData,
      },
    });

    console.log('[limit-user] 查询用户限流状态成功', resp);

    const data = (resp.body?.data || { code: 0, msg: '' }) as QueryActivityDataResponse;

    // 检查是否限流 - 包括正常限流和配置错误的情况
    const isLimited = LIMIT_ERROR_CODES.includes(data.code);
    console.log(`[limit-user] 用户限流状态: ${isLimited ? '已限流' : '未限流'}, code: ${data.code}`);

    return isLimited;
  } catch (error) {
    console.error('[limit-user] 查询用户限流状态出错:', error);
    return false;
  }
}
