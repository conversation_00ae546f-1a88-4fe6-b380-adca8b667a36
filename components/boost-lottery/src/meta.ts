import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 该组件使用 formily 协议
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        lotteryModID: {
          type: 'string',
          title: '模块',
          'x-validator': [{ required: true, message: '请选择抽奖2.0后端模块' }],
          'x-decorator': 'FormItem',
          'x-component': 'ModSelect',
          'x-component-props': {
            modType: 11,
          },
        },
        needBoostedNum: {
          type: 'number',
          title: '参与抽奖需要被助力人数',
          default: 3,
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
        },
        progressText: {
          type: 'string',
          title: '助力进度文案',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-validator': [{ required: true, message: '请输入进度文案' }],
          'x-decorator-props': {
            labelWrap: true,
            extra: '<num> 会替换为当前进度, <total> 会替换为需要参与的助力人数',
          },
          default: '<total>位好友激活会员（<num>/<total>）',
        },
        backgroundColor: {
          type: 'string',
          title: '背景颜色',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          default: 'rgba(255, 243, 216, 1)',
        },
        lockBackground: {
          type: 'string',
          title: '锁定状态背景图',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-validator': [{ required: true, message: '请上传锁定状态背景图' }],
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
        },
        lotteryBackground: {
          type: 'string',
          title: '可抽奖状态背景图',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-validator': [{ required: true, message: '请上传可抽奖状态背景图' }],
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          default: '',
        },
        receivedRewardBackground: {
          type: 'string',
          title: '抽奖完成状态背景图',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          default: '',
        },
        rewardBackground: {
          type: 'string',
          title: '红包图片',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-validator': [{ required: true, message: '请上传红包图片，用于抽奖弹窗展示' }],
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          default: '',
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
