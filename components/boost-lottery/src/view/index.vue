<template>
  <div
    class="boost-lottery"
    dt-eid="boost_red_packet"
    :dt-cmd="`hold=${holdReport}`"
    :dt-params="reportParams"
  >
    <div
      v-if="status !== ComponentStatus.Received"
      class="boost-lottery__red-envelope"
      :style="{
        backgroundImage: `url(${redEnvelopeBgImagesByStatus[status]})`
      }"
      @click="doLottery"
    />
    <div
      v-else
      :style="{
        backgroundImage: `url(${redEnvelopeBgImagesByStatus[status]})`
      }"
      class="boost-lottery__reward"
    >
      <reward
        v-if="!isHandlingDelivery"
        :cover-image="cover"
        :button-text="buttonText"
        :name="name"
        :is-button-disable="isButtonDisable"
        :is-fallback-reward="isFallbackReward"
        @button-click="handleClick"
      />
      <div
        v-else
        class="delivery-handling"
      >
        <div class="delivery-handling__image" />
      </div>
    </div>
    <div class="boost-lottery__progress">
      {{ progressText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, getCurrentInstance, ref, watch } from 'vue';
import { hooks, OrderStatus, type PropertyResult } from '@tencent/moka-ui-domain';
import { camelizeKeys, transformObjectToQueryString } from '@tencent/mole-utils-lib';

import { getUserClickPosAndLotteryResult, saveUserLotteryStatusWithRetry } from '../api';
import { COMPONENT_EVENT, COMPONENT_TYPE, ORDER_STATUS_PRIORITY } from '../constant/index';
import { usePacketRewardHook } from '../hooks/use-packet-reward-hook';
import { type ComponentConfigs, ComponentStatus, type RefreshBoostedStatusParams } from '../types';
import Reward from './components/reward.vue';

const { $bus, toast, isLogin, loginReady, openLogin } = hooks.useMokaInject();
const componentInstance = getCurrentInstance();

const props = defineProps<{
  config: ComponentConfigs;
}>();

const holdReport = ref(true);
const reportParams = computed(() => {
  const { config } = props;
  return transformObjectToQueryString({
    mod_id: 'activity_invite_assist_card',
    rpacket_id: config.lotteryModID,
    rpacket_status: status.value - 1,
    progress: boostedNum.value,
  });
});
/** 组件状态 */
const status = ref(ComponentStatus.Lock);
/** 当前被助力人数 */
const boostedNum = ref(0);
/** 奖励 */
const rewardInfo = ref<PropertyResult>();
const {
  originInfo,
  buttonText,
  orderStatus,
  name,
  cover,
  isButtonDisable,
  isFallbackReward,
  handleBtnClick,
} = usePacketRewardHook(rewardInfo.value);
watch(() => rewardInfo.value, newVal => originInfo.value = newVal);

/** 处理发货状态 */
const isHandlingDelivery = computed(() => status.value === ComponentStatus.Received && !rewardInfo.value);

/** 助力进度文本 */
const progressText = computed(() => {
  const { progressText: text, needBoostedNum } = props.config;
  // 兜底文案
  if (!text) {
    return `${needBoostedNum}位好友激活会员(${boostedNum.value}/${needBoostedNum})`;
  }

  return text.replaceAll('<num>', `${boostedNum.value}`).replaceAll('<total>', `${needBoostedNum}`);
});
/** 红包背景图 */
const redEnvelopeBgImagesByStatus = computed(() => {
  const { lockBackground, lotteryBackground, receivedRewardBackground } = props.config;

  return {
    [ComponentStatus.Lock]: lockBackground,
    [ComponentStatus.Lottery]: lotteryBackground,
    [ComponentStatus.Received]: receivedRewardBackground,
  };
});

/** 刷新助力状态 */
async function refreshBoostedStatus(instance: any, params: RefreshBoostedStatusParams) {
  try {
    console.log('[refreshBoostedStatus]', instance, params);
    const { isSlave, boostedCount } = params || {};
    const { needBoostedNum } = props.config;
    if (isSlave) return;
    boostedNum.value = boostedCount ?? 0;
    if (!needBoostedNum || !boostedCount || boostedCount < needBoostedNum) {
      status.value = ComponentStatus.Lock;
      return;
    }

    // 已中奖无需再获取
    if (status.value === ComponentStatus.Received) {
      return;
    }

    await refreshLotteryStatus();
  } catch (err) {
    console.error('[refreshBoostedStatus] 刷新助力状态失败', err);
  } finally {
    holdReport.value = false;
  }
}

/** 刷新抽奖状态 */
async function refreshLotteryStatus(lotteryResult?: any) {
  try {
    const modID = props.config.lotteryModID;
    const result = await getUserClickPosAndLotteryResult(modID);
    // 奖励信息
    const lotteryReward = result?.propertyResults?.[0] ?? lotteryResult?.originResult?.property_results?.[0];
    console.log('[refreshLotteryStatus] 查询抽奖结果成功', lotteryReward, result?.lotteryIidPos);

    if (lotteryReward) {
      const preStatus = rewardInfo.value?.orderDetail.status ?? OrderStatus.ORDER_STATUS_UNKNOWN;
      // 设置中奖信息和状态
      rewardInfo.value = camelizeKeys(lotteryReward);
      const curStatus = rewardInfo.value?.orderDetail.status ?? OrderStatus.ORDER_STATUS_UNKNOWN;

      // 判断订单发货状态是否需要保留
      if (ORDER_STATUS_PRIORITY[preStatus] > ORDER_STATUS_PRIORITY[curStatus]) {
        // 状态回退
        rewardInfo.value!.orderDetail.status = preStatus;
      }

      status.value = ComponentStatus.Received;
      console.log('[refreshLotteryStatus] 已中奖', result);
      return;
    }

    console.info('[refreshLotteryStatus] 查询中奖物品为空', lotteryResult, result);
    // 存在这个字段表示已抽过奖，可能出现超时等原因没有中奖物品
    if (result?.lotteryIidPos) {
      console.log('[refreshLotteryStatus] 已抽奖但是未返回中奖物品', lotteryReward, result);
      status.value = ComponentStatus.Received;
      return;
    }

    // 二次校验状态
    const { needBoostedNum } = props.config;
    // 达到助力进度
    if (boostedNum.value >= needBoostedNum) {
      console.log('[refreshLotteryStatus] 助力进度已达到，但是未抽奖', lotteryReward, result);
      status.value = ComponentStatus.Lottery;
    }
  } catch (err) {
    console.error('[refreshLotteryStatus]', err);
  }
}

/** 执行抽奖 */
async function doLottery() {
  console.log('[doLottery] boosted-lottery doLottery');
  await loginReady;
  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  if (status.value === ComponentStatus.Lock) {
    toast?.('未满足助力条件，快去邀请好友吧~');
    return;
  }

  if (status.value === ComponentStatus.Lottery) {
    if ((window as any).vipCommonLogic) {
      // 判断是否已激活
      const isActivated = await (window as any).vipCommonLogic.tryActivate?.();
      if (!isActivated) {
        console.log('[boost-lottery] 用户未激活');
        return;
      }

      // 判断是否已授权
      const isAuthed = await (window as any).vipCommonLogic.tryAuthGame?.();
      if (!isAuthed) {
        console.log('[boost-lottery] 用户未授权');
        return;
      }
    }

    console.log('[doLottery] 助力抽奖拉起抽奖弹窗');
    const { id, lotteryModID } = props.config;
    $bus?.$emit(COMPONENT_EVENT.Lottery, componentInstance?.proxy, {
      componentID: id,
      componentType: COMPONENT_TYPE,
      lotteryModID,
      redPacketBg: props.config.rewardBackground,
      point: 0,
      handleLotterySuccess: async (resp: any) => {
        console.log('[handleLotterySuccess] 助力抽奖成功回调', resp);
        const lotterySuccessResult = resp?.originResult?.property_results?.[0];
        const orderID = lotterySuccessResult?.order_detail?.order_id;
        await saveUserLotteryStatusWithRetry(props.config.lotteryModID, orderID, 3);
        void refreshLotteryStatus(resp);
      },
      handleLotteryFailed: () => {
        console.error('[handleLotteryFailed] 助力抽奖失败回调');
        void refreshLotteryStatus();
      },
      handleWXSecondeReceiveSuccess,
      /** 关闭弹窗更新 */
      refreshWhenCloseDialog: () => {
        void refreshLotteryStatus();
      },
    });
  }
}

/** 处理按钮点击 */
function handleClick() {
  handleBtnClick({
    handleWXSecondeReceiveSuccess,
    handleAfterClick: () => {
      // 订单终态不进行刷新处理
      if ([
        OrderStatus.ORDER_STATUS_DELIVERED_FAILED,
        OrderStatus.ORDER_STATUS_DELIVERED,
      ].includes(orderStatus.value ?? OrderStatus.ORDER_STATUS_UNKNOWN)) {
        return;
      }

      void refreshLotteryStatus();
    },
  });
}

/** 处理微信二次领取成功 */
function handleWXSecondeReceiveSuccess(result?: PropertyResult) {
  if (!result) return;
  console.log('[handleWXSecondeReceiveSuccess] 领取成功，刷新抽奖状态', camelizeKeys(result));
  rewardInfo.value = camelizeKeys(result);
  status.value = ComponentStatus.Received;
}

// 组件抛出事件
defineExpose({
  /** 更新助力状态 */
  [COMPONENT_EVENT.RefreshBoosted]: refreshBoostedStatus,
  /** 更新抽奖状态 */
  [COMPONENT_EVENT.RefreshLottery]: refreshLotteryStatus,
});
</script>

<style lang="scss" src="./index.scss" scoped />
