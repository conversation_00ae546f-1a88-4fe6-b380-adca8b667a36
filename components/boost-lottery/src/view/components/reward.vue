<template>
  <div class="reward">
    <template v-if="!isFallbackReward">
      <div
        class="reward-cover"
        :style="{
          backgroundImage: `url(${coverImage})`
        }"
      />
      <div class="reward-name">
        {{ name }}
      </div>
      <div
        class="reward-button"
        :class="{ disable: isButtonDisable }"
        @click="handleBtnClick"
      >
        {{ buttonText }}
      </div>
    </template>
    <template v-else>
      <div class="reward-fallback">
        新春大吉
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  coverImage?: string;
  name?: string;
  isButtonDisable?: boolean;
  buttonText?: string;
  isFallbackReward?: boolean;
}>();

const emits = defineEmits<(e: 'buttonClick') => void>();

const handleBtnClick = () => {
  emits('buttonClick');
};

</script>

<style lang="scss" scoped>
.reward {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  &-cover {
    width: 0.29rem;
    height: 0.29rem;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
  }

  &-name {
    width: 85%;
    font-family: Noto Sans SC;
    font-size: 0.12rem;
    font-weight: 500;
    line-height: 0.1738rem;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgba(112, 45, 0, 1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &-button {
    font-family: Noto Sans SC;
    font-size: 0.1rem;
    font-weight: 500;
    line-height: 0.1448rem;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgba(199, 39, 0, 1);
  }

  &-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Noto Sans SC;
    font-size: 0.12rem;
    font-weight: 500;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgba(112, 45, 0, 1);
  }
}
</style>
