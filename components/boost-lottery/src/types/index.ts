/** 组件配置 */
export interface ComponentConfigs {
  /** 组件 ID */
  id: string;
  /** 后端模块 */
  lotteryModID: string;
  /** 参与抽奖需要被助力人数 */
  needBoostedNum: number;
  /** 兜底背景图 */
  backgroundImage?: string;
  /** 锁定状态背景图 */
  lockBackground?: string;
  /** 解锁状态背景图 */
  lotteryBackground?: string;
  /** 抽奖完成状态背景图 */
  receivedRewardBackground?: string;
  /** 红包图 */
  rewardBackground: string;
  /** 描述信息 */
  progressText?: string;
}

/** 组件状态 */
export enum ComponentStatus {
  /** 未达到助力进度 */
  Lock = 1,
  /** 达到助力进度 */
  Lottery = 2,
  /** 已抽奖状态 */
  Received = 3,
}

/** 刷新助力状态 */
export interface RefreshBoostedStatusParams {
  /** 是否为客态 */
  isSlave: boolean;
  /** 助力进度 */
  boostedCount: number;
}

/** 奖励类型 */
export enum RewardType {
  /** 其他 */
  Other = 0,
  /** 积分 */
  Point = 1,
  /** Q 币 */
  QB = 2,
}

/** 发货平台信息 */
export interface DeliveryPlat {
  /** 关联的福利平台物品信息 */
  extends: string;
}

/** 礼包信息 */
export enum GiftType {
  /** QB */
  QB = 4,
  /** 会员积分 */
  Point = 14,
}

/** 物品关联的拓展信息 */
export interface ExtendInfo {
  giftDetail: {
    type: GiftType;
  };
}
