import { actExecForwardRequester, actUtils } from '@tencent/moka-data-core';
import type { GetUserLotteryResp } from '@tencent/moka-ui-domain';
import { camelizeKeys, sleep } from '@tencent/mole-utils-lib';

/** 请求接口返回 */
export interface GetUserClickPosAndLotteryResult extends GetUserLotteryResp {
  /** 抽奖位置信息 */
  lotteryIidPos: string;
}

/**
 * 通过抽奖 ID 获取用户抽奖结果位置信息
 * @param modId 抽奖模块 ID
 * @returns 抽奖模块 ID 对应的抽奖结果位置信息
 */
export async function getUserClickPosAndLotteryResult(modId: string):
Promise<GetUserClickPosAndLotteryResult | undefined> {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const resp = await actExecForwardRequester.request({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/GetUserClickPosAndLotteryResult',
      data: {
        lottery_iid: actUtils.getCIIDByModID({ modID: modId }),
      },
    },
  });

  if (resp.code !== 0 || !resp.body?.data) {
    console.error(`[getUserClickPosAndLotteryResult] 获取用户抽奖位置信息失败，code: ${resp.code}, tip: ${resp.tip}`);
    return;
  }

  return camelizeKeys(resp.body.data) ;
}

/**
 * 保存抽奖位置相关信息
 * @param modID 抽奖模块 ID
 */
export async function saveUserLotteryStatus(modID: string, orderID: string) {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const lotteryID = actUtils.getCIIDByModID({ modID });

  console.log(`[saveUserLotteryStatus] 保存开始，lotteryID: ${lotteryID}`);
  const resp = await actExecForwardRequester.request({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/SaveUserClickPos',
      data: {
        // 助力抽奖仅涉及一个，终端和前端判断均为不为空字符串表示已抽奖，格式与红包墙保持一致
        lottery_iid_pos: { [lotteryID]: `${orderID ?? 'true'},,` },
      },
    },
  });
  console.log('[saveUserLotteryStatus] 保存成功，resp:', resp);

  if (resp.code !== 0) {
    console.error(`[saveUserLotteryStatus] 保存失败，code: ${resp.code}, tip: ${resp.tip}`);
    return false;
  }

  return true;
}

/**
 * 保存抽奖位置相关信息
 * @param modID 抽奖模块 ID
 * @param orderID 订单 ID
 * @param retryTimes 重试次数
 */
export async function saveUserLotteryStatusWithRetry(
  modID: string,
  orderID: string,
  retryTimes = 0,
) {
  const res = await saveUserLotteryStatus(modID, orderID);
  if (res) {
    return;
  }

  let retryCount = 0;
  while (retryCount < retryTimes) {
    await sleep(100);
    const retryRes = await saveUserLotteryStatus(modID, orderID);
    retryCount += 1;
    if (retryRes) {
      return;
    }
  }
}
