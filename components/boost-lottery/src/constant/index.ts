import { OrderStatus } from '@tencent/moka-ui-domain';

/** 组件内抛出事件 */
export const COMPONENT_EVENT = {
  /** 刷新助力状态 */
  RefreshBoosted: 'refreshBoosted',
  /** 抽奖 */
  Lottery: 'lottery',
  /** 刷新抽奖状态 */
  RefreshLottery: 'refreshLottery',
  /** 锚定至积分 */
  NeedScrollToPointExchange: 'needScrollToPointExchange',
};

export const COMPONENT_TYPE = 'moka-ui-boost-lottery';

/** 订单发货中状态提示文案 */
export const ORDER_DELIVERING_TIP = '【领取成功】到账可能存在延迟，稍后可前往QQ钱包查看';

/** 订单发货失败状态提示文案 */
export const ORDER_DELIVER_FAILED_TIP = '【发货异常】 您可通过页面【活动规则］联系客服协助处理';

/** 订单状态优先级，用于判断订单状态不能回滚 */
export const ORDER_STATUS_PRIORITY = {
  [OrderStatus.ORDER_STATUS_UNKNOWN]: 0,
  [OrderStatus.ORDER_STATUS_UNDELIVERED]: 1,
  [OrderStatus.ORDER_STATUS_DELIVERING]: 2,
  [OrderStatus.ORDER_STATUS_DELIVERED]: 3,
  [OrderStatus.ORDER_STATUS_DELIVERED_FAILED]: 3,
};
