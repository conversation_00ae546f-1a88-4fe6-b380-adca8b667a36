import { showToast } from 'vant';
import { computed, getCurrentInstance } from 'vue';
import type { PropertyResult } from '@tencent/moka-ui-domain';
import { hooks, lottery, OrderStatus, utils } from '@tencent/moka-ui-domain';

import { COMPONENT_EVENT, ORDER_DELIVER_FAILED_TIP, ORDER_DELIVERING_TIP } from '../constant';

const { useLotteryRewardInfoHook } = lottery;

/** 根据订单状态展示文本 */
const textByOrderStatus: Record<number, string | undefined> = {
  [OrderStatus.ORDER_STATUS_DELIVERED]: '已领取',
  [OrderStatus.ORDER_STATUS_DELIVERED_FAILED]: '发货失败',
  [OrderStatus.ORDER_STATUS_DELIVERING]: '发货中',
};

export function usePacketRewardHook(info?: PropertyResult) {
  const { isQB, originInfo, isPoint, orderStatus, handleSecondeRelease, isDelivered } = useLotteryRewardInfoHook(info);

  const { $bus } = hooks.useMokaInject();
  const componentInstance = getCurrentInstance();

  /** 名称 */
  const name = computed(() => originInfo.value?.instanceInfo.name);
  /** 图片 */
  const cover = computed(() => originInfo.value?.instanceInfo.picUrl);
  /** 按钮文本 */
  const buttonText = computed(() => {
    // 积分商品
    if (isPoint.value) {
      return orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED_FAILED ? '发货失败' : '去兑换';
    }

    // QB 商品
    if (isQB.value) {
      return textByOrderStatus[orderStatus.value ?? OrderStatus.ORDER_STATUS_UNKNOWN] ?? '去领取';
    }

    return '';
  });
  /** 是否为兜底奖励 */
  const isFallbackReward = computed(() => !isQB.value && !isPoint.value);
  /** 是否展示领取按钮禁用状态 */
  const isButtonDisable = computed(() => {
    if (isQB.value && isDelivered.value) return true;
    return false;
  });

  /** 处理点击 */
  const handleBtnClick = (params?: {
    handleWXSecondeReceiveSuccess?: (result?: PropertyResult) => (Promise<void> | void);
    handleWXSecondeReceiveFailed?: (result?: PropertyResult) => (Promise<void> | void);
    handleAfterClick?: () => Promise<void> | void;
  }) => {
    if (isFallbackReward.value) {
      console.log('[handleBtnClick] 兜底奖励', originInfo.value);
      return;
    }

    // 积分奖励领取
    if (isPoint.value) {
      console.log('[handleBtnClick] 积分奖励', originInfo.value);
      handlePointRewardBtnClick();
      return;
    }

    // QB 奖励领取
    if (isQB.value) {
      console.log('[handleBtnClick] QB 奖励', originInfo.value);
      handleQBRewardBtnClick(params);
      return;
    }
  };

  /** 处理 QB 奖励领取 */
  const handleQBRewardBtnClick = (params?: {
    handleWXSecondeReceiveSuccess?: (result?: PropertyResult) => (Promise<void> | void);
    handleWXSecondeReceiveFailed?: (result?: PropertyResult) => (Promise<void> | void);
    handleAfterClick?: () => Promise<void> | void;
  }) => {
    const {
      handleWXSecondeReceiveSuccess,
      handleWXSecondeReceiveFailed,
      handleAfterClick,
    } = params || {};

    // 已发货状态，按钮状态展示为已领取禁用状态
    if (orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED) {
      console.log('[handleQBBtnClick] 已发货', originInfo.value);
      void handleAfterClick?.();
      return;
    }

    // 发货失败状态
    if (orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED_FAILED) {
      console.log('[handleQBBtnClick] 发货失败', originInfo.value);
      void handleAfterClick?.();
      showToast({
        message: ORDER_DELIVER_FAILED_TIP,
        zIndex: 1000,
      });

      return;
    }

    // eslint-disable-next-line @typescript-eslint/naming-convention
    const { getLoginType, ELoginType } = utils;
    const isWxLogin = getLoginType() === ELoginType.Wx;

    // 非微信登录或发货中直接提示发货中状态
    if (!isWxLogin || orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERING) {
      console.log('[handleQBBtnClick] 发货中', originInfo.value);
      void handleAfterClick?.();
      showToast({
        message: ORDER_DELIVERING_TIP,
        zIndex: 1000,
      });

      return;
    }

    // 微信登录需要填写 QQ 号
    handleSecondeRelease(
      // 成功回调
      () => {
        if (!originInfo.value) return;
        // 手动设置为发货中状态
        originInfo.value.orderDetail.status = OrderStatus.ORDER_STATUS_DELIVERING;
        showToast({
          message: ORDER_DELIVERING_TIP,
          zIndex: 1000,
        });

        // 输入 QQ 号成功后需要更新订单状态
        void handleWXSecondeReceiveSuccess?.(originInfo.value);
        void handleAfterClick?.();
      },
      // 失败回调
      () => {
        void handleWXSecondeReceiveFailed?.(originInfo.value);
        void handleAfterClick?.();
      },
    );
  };

  /** 处理积分奖励领取 */
  const handlePointRewardBtnClick = () => {
    // 发货失败提示
    if (orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED_FAILED) {
      console.error('[handlePointRewardBtnClick] 发货失败状态', originInfo.value);
      showToast({
        message: ORDER_DELIVER_FAILED_TIP,
        zIndex: 1000,
      });

      return;
    }

    $bus?.$emit(COMPONENT_EVENT.NeedScrollToPointExchange, componentInstance?.proxy);
  };

  return {
    originInfo,
    name,
    cover,
    buttonText,
    isButtonDisable,
    orderStatus,
    isFallbackReward,
    handleBtnClick,
  };
}
