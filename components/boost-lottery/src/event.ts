
import { COMPONENT_EVENT } from './constant/index';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [
    {
      label: '执行抽奖',
      value: COMPONENT_EVENT.Lottery,
      desc: '',
    },
    {
      label: '积分兑换-锚定',
      value: COMPONENT_EVENT.NeedScrollToPointExchange,
      desc: '积分兑换-锚定',
    },
  ],
  /** 组件内部方法，提供外部调用 */
  methods: [
    {
      label: '更新助力进度',
      value: COMPONENT_EVENT.RefreshBoosted,
      desc: '',
    },
    {
      label: '更新抽奖进度',
      value: COMPONENT_EVENT.RefreshLottery,
      desc: '',
    },
  ],
};
