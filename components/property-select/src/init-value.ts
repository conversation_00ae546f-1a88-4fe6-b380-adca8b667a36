export default {
  type: 'moka-ui-property-select',

  previewDialog: false,

  style: {
    left: '100',
    width: '120',
    height: '32',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
  },

  selectedKey: '',

  /* 选中展示的相关配置 */
  text: '我的物品选择：$selected',
  textColor: '',
  backgroundImage: '',
  backgroundColor: '',

  /* 弹窗基础的相关配置 */
  dialogBackgourundImage: 'https://yyb.qpic.cn/moka-imgs/1724994478849-hxaau61nkzf.png',
  dialogWidth: 317,
  dialogHeight: 434,

  propertysMarginTop: 67,

  /* 确认按钮的相关配置 */
  confirmBackgourundImage: 'https://yyb.qpic.cn/moka-imgs/1724990832367-fgmmgvmibht.png',
  confirmWidth: 167,
  confirmHeight: 41,
  confirmUnselectedTips: '请先选择物品哦',

  propertys: [],

  dtEid: 'property-select',
  dtTplID: 1411,
};
