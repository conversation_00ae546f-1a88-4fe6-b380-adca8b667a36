import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      selectKey: {
        type: 'string',
        title: '物品选中key',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-decorator-props': {
          labelWrap: true,
          tooltip: '用于记录用户的选中物品；允许自定义key，方便多个场景共用',
        },
        'x-component-props': {
          placeholder: '选填，默认是{活动id_组件id}',
        },
      },
      voidSelectedOptionalBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          title: '选中展示配置',
        },
        properties: {
          text: {
            type: 'string',
            title: '文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-decorator-props': {
              extra: '$selected 实际会被替换为用户选中的物品名称',
            },
            'x-validator': [{ required: true, message: '请输入文案' }],
          },
          textColor: {
            type: 'string',
            title: '字体颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          backgroundImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
          },
          backgroundColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
      previewDialog: {
        type: 'boolean',
        title: '预览弹窗效果',
        enum: [
          {
            label: '关闭',
            value: false,
          },
          {
            label: '显示',
            value: true,
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
      voidDialogOptionalBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          title: '弹窗配置',
        },
        properties: {
          dialogBackgourundImage: {
            type: 'string',
            title: '弹窗背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
                widthPath: 'dialogWidth',
                heightPath: 'dialogHeight',
              },
            },
            'x-decorator-props': {
              extra: '上传图片后将按比例锁定弹窗大小：图宽 * 0.5，图高 * 0.5',
            },
            'x-validator': [{ required: true, message: '请上传背景图' }],
          },
          dialogWidth: {
            type: 'number',
            title: '弹窗宽度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          dialogHeight: {
            type: 'number',
            title: '弹窗高度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          propertysMarginTop: {
            type: 'number',
            title: '卡片列表顶部距离',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-validator': [{ required: true, message: '请输入顶部距离' }],
          },
          confirmBackgourundImage: {
            type: 'string',
            title: '确认按钮背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
                widthPath: 'confirmWidth',
                heightPath: 'confirmHeight',
              },
            },
            'x-decorator-props': {
              extra: '上传图片后将按比例锁定按钮大小：图宽 * 0.5，图高 * 0.5',
            },
            'x-validator': [{ required: true, message: '请上传背景图' }],
          },
          confirmWidth: {
            type: 'number',
            title: '确认按钮宽度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          confirmHeight: {
            type: 'number',
            title: '确认按钮高度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          confirmUnselectedTips: {
            type: 'string',
            title: '未选中Tips',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-validator': [{ required: true, message: '请输入未选中Tips' }],
          },
        },
      },

      voidPropertyOptionalBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          title: '物品配置',
        },
        properties: {
          propertys: {
            type: 'array',
            'x-decorator': 'FormItem',
            'x-component': 'ArrayTable',
            'x-component-props': {
              emptyText: '暂无数据',
            },
            items: {
              type: 'object',
              properties: {
                column1: {
                  type: 'void',
                  'x-component': 'ArrayTable.Column',
                  'x-component-props': {
                    title: '名称',
                    align: 'center',
                    width: 100,
                  },
                  properties: {
                    name: {
                      type: 'string',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      'x-validator': [{ required: true, message: '请输入物品名称' }],
                    },
                  },
                },
                column2: {
                  type: 'void',
                  'x-component': 'ArrayTable.Column',
                  'x-component-props': {
                    title: '封面图',
                    align: 'center',
                  },
                  properties: {
                    cover: {
                      type: 'string',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      'x-validator': [{ required: true, message: '请上传物品封面图' }],
                    },
                  },
                },
                column3: {
                  type: 'void',
                  'x-component': 'ArrayTable.Column',
                  'x-component-props': {
                    title: '选中封面图',
                    align: 'center',
                  },
                  properties: {
                    activeCover: {
                      type: 'string',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      'x-validator': [{ required: true, message: '请上传选物品选中封面图' }],
                    },
                  },
                },
                column4: {
                  type: 'void',
                  'x-component': 'ArrayTable.Column',
                  'x-component-props': {
                    title: '操作',
                    prop: 'operations',
                    width: 50,
                    fixed: 'right',
                  },
                  properties: {
                    item: {
                      type: 'void',
                      'x-component': 'FormItem',
                      properties: {
                        remove: {
                          type: 'void',
                          'x-component': 'ArrayTable.Remove',
                        },
                      },
                    },
                  },
                },
              },
            },
            properties: {
              add: {
                type: 'void',
                'x-component': 'ArrayTable.Addition',
                title: '新增条目',
              },
            },
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
