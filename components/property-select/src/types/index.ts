/** 组件配置信息 */
export interface Config {
  /** 组件id */
  id: string;

  /** 预览弹窗 */
  previewDialog: boolean;

  /** 选中key，用于localStorage存储 */
  selectedKey?: string;

  /** 选中区域文案 */
  text: string;
  /** 选中区域字体颜色 */
  textColor?: string;
  /** 选中区域背景图 */
  backgroundImage?: string;
  /** 选中区域背景色 */
  backgroundColor?: string;

  /** 弹窗背景图 */
  dialogBackgourundImage: string;
  /** 弹窗宽度 */
  dialogWidth: number;
  /** 弹窗高度 */
  dialogHeight: number;

  /** 卡片列表顶部距离 */
  propertysMarginTop: number;

  /** 确认按钮背景图 */
  confirmBackgourundImage: string;
  /** 确认按钮宽度 */
  confirmWidth: number;
  /** 确认按钮高度 */
  confirmHeight: number;
  /** 未选中Tips */
  confirmUnselectedTips: string;

  /** 物品列表配置 */
  propertys: Property[];
}

/** 物品配置信息 */
export interface Property {
  /** 物品名称 */
  name: string;
  /** 封面图 */
  cover: string;
  /** 选中封面图 */
  activeCover: string;
}
