<template>
  <div>
    <button
      class="property-select-button"
      :style="buttonStyle"
    >
      {{ buttonTitle }}
    </button>
    <van-dialog
      v-model:show="showDialog"
      class="property-select-dialog"
      :show-confirm-button="false"
      :show-cancel-button="false"
      z-index="999"
      :style="dialogStyle"
      teleport="body"
    >
      <div
        class="property-select-dialog-container"
        :style="{
          top: `${config.propertysMarginTop / 100}rem`
        }"
      >
        <div
          :style="{ visibility: config.propertys?.length > 0 ? 'visible' : 'hidden' }"
          class="property-select-dialog-propertys"
        >
          <img
            v-for="(item,index) in config.propertys"
            :key="item.name + index"
            class="property-select-dialog-propertys-item"
            :src="updateSelectedProperty?.name === item.name ? item.activeCover : item.cover"
            @click="handleSelected(item)"
          >
        </div>
        <button
          class="property-select-dialog-confirm-btn"
          :data-report="JSON.stringify(reportData)"
          :style="confirmButtonStyle"
          @click="handleConfirm"
        />
      </div>
    </van-dialog>
  </div>
</template>
<script setup lang="ts">
import 'vant/es/dialog/style';
import { Dialog as VanDialog } from 'vant';
import { computed, onMounted, ref, toRefs, watch } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import MoleJSBridge from '@tencent/mole-jsbridge';

import { PROPERTY_SELECTED } from '../constant';
import { useResize } from '../hooks/use-dialog-resize';
import {
  type Config,
  type Property,
} from '../types';

const moleJSBridge = MoleJSBridge.getJSBridge();

const { isLogin, openLogin, inMagic, $bus, toast } = hooks.useMokaInject();
const { getLoginUserOpenID } = utils;
const isYyb = moleJSBridge?.appEnv === 'yyb';

const props = defineProps<{
  config: Config;
}>();
const { config } = toRefs(props);

/** 已选中的物品 */
const selectedProperty = ref<Property>();
/** 变更选中的物品 */
const updateSelectedProperty = ref<Property>();
/** 是否显示弹窗 */
const showDialog = ref(false);

const {
  clientHeight,
} = useResize();

const buttonStyle = computed(() => {
  const {
    textColor,
    backgroundImage,
    backgroundColor,
  } = config.value;
  return {
    color: textColor,
    backgroundImage: `url(${backgroundImage || ''})`,
    backgroundColor,
  };
});

const buttonTitle = computed(() => config.value.text.replace(/\$selected/g, selectedProperty.value?.name || ''));

const confirmButtonStyle = computed(() => {
  const {
    confirmBackgourundImage,
    confirmWidth,
    confirmHeight,
  } = config.value;
  return {
    backgroundImage: `url(${confirmBackgourundImage})`,
    width: `${confirmWidth / 100}rem`,
    height: `${confirmHeight / 100}rem`,
  };
});

const dialogStyle = computed(() => {
  const {
    dialogBackgourundImage,
    dialogWidth,
    dialogHeight,
  } = config.value;

  // 当高度不足时，进行缩放; -50 是避免高度撑满clientHeight不美观
  let scaleHeight = 1;
  if (dialogHeight > clientHeight.value - 50) {
    scaleHeight = (clientHeight.value - 50) / dialogHeight;
  }

  return {
    backgroundImage: `url(${dialogBackgourundImage})`,
    width: `${dialogWidth / 100}rem`,
    height: `${dialogHeight / 100}rem`,
    transform: !inMagic ? `translateY(-50%) scale(${scaleHeight})` : 'translateY(-50%)',
    top: '50%',
  };
});

const reportData = {
  is_login: getLoginUserOpenID() ? 1 : 0,
  select_key: updateSelectedProperty.value,
};

watch(() => props.config.previewDialog, (newValue: boolean) => {
  if (!inMagic) {
    return;
  }

  showDialog.value = newValue;
}, {
  immediate: true,
});

onMounted(async () => {
  if (inMagic) {
    return;
  }

  // onMounted阶段isLogin.value无法及时识别登录，使用getLoginUserOpenID
  if (!getLoginUserOpenID()) {
    // 未登录，获取最后一次未登录选中记录并自动弹窗
    const lastSelectedName = await getStorage(false);
    selectedProperty.value = config.value.propertys.find(item => item.name === lastSelectedName);
    updateSelectedProperty.value = selectedProperty.value;
    showDialog.value = true;
    return;
  }

  // 已登录，不管刚才是否从未登录拉起的，以已登录选中记录为优先
  const curSelectedName = (await getStorage(true)) || (await getStorage(false));
  selectedProperty.value = config.value.propertys.find(item => item.name === curSelectedName);
  updateSelectedProperty.value = selectedProperty.value;
  // 如果两个都是空的，属于已登录但从未选中则自动弹窗
  if (!curSelectedName) {
    showDialog.value = true;
    return;
  }

  $bus?.$emit(PROPERTY_SELECTED, { config: config.value });
  setStorage(true);
  // 清除最后一次未登录选中记录
  sessionStorage.setItem(getStorageKey(false), '');
});

/** 点击选中物品 */
const handleSelected = async (item: Property) => {
  updateSelectedProperty.value = item;
};

/** 确认选中物品 */
const handleConfirm = async () => {
  if (!updateSelectedProperty.value) {
    toast?.(config.value.confirmUnselectedTips);
    return;
  }

  selectedProperty.value = updateSelectedProperty.value;
  // 未登录时新最后一次未登录选中记录，并拉起登录
  if (!isLogin.value) {
    setStorage(false);
    openLogin?.();
    return;
  }

  showDialog.value = false;
  setStorage(true);
  // 清除最后一次未登录选中记录
  sessionStorage.setItem(getStorageKey(false), '');
  $bus?.$emit(PROPERTY_SELECTED, { config: config.value });
};

/**
 * 根据登录情况，设置最后一次未登录选中记录 or 已登录选中记录
 * @param logged 已登录
 */
const setStorage = (logged: boolean) => {
  const key = getStorageKey(logged);
  const value = selectedProperty.value?.name || '';
  // 在宝内+登录则通过 jsbridge，保证和 native 一致
  if (isYyb && logged) {
    void moleJSBridge.call('saveData', {
      key,
      value,
      mode: 0,
    });

    return;
  }

  const storageInstance = logged ? localStorage : sessionStorage;

  storageInstance.setItem(key, value);
};

/**
 * 根据登录情况，返回最后一次未登录选中记录 or 已登录选中记录
 * @param logged 已登录
 */
const getStorage = async (logged: boolean) => {
  const key = getStorageKey(logged);
  // 在宝内+登录则通过 jsbridge，保证和 native 一致
  if (isYyb && logged) {
    const result = await moleJSBridge.call('getData', {
      key,
      mode: 0,
    });
    const storageValue = (result?.data as { [key: string]: any })?.[key];
    return storageValue && storageValue !== 'null' ? storageValue : '';
  }

  const storageInstance = logged ? localStorage : sessionStorage;
  return storageInstance.getItem(key);
};

/**
 * 根据登录情况，返回最后一次未登录选中key or 已登录选中key
 * @param logged 已登录
 */
const getStorageKey = (logged: boolean) => {
  let suffixKey = 'lasted-selected';
  if (logged) {
    suffixKey = getLoginUserOpenID();
  }

  return `property-select.${config.value.selectedKey || (globalThis as any).magicUiconfig?.[0]?.actId}.${suffixKey}`;
};

</script>
<style lang="scss" scoped>
.property-select-button {
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  white-space: nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
}
</style>
<style lang="scss" src="./index.scss" />
