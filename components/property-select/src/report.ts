import type { ComponentConfig, IPublicComponentReportSchema } from '@tencent/moka-schema/dist/component-meta-types';
import { BuiltInReportEvent } from '@tencent/mole-report';

export default function getReportConfig(componentConfig: ComponentConfig): IPublicComponentReportSchema {
  const reportModule = (componentConfig as any).report?.module?._module;
  const baseReportData = {
    _module: reportModule,
    mod_title: reportModule,
    btn_title: reportModule,
  };

  return {
    schema: {
      // 自定义事件
      publicRules: [],
      nodes: [{
        description: '物品选择弹窗',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: '.property-select-dialog',
          data: {
            ...baseReportData,
            eid: 'property-select-dialog',
          },
        },
      },
      {
        description: '物品选择按钮',
        rule: {
          events: [BuiltInReportEvent.Exposure, BuiltInReportEvent.Click],
          selector: '.property-select-dialog-confirm-btn',
          data: {
            ...baseReportData,
            button_text: '确认选择',
            eid: 'property-select-button',
          },
        },
      }],
    },
  };
}
