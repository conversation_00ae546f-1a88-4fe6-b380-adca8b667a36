import { defineStore } from '@tencent/moka-data-core';
import { getCookies, isYSDK, safeJsonStringify } from '@tencent/mole-utils-lib';

import { checkYSDKUserYYBOpenid, YSDKLoginInfo, YSDKLoginType } from '../api/ysdk';

/** YSDK 转换 Store */
export interface YSDKTransferState {
  hasYybOpenid: boolean;
}

/** Store ID */
const storeID = 'ysdk-transfer';
const inYSDK = isYSDK(navigator.userAgent);
export const useYSDKTransferStore = () => {
  const useStore = defineStore({
    id: storeID,
    state: (): YSDKTransferState => ({
      hasYybOpenid: false,
    }),
    actions: {
      async checkYSDKUser() {
        // 非 ysdk 场景无需校验
        if (!inYSDK) {
          this.hasYybOpenid = true;
          return;
        }

        const cookie = getCookies(document.cookie);
        const loginInfo: YSDKLoginInfo = {
          loginType: cookie.logintype as YSDKLoginType,
          appId: cookie.appid,
          openId: cookie.openid,
          token: cookie.access_token,
        };

        // 登录态异常或者手机登录用户
        if (!loginInfo.openId || !loginInfo.token || !loginInfo.appId || loginInfo.loginType === YSDKLoginType.PHONE) {
          console.log('[accessRequest: checkYSDKUser] 登录态异常或者为手机登录用户, loginInfo:', safeJsonStringify(loginInfo));
          this.hasYybOpenid = false;
          return;
        }

        if ([YSDKLoginType.QQ, YSDKLoginType.WX].includes(loginInfo.loginType)) {
          this.hasYybOpenid = await checkYSDKUserYYBOpenid(loginInfo);
          console.log('[accessRequest: checkYSDKUser] 结果, hasYybOpenid: ', this.hasYybOpenid);
        }
      },
      async getHasYybOpenid() {
        await this.checkYSDKUser();
        return this.hasYybOpenid;
      },
    },
  }, {
    isolate: false,
  });

  return useStore(storeID);
};
