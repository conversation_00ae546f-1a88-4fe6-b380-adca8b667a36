<template>
  <div @click="handleClickOrderConfirm">
    <div
      v-if="config.productType === ProductType.Card && spuInfo"
      class="point-exchange-box"
    >
      <spu-card-item
        :spu="spuInfo"
        :source-id="config.sourceID"
        :report-dict="reportDict"
      />
    </div>
    <img
      v-else
      class="product-img"
      :src="config.productImg"
      alt=""
    >
  </div>
</template>

<script setup lang="ts">
import 'vant/es/toast/style';
import { showToast } from 'vant';
import { computed, getCurrentInstance, ref, watch } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';

import { getExchangePopDetail } from '../api';
import SpuCardItem from '../components/spu-card-item.vue';
import { useYSDKTransferStore } from '../store';
import { type ComponentConfig, ProductType, SpuDetail } from '../types';
import { createIframe, getIframe } from '../utils/iframe';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { openLogin, loginReady, isLogin, inMagic, $bus } = hooks.useMokaInject();

if (!inMagic) {
  // 创建iframe
  createIframe(props.config.sourceID, Number(props.config.env));
}

const handleClickOrderConfirm = async () => {
  await loginReady;
  console.log('handleClickOrderConfirm');
  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  if ((window as any).vipCommonLogic) {
    // 判断是否已激活
    const isActivated = await (window as any).vipCommonLogic.tryActivate?.();
    if (!isActivated) {
      console.log('[point-exchange] 用户未激活');
      return;
    }

    // 判断是否已授权
    const isAuthed = await (window as any).vipCommonLogic.tryAuthGame?.();
    if (!isAuthed) {
      console.log('[point-exchange] 用户未授权');
      return;
    }
  }

  try {
    const iframe = getIframe();
    if (iframe.ownerDocument.readyState !== 'complete') {
      showToast('商品加载中，请稍后重试～');
      return;
    }

    const { spuID, isMonthCard } = props.config;
    iframe.contentWindow?.postMessage({
      type: 'exchange',
      spuID,
      isMonthCard,
      reportDict: reportDict.value,
    }, '*');
  } catch (error) {
    console.error('[handleClickOrderConfirm]', error);
  }
};

// 获取商品信息
const spuInfo = ref<SpuDetail>();
const reportDict = computed(() => {
  if (!spuInfo.value) return;
  const { exchangePoint, name, type, exchangeStatus } = spuInfo.value;
  const firstSKU = spuInfo.value.skuList[0];
  return {
    goods_price: exchangePoint,
    goods_title: name,
    mod_id: 'point_exchange_gift_card',
    tab_title: '活动平台组件',
    spu_id: props.config.spuID,
    spu_type: String(type),
    position: '-1',
    second_tab_title: '',
    has_condition: firstSKU.progress ? '1' : '0',
    buttonstatus: '活动平台-无效',
    spu_status: String(exchangeStatus),
    has_sub_mark: firstSKU.labels?.map(item => item.value)?.join(',') ?? '0',
    source_id: props.config.sourceID,
  };
});
const store = useYSDKTransferStore();
const getSPUInfo = async () => {
  await loginReady;
  const { spuID } = props.config;
  if (!spuID) return;
  const hasYybOpenid = await store.getHasYybOpenid();
  if (!hasYybOpenid) return;
  spuInfo.value = await getExchangePopDetail({ spuID: props.config.spuID, env: props.config.env });
};

watch(() => props.config.productType, () => {
  void getSPUInfo();
}, { immediate: true });

// 监听积分商城关闭事件 触发事件更新
window.addEventListener('message', receiveMessage);
const instance = getCurrentInstance();
function receiveMessage(event: MessageEvent) {
  const { origin, data } = event;
  if (!/qq.com/.exec(origin)) return;
  if (data.type === 'close' && Number(data.spuID) === Number(props.config.spuID)) {
    $bus?.$emit('pointExchange:closePopup', instance?.proxy);
  }
}
</script>

<style lang="scss" src="./index.scss" scoped />
