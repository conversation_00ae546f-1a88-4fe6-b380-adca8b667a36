import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        env: {
          type: 'number',
          title: '环境',
          default: '0',
          required: true,
          enum: [
            {
              label: '测试',
              value: '0',
            },
            {
              label: '预发布',
              value: '1',
            },
            {
              label: '正式',
              value: '2',
            },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
        },
        productType: {
          type: 'number',
          title: '商品展示类型',
          enum: [
            {
              label: '按钮形',
              value: 0,
            },
            {
              label: '商品卡片形',
              value: 1,
            },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-validator': [{ required: true, message: '请选择商品展示类型' }],
        },
        isMonthCard: {
          type: 'boolean',
          title: '是否是月卡商品',
          enum: [
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-decorator-props': {
            tooltip: '月卡和其他商品接口不一样需要识别',
            labelWrap: true,
          },
          'x-validator': [{ required: true, message: '请判断是否为月卡商品' }],
        },
        spuID: {
          type: 'string',
          title: '商品id',
          'x-decorator': 'FormItem',
          'x-component': 'TextLink',
          'x-validator': [{ required: true, message: '请输入商品id' }],
          'x-component-props': {
            linkTitle: '配置平台',
            linkStrOrFunc() {
              // TODO: 链接确定，还需修改
              return 'https://wujiang.woa.com/xy/app/dev/yyd/';
            },
          },
        },
        sourceID: {
          type: 'number',
          title: 'sourceID',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-decorator-props': {
            extra: '主要用于上报',
          },
          'x-validator': [{ required: true, message: '请输入sourceID' }],
        },
        productImg: {
          type: 'string',
          title: '商品图片',
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          'x-validator': [{ required: true, message: '请上传商品图片' }],
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [
      {
        label: '关闭兑换弹窗',
        value: 'pointExchange:closePopup',
        desc: '关闭兑换弹窗',
      },
    ],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
