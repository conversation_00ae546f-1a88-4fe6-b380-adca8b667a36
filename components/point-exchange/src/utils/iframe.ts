import { utils } from '@tencent/moka-ui-domain';
import { addParamsToURL } from '@tencent/mole-utils-lib';

import { Env } from '../types';

let globalIframe: HTMLIFrameElement | null = null;
let env = Env.Test;

const host = {
  [Env.Test]: 'https://m-test.iwan.yyb.qq.com',
  [Env.PreRelease]: 'https://iwan-test.yyb.qq.com',
  [Env.Release]: 'https://m.iwan.yyb.qq.com',
};

// 创建或获取 iframe 元素
export function getIframe() {
  createIframe('', env);

  return globalIframe!;
}

// 创建 iframe
export function createIframe(sourceID: string, envConfig = Env.Test) {
  env = envConfig;
  if (!globalIframe) {
    globalIframe = document.createElement('iframe');
    globalIframe.id = 'pointExchangeIframe';
    globalIframe.style.width = '100vw';
    globalIframe.style.height = '100vh';
    globalIframe.style.position = 'fixed';
    globalIframe.style.top = '0';
    globalIframe.style.left = '0';
    globalIframe.style.border = 'none';
    globalIframe.style.display = 'none';
    globalIframe.style.zIndex = '999';
    const src = addParamsToURL(`${host[envConfig]}/game-private/points-mall/order-confirm`, {
      source_id: sourceID,
    });
    globalIframe.src = src;
    document.body.appendChild(globalIframe);

    // 监听积分商城关闭事件,只需要监听一次
    window.addEventListener('message', receiveMessage);
  }
}

function receiveMessage(event: MessageEvent) {
  const { origin, data } = event;
  console.log('receiveMessage', event);
  if (!/qq.com/.exec(origin)) return;
  if (data.type === 'close') {
    hideIframe();
  }

  if (data.type === 'jump') {
    utils.jumpUrl(data.url);
  }

  if (data.type === 'show') {
    showIframe();
  }
}

function showIframe() {
  const iframe = getIframe();
  iframe.style.display = 'block';
  document.body.style.overflow = 'hidden';
}

function hideIframe() {
  const iframe = getIframe();
  iframe.style.display = 'none';
  document.body.style.overflow = 'unset';
}
