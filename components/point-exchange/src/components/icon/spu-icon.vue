<template>
  <div :class="$style.recommendProductIcon">
    <coupon-icon
      v-if="isCoupon"
      :coupon-data="product.skuList[0]"
    />
    <img
      v-else
      :class="$style.adatper"
      :src="product.image"
      alt=""
    >
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';

import { EExchangeType, GoodsType, SpuDetail } from '../../types';
import CouponIcon from './coupon-icon.vue';

const props = defineProps<{
  product: SpuDetail;
}>();

const isCoupon = computed(() => props.product.type === GoodsType.COMMON_SPU
  && Number(props.product.skuList[0].exchangeType) === EExchangeType.COUPON);
</script>

<style lang="scss" module>
.recommendProductIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 0.96rem;
  background-color: rgb(255 247 224);
}
.adatper {
  width: 0.58rem;
  height: 0.36rem;
  border-radius: 0.03rem;
}
</style>
