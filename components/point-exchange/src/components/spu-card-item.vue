<template>
  <div
    v-if="spu"
    dt-eid="goods_card"
    :dt-params="utils.getReportParamsStr({
      ...reportDict,
      goods_id: spu.id,
    }, inMagic)"
    class="recommendProduct"
  >
    <div class="productBox">
      <div
        v-if="labels?.value"
        class="tag"
      >
        {{ labels.value }}
      </div>
      <spu-icon
        v-if="isNeedAapterSkuImgTypes(spu.skuList[0])"
        :product="spu"
      />
      <img
        v-else
        class="skuImage"
        :src="spu.image"
      >
    </div>
    <div
      class="productInfo"
      dt-eid="exchange_goods_btn"
      :dt-params="utils.getReportParamsStr({
        ...reportDict,
        goods_id: spu.id,
        btn_title: btnText,
      }, inMagic)"
    >
      <div class="nameBox">
        <div class="name">
          {{ spu.name }}
        </div>
      </div>
      <div
        :class="{
          purchaseBox: true,
          disbale: !canExchange
        }"
      >
        <div v-if="canExchange">
          <div
            class="value"
          >
            <span
              class="point"
            >
              {{ formattedNeedPoints.points }}
            </span>
            <span class="unit">{{ formattedNeedPoints.unit }}</span>
          </div>
        </div>
        <div class="text">
          {{ btnText }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';

import { NEED_ADAPTER_SKU_IMG_SECOND_TYPES, NEED_ADAPTER_SKU_IMG_TYPES } from '../constant';
import { EGiftStatus, SkuDetail, SpuDetail } from '../types';
import { formatPoints } from '../utils/format';
import SpuIcon from './icon/spu-icon.vue';

const { inMagic } = hooks.useMokaInject();
const props = defineProps<{
  spu?: SpuDetail;
  reportDict?: Record<string, string>;
  sourceId: string;
}>();

const formattedNeedPoints = computed(() => formatPoints(props.spu?.exchangePoint || '0'));

const canExchange = computed(() => Number(props.spu?.exchangeStatus) === EGiftStatus.GIFT_STATUS_NORMAL);
const labels = computed(() => props.spu?.skuList[0].labels?.[0]);

const btnText = computed(() => {
  if (canExchange.value) return '兑';
  if ([EGiftStatus.GIFT_STATUS_GIFT_CANT_EXCHANGE, EGiftStatus.GIFT_STATUS_GIFT_HAS_GOT].includes(Number(props.spu?.exchangeStatus))) return '已兑换';
  return '已达限量';
});

// 用于判断商品是否需要适配 SKU 图片类型
const isNeedAapterSkuImgTypes = (good: SkuDetail) => {
  const isMatchFirstCategory = NEED_ADAPTER_SKU_IMG_TYPES.includes(Number(good.exchangeType));
  const isMatchSecondCategory = NEED_ADAPTER_SKU_IMG_SECOND_TYPES.includes(Number(good.secondLevelCategory));
  return isMatchFirstCategory || isMatchSecondCategory;
};
</script>
<style lang="scss" scoped>
@font-face {
  font-family: 'YYB';
  src: url('https://cms.myapp.com/xy/yybtech/Q7TnRdXh.ttf');
}
@mixin ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommendProduct {
  width: 100%;
  overflow: hidden;
  border-radius: 0.08rem;
  background-color: #FFFFFF;
  padding-bottom: 0.06rem;
}
.productBox {
  position: relative;
  width: 100%;
  height: 0.96rem;
  background-color: rgba(255, 242, 226, 1);
}
.nameBox {
  padding: 0 0.06rem;
}
.tag {
  position: absolute;
  top: 0.06rem;
  left: 0.06rem;
  z-index: 10;
  max-width: calc(100% - 0.12rem);
  padding: 0.01rem 0.06rem;
  font-size: 0.1rem;
  font-weight: 500;
  color: rgb(244 131 25 / 1);
  background: #FFE7BA;
  border-radius: 0.04rem;
}
.skuImage {
  width: 100%;
  height: 0.96rem;
  border-radius: 0.08rem 0.08rem 0 0;
}
.productInfo {
  background-color: rgba(245, 246, 249, 1);
  border-radius: 0 0 0.08rem 0.08rem;
  padding-bottom: 0.08rem;
}
.name {
  font-size: 0.12rem;
  font-weight: 500;
  line-height: 0.24rem;
  color: #000;
  text-align: justify;
  letter-spacing: -0.371px;

  @include ellipsis;
}
.purchaseBox {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin: 0 0.04rem;
  height: 0.24rem;
  padding: 0 0.06rem;
  background: url('https://cdn.yyb.gtimg.com/wupload/xy/yybtech/IQfdjHhY.png');
  background-size: 100% 100%;
  border-radius: 0.12rem;
  &.disbale {
    background: rgba(238, 60, 56, 0.2);
    justify-content: center;
    .text {
      color: #FFF;
    }
  }
}
.value {
  display: flex;
  align-items: center;
  color: #FFFFFF;
  height: 0.24rem;
  width: 0.5rem;
  overflow: hidden;
}
.point {
  font-family: YYB;
  font-size: 0.12rem;
  line-height: 0.15rem;
  transform: translate(0, 0.01rem);
}
.unit {
  text-wrap: nowrap;
  font-size: 0.1rem;
  @include ellipsis;
}
.text {
  font-size: 0.12rem;
  line-height: 0.24rem;
  font-weight: 700;
  color: #825237;
}
</style>
