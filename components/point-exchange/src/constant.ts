import { EExchangeType, TemplateGiftType } from './types/index';

/** 首充免单图片 */
export const FIRST_FLUSH = 'https://vfiles.gtimg.cn/wupload/fileupload/518edc17_Sa-kZoJQKrsc3szZQXL7abias0NTZ-JL.png';

/** 需要适配产品封面图的 SKU 类型 */
export const NEED_ADAPTER_SKU_IMG_TYPES = [
  EExchangeType.COUPON,
  EExchangeType.GIFT_VIP,
  EExchangeType.MEMBER_SHIP,
  EExchangeType.WECHAT_COUPON,
  EExchangeType.INCREMENT_PACKAGE,
];

/** 需要适配产品封面图的 SKU 二级类型 */
export const NEED_ADAPTER_SKU_IMG_SECOND_TYPES = [
  TemplateGiftType.TEMPLATE_CDKEY_VOUCHER_PROP,
];

export const REQUEST_SUCCESS_CODE = 0;

export const COMPONENT_TYPE = 'point-exchange';
