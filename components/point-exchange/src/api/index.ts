import { camelize<PERSON><PERSON>s } from 'humps';
import { commonRequester } from '@tencent/moka-data-core';
import { isYSDK } from '@tencent/mole-utils-lib';

import { COMPONENT_TYPE, REQUEST_SUCCESS_CODE } from '../constant';
import { Env, SpuDetail } from '../types';

/**
 * 查询兑换弹窗信息
 * @param spuID 商品id
 * @returns 商品详情
 */
export async function getExchangePopDetail({
  spuID,
  env,
}: { spuID: string; env: Env }): Promise<SpuDetail | undefined> {
  const isTest = Number(env) === Env.Test;
  let host = isTest ? 'ydd-test.yyb.qq.com' : 'ydd.yyb.qq.com';

  if (isYSDK(navigator.userAgent)) {
    host = isTest ? 'ysdk-test.yyb.qq.com' : 'ysdk.yyb.qq.com';
  }

  const resp = await commonRequester.request<any, {
    ret_code: number;
    err_msg: string;
    spu: unknown;
  }>({
    componentType: COMPONENT_TYPE,
    url: `https://${host}/trpc.private_domain.yyd_gift_exchange.YydGiftExchange/GetExchangePopDetail`,
    withCredentials: true,
    method: 'POST',
    data: {
      goods_id: spuID,
    },
  });

  const { code, tip, body: res } = resp;

  if (!res) {
    console.error('[getExchangePopDetail] 获取商品异常', code, tip);
    return;
  }

  const { ret_code: bizCode, err_msg: errMsg, spu } = res;
  if (bizCode !== REQUEST_SUCCESS_CODE) {
    console.error('[getExchangePopDetail] 获取兑换商品信息接口异常', bizCode, errMsg);
    return;
  }

  return camelizeKeys(spu) as SpuDetail;
}
