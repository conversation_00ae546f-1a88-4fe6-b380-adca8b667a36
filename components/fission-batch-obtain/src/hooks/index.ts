import 'vant/es/toast/style';
import '@tencent/moka-athena-next-v2/dist/style.css';
import { showToast } from 'vant';
import { computed, ref } from 'vue';
import { PropertyReceive } from '@tencent/moka-athena-next-v2';
import {
  common,
  constants,
  hooks,
  type ObtainInfo,
  type ObtainProperty,
  store,
} from '@tencent/moka-ui-domain';

import { doObtain, fetchObtainInfo, getDeliveryInfo } from '../api';
import { showObtainResultPopup } from '../components/obtain-result-popup';
import ProgressPopup from '../components/progress';
import { type ComponentConfig, PartitionType } from '../types';
import { formatObtainInfo, showCdkeyCodePopup } from '../utils';

function toast(tips: string) {
  showToast({
    message: tips,
    // 比 van-popup 蒙层高防止看不清
    zIndex: 3000,
  });
}

export function useBatchObtain(config: ComponentConfig) {
  // 所有配置的物品
  const properties = ref<ObtainProperty[]>(config.batchObtainConfigs?.map((propertyConfig) => {
    const { threshold, obtainID } = propertyConfig;
    return {
      threshold,
      obtainID,
    };
  }) ?? []);
  const { UPDATE_YYB_EVENT } = constants;
  // 所有可领取的物品
  const canObtainProperties = computed(() => properties.value.filter((item) => {
    const { obtainInfo, threshold } = item;
    if (fissionStore.ladderPoints < Number(threshold)) return false;
    if (!obtainInfo) return false;
    const {
      isPersonalOutOfStock,
      isGlobalOutOfStock,
      isCDK,
      hasReceiveRecord,
    } = obtainInfo;
    // CDK 没有领取记录才能领取
    if (isCDK) return !hasReceiveRecord;

    // 其余物品由限量决定
    return !isPersonalOutOfStock && !isGlobalOutOfStock;
  }));
  const {
    loginReady,
    inMagic,
    $bus,
  } = hooks.useMokaInject();
  const { useFissionBoostStore, useYybUpdateStore } = store;
  const fissionStore = useFissionBoostStore();
  const yybUpdateStore = useYybUpdateStore();

  // 校验区服信息
  const checkPartition = async () => {
    const { partitionType, getPartitionRoleInfo } = hooks.useBoundPartitionInfo(config.pkgName);
    const { data: boundPartitionAndRole } = await getPartitionRoleInfo() || {};
    if (partitionType.value !== PartitionType.NOT_PARTITION && !boundPartitionAndRole) {
      console.warn('游戏需要区服信息 用户区服信息无效');
      return false;
    }

    return true;
  };

  // 获取物品领取信息
  const getObtainInfo = async (obtainID: string) => {
    if (inMagic) return;
    const obtainInfo = await fetchObtainInfo(config.id, obtainID);
    const index = properties.value.findIndex(property => property.obtainID === obtainID);
    const { defaultPropertyIcon } = config.propertyConfigs;
    const formattedObtainInfo = formatObtainInfo(obtainInfo, defaultPropertyIcon);
    properties.value[index].obtainInfo = formattedObtainInfo;
    return formattedObtainInfo;
  };

  // 执行物品领取
  const doObtainProperty = async (obtainProperty: ObtainProperty): Promise<{
    code: number;
    msg: string;
    orderId: string;
  } | undefined> => {
    const { obtainInfo, obtainID } = obtainProperty;
    if (!obtainInfo) return;
    const {
      isGlobalOutOfStock,
      isPersonalOutOfStock,
    } = obtainInfo;

    // 已领取或已领完
    if (isGlobalOutOfStock || isPersonalOutOfStock) return;

    await loginReady;
    if (!await fissionStore.openVipCommonPopup()) {
      return;
    }

    const {
      property,
      isAMS,
      isQb,
      isRealGood,
    } = obtainInfo;
    const isPropertyReady = property?.instanceInfo && property.propertyDetail;
    if (!isPropertyReady) return;
    if (!yybUpdateStore.isVersionValid) {
      $bus?.$emit(UPDATE_YYB_EVENT, { config });
      return;
    }

    return new Promise((resolve) => {
      new PropertyReceive({
        propertyData: property, // 中台物品数据
        modId: Number(obtainID),
        methodName: 'DoObtain',
        type: isAMS ? 'ams' : 'default', // 中台组件要传，指定为default
        isSilentReceive: true, // 均为静默领取 裂变维护自己的领取结果弹窗
        doReceiveFn: async (params) => {
          const { propertyData, ext, verifyInfo, qualifierParams } = params;
          console.log('qualifierParams', qualifierParams);
          console.log('传入的中台物品数据', propertyData);
          console.log('前置插件收集到的发货参数', ext);
          console.log('防水墙校验结果（任务组件使用）', verifyInfo);
          const extendsInfo = isAMS || isQb ? JSON.stringify(ext) : '';
          const obtainResult = await doObtain(config.id, obtainID, extendsInfo, qualifierParams);

          let orderId = '';
          function standardReturnData(code: number, msg: string) {
            const returnData = {
              code, // 0为成功，其他视为失败
              msg,
              orderId,
            };
            resolve(returnData);
            return returnData;
          }

          // 领取接口错误
          if (!obtainResult || obtainResult?.tip) {
            return standardReturnData(-1, obtainResult?.tip || '网络错误');
          }

          const { isPass, msg } = common.checkQualifier(obtainResult as any, false);
          // 实物需要填写订单ID，主要服务于实物弹窗填写地址 本期不涉及 保留原逻辑
          orderId = isRealGood ? obtainResult?.data?.record?.order_id : '';

          if (!isPass) {
            return standardReturnData(-1, obtainResult?.tip || msg || '网络错误');
          }

          const formattedObtainInfo = await getObtainInfo(obtainID);
          if (!formattedObtainInfo?.hasReceiveRecord) {
            return standardReturnData(-1, '网络错误');
          }

          // 实物但是没有订单 id 直接返回 TODO 本期不涉及
          // if (isRealGood && !orderId) {
          //   return;
          // }
          return standardReturnData(0, '');
        },
      });
    });
  };

  /** 批量领取物品 */
  const doBatchObtain = async () => {
    await loginReady;
    if (!await fissionStore.openVipCommonPopup()) {
      return;
    }

    if (canObtainProperties.value.length === 0) return;

    if (!yybUpdateStore.isVersionValid) {
      $bus?.$emit(UPDATE_YYB_EVENT, { config });
      return;
    }

    if (config.pkgName) {
      const checkPartitionResult = await checkPartition();
      if (!checkPartitionResult) return;
    }

    let progressInstance: ProgressPopup | null = null;
    const successProperties: ObtainInfo[] = [];
    const failedProperties: ObtainInfo[] = [];
    const failedResults: any[] = [];
    const step = Math.floor(100 / canObtainProperties.value.length);
    for (let index = 0; index < canObtainProperties.value.length; index++) {
      const obtainProperty = canObtainProperties.value[index];
      try {
        const obtainResult = await doObtainProperty(obtainProperty);
        if (!obtainResult) continue;
        if (obtainResult.code === 0) {
          successProperties.push(obtainProperty.obtainInfo!);
        } else {
          failedProperties.push(obtainProperty.obtainInfo!);
          failedResults.push(obtainResult);
        }

        // 此时创建进度弹窗，防止蒙层级别过高无法点击
        if (!progressInstance) progressInstance = new ProgressPopup();
        // 第一个请求失败，直接结束进度条
        if (index === 0 && !successProperties.length) {
          progressInstance.setPercent(100);
          break;
        }

        progressInstance.setPercent(step * index);
      } catch (error) {
        console.error(error);
      }
    }

    console.log('[doBatchObtain]', successProperties, failedProperties, failedResults);
    progressInstance?.endLoadingAndSetEndCB(() => {
      if (successProperties.length > 0) {
        showObtainResultPopup(successProperties);
        return;
      }

      // 直接取第一个失败的错误消息
      toast?.(failedResults[0].msg || '领取失败');
    });
  };

  /** 领取单个物品 */
  const doSingleObtain = async (obtainID: string) => {
    await loginReady;
    if (!await fissionStore.openVipCommonPopup()) {
      return;
    }

    // 放过 CDK 的可领取校验 在后续拦截
    const obtainProperty = properties.value.find(item => item.obtainID === obtainID);
    if (!obtainProperty) return;
    const { obtainInfo } = obtainProperty;
    if (!obtainInfo) return;
    const {
      isGlobalOutOfStock,
      isPersonalOutOfStock,
      isCDK,
      hasReceiveRecord,
    } = obtainInfo;

    // cdk 礼包且有领取记录拉起 cdk 复制弹窗
    if (isCDK && hasReceiveRecord) {
      const cdkeyCode = await getCDKCodeByObtainID(obtainID);
      if (!cdkeyCode) {
        toast('获取礼包码失败，请联系客服');
        return;
      }

      const { instanceInfo, propertyDetail } = obtainInfo.property;
      showCdkeyCodePopup(instanceInfo, propertyDetail, cdkeyCode);
      return;
    }

    if (isGlobalOutOfStock || isPersonalOutOfStock) return;

    if (config.pkgName) {
      const checkPartitionResult = await checkPartition();
      if (!checkPartitionResult) return;
    }

    const progressInstance = new ProgressPopup();
    const obtainResult = await doObtainProperty(obtainProperty);

    progressInstance.endLoadingAndSetEndCB(() => {
      if (obtainResult?.code === 0) {
        showObtainResultPopup([obtainProperty.obtainInfo!]);
        return;
      }

      toast?.(obtainResult?.msg || '领取失败');
    });
  };

  /** 获取兑换码 */
  const getCDKCodeByObtainID = async (obtainID: string) => {
    const property = properties.value.find(item => item.obtainID === obtainID);
    const { cdkeyCode, orderId } = property?.obtainInfo || {};

    if (!orderId) {
      console.error('[getCDKCodeByObtainID]订单id为空', property);
      return;
    }

    if (cdkeyCode) return cdkeyCode;
    const { cdkeyCode: cdkCode = '' } = await getDeliveryInfo(orderId) || {};
    const trimmedCdkCode = cdkCode.trim();
    property!.obtainInfo!.cdkeyCode = trimmedCdkCode;

    return trimmedCdkCode;
  };

  const isInit = ref(false);
  /** 初始化 */
  const init = async () => {
    const requests = config.batchObtainConfigs?.map(propertyConfig => (async () => {
      await getObtainInfo(propertyConfig.obtainID);
    })()) ?? [];

    await Promise.all(requests);
    isInit.value = true;

    console.log('properties', properties.value);
  };

  return {
    properties,
    canObtainProperties,
    isInit,
    init,
    doBatchObtain,
    doSingleObtain,
  };
}
