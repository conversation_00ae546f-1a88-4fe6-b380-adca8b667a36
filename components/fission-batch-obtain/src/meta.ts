import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        /** 关联领取组件配置 */
        batchObtainConfigOptionalBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '关联领取组件组',
          },
          properties: {
            batchObtainConfigs: {
              type: 'array',
              'x-decorator': 'FormItem',
              'x-component': 'ArrayTable',
              'x-component-props': {
                emptyText: '暂无数据',
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '物品领取积分门槛',
                    },
                    properties: {
                      threshold: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'InputNumber',
                        'x-component-props': {
                          placeholder: '请输入物品领取积分门槛',
                        },
                        'x-validator': [{ required: true, message: '请输入物品领取积分门槛' }],
                      },
                    },
                  },
                  column2: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '领取模块关联',
                      align: 'center',
                    },
                    properties: {
                      obtainID: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'ModSelect',
                        'x-component-props': {
                          modType: 12,
                        },
                        'x-validator': [{ required: true, message: '请选择领取后端模块' }],
                      },
                    },
                  },
                  column3: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '操作',
                      prop: 'operations',
                      width: 50,
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  title: '新增',
                },
              },
            },
          },
        },
        /** 物品配置 */
        collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '物品配置',
              },
              properties: {
                propertyConfigs: {
                  type: 'object',
                  properties: {
                    title: {
                      title: '模块标题',
                      name: '模块标题',
                      'x-component': 'Input',
                      'x-decorator': 'FormItem',
                      default: '赚积分 免费领礼包',
                    },
                    obtainedIcon: {
                      type: 'string',
                      title: '已领物品ICON',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: 'https://yyb.qpic.cn/moka-imgs/1730181841970-8dk3ayhxraj.png',
                    },
                    defaultPropertyIcon: {
                      type: 'string',
                      title: '兜底物品图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: 'https://yyb.qpic.cn/moka-imgs/1730191223931-i4w9mkyhma.png',
                    },
                  },
                },
              },
            },
          },
        },
        /** 包名 */
        pkgName: {
          type: 'string',
          title: '包名',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'AppSelect',
          'x-component-props': {
            bindType: 'pkgName',
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
