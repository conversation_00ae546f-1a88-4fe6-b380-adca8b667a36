import { PropertyReceive, Utils } from '@tencent/moka-athena-next-v2';
import {
  type InstanceInfo,
  type ObtainInfo,
  type ObtainProperty,
  type ObtainPropertyInfo,
  type PropertyDetail,
  UserObtainStatus,
} from '@tencent/moka-ui-domain';

/**
 * 格式化领取信息
 * @param obtainPropertyInfo 后台返回的物品信息
 * @param defaultIcon 默认礼包图标
 * @returns 格式化后的领取信息
 */
export function formatObtainInfo(
  obtainPropertyInfo?: ObtainPropertyInfo,
  defaultIcon = '',
): ObtainInfo | undefined {
  if (!obtainPropertyInfo) return;
  const {
    obtain_online: {
      user_records: userRecords = [],
      basic_info: {
        property: { instance_info: property, detail: propertyDetail },
      },
      user_obtain_status: userObtainStatus,
    },
  } = obtainPropertyInfo || {};

  const isPersonalOutOfStock = userObtainStatus === UserObtainStatus.USER_OBTAIN_STATUS_RECEIVED;
  const isGlobalOutOfStock = userObtainStatus === UserObtainStatus.USER_OBTAIN_STATUS_FINISHED;
  const hasReceiveRecord = userRecords.length > 0;
  const isCDK = isCDKey(propertyDetail);
  const buttonText = getButtonText(isGlobalOutOfStock, isPersonalOutOfStock, isCDK, hasReceiveRecord);

  return {
    orderId: userRecords[0]?.order_id,
    userObtainStatus,
    isPersonalOutOfStock,
    isGlobalOutOfStock,
    hasReceiveRecord,
    property: {
      instanceInfo: {
        ...property,
        pic_url: property?.pic_url || defaultIcon,
      },
      propertyDetail,
    },
    isAMS: Utils.isAms({ propertyDetail }),
    isQb: Utils.isQB({ propertyDetail }),
    isRealGood: false,
    isCDK,
    buttonText,
    // isRealGood: Utils.({ propertyDetail }), //  TODO  本期不涉及实物， yyb-sdk 也没对应判断 先不写
  };
}

function getButtonText(
  isGlobalOutOfStock: boolean,
  isPersonalOutOfStock: boolean,
  isCDK: boolean,
  hasReceiveRecord: boolean,
) {
  if (isCDK && hasReceiveRecord) {
    return '去兑换';
  }

  if (isGlobalOutOfStock) {
    return '已领完';
  }

  if (isPersonalOutOfStock) {
    return '已领取';
  }

  return '领取';
}

/**
 * 是否是 CDKey 物品
 * @param propertyDetail 物品信息
 * @returns boolean
 */
function isCDKey(propertyDetail: PropertyDetail): boolean {
  return Utils.getPropertyType({ propertyDetail }).presentType === Utils.PRESENT_TYPE.CDKEY;
}

/**
 * 展示 cdk 复制弹窗
 * @param instanceInfo  物品信息
 * @param propertyDetail  物品信息
 * @param cdkeyCode 兑换码
 */
export function showCdkeyCodePopup(instanceInfo: InstanceInfo, propertyDetail: PropertyDetail, cdkeyCode: string) {
  const { h5_link: cdkeyLinkUrl = '', name: title = '' } = instanceInfo;

  new PropertyReceive({
    propertyData: {
      instanceInfo,
      propertyDetail,
    },
    receiveResult: {
      code: 0,
      msg: '',
      showResult: {
        title,
        desc: '',
        customData: {
          cdkeyCode,
          cdkeyLink: cdkeyLinkUrl,
        },
      },
    },
    isReceived: true,
  });
}

/**
 * 获取当前获取积分对应激活物品索引
 * @param properties 物品列表
 * @param curPoints 当前积分
 * @returns number
 */
export function findLatestActivePropertyIndex(properties: ObtainProperty[], curPoints: number) {
  if (curPoints <= Number(properties[0].threshold)) return 0;
  if (curPoints >= Number(properties[properties.length - 1].threshold)) return properties.length - 1;
  for (let i = 0; i < properties.length - 1; i++) {
    if (curPoints >= Number(properties[i].threshold) && curPoints < Number(properties[i + 1].threshold)) {
      return i;
    }
  }

  return properties.length - 1;
}
