<template>
  <div class="fission-batch-obtain">
    <!-- 顶部标题栏 -->
    <div class="title">
      {{ config.propertyConfigs.title }}
    </div>
    <!-- 物品列表 -->
    <div class="properties-container">
      <div
        ref="propertyScrollRef"
        class="properties-container"
        @scroll="onScroll"
      >
        <!-- 所有物品横滑区域 -->
        <div class="properties">
          <property
            v-for="(property,index) in properties"
            :key="property.obtainID"
            :property="property"
            :config="config"
            :reached-threshold="ladderPoints >= Number(property.threshold)"
            :is-big-prize="index === properties.length - 1"
            dt-eid="welfare"
            dt-params="mod_id=welfare_receive_card&mod_title=福利领取卡"
            :dt-cmd="`hold=${fissionStore.isReportHeld}`"
            @click="onClickProperty(property.obtainID)"
          />
        </div>
        <!-- 进度条 -->
        <points-progress-bar
          :ladder-points="ladderPoints"
          :properties="properties"
          :progress-style="progressStyle"
          :active-progress-style="computedBarStyleMap.activeProgressStyle"
          :points-triangle-style="computedBarStyleMap.pointsTriangleStyle"
        />
        <!-- 已领积分 -->
        <received-points
          :received-points="ladderPoints"
          :received-points-style="computedBarStyleMap.receivedPointsStyle"
          :style="progressStyle"
          @click="showPropertyDetails"
        />
      </div>
      <!-- 大奖：未滚动至最后一个奖品时固定展示 -->
      <div
        v-if="isBigPrizeVisible"
        class="big-prize"
      >
        <property
          :property="bigPrize"
          :config="config"
          :reached-threshold="ladderPoints >= Number(bigPrize.threshold)"
          active-bar-width="0"
          is-big-prize
          dt-eid="welfare"
          dt-params="mod_id=welfare_receive_card&mod_title=福利领取卡"
          :dt-cmd="`hold=${fissionStore.isReportHeld}`"
          @click="onClickProperty(bigPrize.obtainID)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { showToast } from 'vant';
import { computed, defineProps, ref, watch } from 'vue';
import { BoostEvent, DetailPopupType, hooks, store } from '@tencent/moka-ui-domain';

import PointsProgressBar from '../components/points-progress-bar.vue';
import Property from '../components/property.vue';
import ReceivedPoints from '../components/received-points.vue';
import { useBatchObtain } from '../hooks';
import type { ComponentConfig } from '../types';
import { findLatestActivePropertyIndex } from '../utils';

const props = defineProps<{
  config: ComponentConfig;
}>();

const {
  properties,
  canObtainProperties,
  isInit,
  init,
  doBatchObtain,
  doSingleObtain,
} = useBatchObtain(props.config);
const { useFissionBoostStore } = store;
const { $bus, loginReady } = hooks.useMokaInject();
const fissionStore = useFissionBoostStore();
// 阶梯积分
const ladderPoints = computed(() => fissionStore.ladderPoints ?? 0);

const onClickProperty = async (obtainID: string) => {
  await loginReady;
  if (!await fissionStore.openVipCommonPopup()) return;

  const property = properties.value.find(p => p.obtainID === obtainID);
  if (!property) return;

  if (ladderPoints.value < Number(property.threshold)) {
    showToast(`邀好友获得${property.threshold}积分以上免费领`);
    return;
  }

  void doSingleObtain(property.obtainID);
};

const bigPrize = computed(() => properties.value.slice(-1)[0]);
const propertyWidth = 0.7;
const propertyWidthContainMargin = 0.77;
// 进度条样式计算
const progressStyle = computed(() => ({
  width: `${propertyWidthContainMargin * (properties.value.length - 1) + propertyWidth}rem`,
}));
// 最后一个激活节点
const latestActiveIndex = computed(() => findLatestActivePropertyIndex(properties.value, ladderPoints.value));
// 计算各个节点的样式：包括激活进度条、激活进度条上的点、激活进度条上的点容器、底部气泡
const computedBarStyleMap = computed(() => {
  const index = latestActiveIndex.value;
  const curThreshold = Number(properties.value[index].threshold);
  // 进度未达到第一个奖品
  if (ladderPoints.value < Number(properties.value[0].threshold)) {
    return {
      width: `${ladderPoints.value / curThreshold * (propertyWidth / 2)}rem`,
      activeProgressStyle: {
        width: `${ladderPoints.value / curThreshold * (propertyWidth / 2)}rem`,
      },
      receivedPointsStyle: {
        left: 0,
      },
      pointsTriangleStyle: {
        right: ladderPoints.value / curThreshold < 0.3 ? '-0.1rem' : undefined,
      },
    };
  }

  // 进度达到最后一个奖品
  if (index === properties.value.length - 1) {
    // 进度已超过最后一个奖品
    if (ladderPoints.value > curThreshold) {
      return {
        activeProgressStyle: {
          width: '100%',
        },
        receivedPointsStyle: {
          right: '0',
        },
        pointsTriangleStyle: {
          right: '0.04rem',
        },
      };
    }

    return {
      activeProgressStyle: {
        width: '100%',
      },
      receivedPointsStyle: {
        right: '0.26rem',
      },
      pointsTriangleStyle: {
        right: `${propertyWidth / 2 - 0.04}rem`,
      },
    };
  }

  const nextThreshold = Number(properties.value[index + 1].threshold);
  // 达到下一个奖品圆点所占单个进度条比例对应的进度条宽度
  const radioDistance = (ladderPoints.value - curThreshold) / (nextThreshold - curThreshold)
    * propertyWidthContainMargin;
  // 判断进度是否已达到下一个奖品，是则加上奖品本身所占宽度，否则直接返回所占比例宽度
  const distance = ladderPoints.value >= curThreshold ? radioDistance + (propertyWidth / 2) : radioDistance;

  return {
    activeProgressStyle: {
      width: `${propertyWidthContainMargin * index + distance}rem`,
    },
    receivedPointsStyle: {
      left: `${propertyWidthContainMargin * index + distance}rem`,
      transform: 'translateX(-50%)',
    },
  };
});

// 监听滚动 滚动到到最后一个奖品时，隐藏大礼包
const isBigPrizeVisible = ref(true);
const onScroll = (e: Event) => {
  if (!e.target) return;
  const target = e.target as HTMLElement;
  const children = propertyScrollRef.value.getElementsByClassName('property-container');
  const lastItem = children[children.length - 1];
  const lastItemRect = lastItem.getBoundingClientRect();
  const containerRect = target.getBoundingClientRect();

  if (lastItemRect.right <= (containerRect.right + lastItemRect.width / 4)) {
    isBigPrizeVisible.value = false;
    return;
  }

  isBigPrizeVisible.value = true;
};

// 拿到礼包数据后锚定到第一个未领取的奖品, 只执行一次
const propertyScrollRef = ref();
watch(() => isInit.value, () => {
  const list = propertyScrollRef.value.getElementsByClassName('property-container');
  const propertyItemWidth = list[0].getBoundingClientRect().width;
  // 找到最后一个领取的奖品
  const index = properties.value.findIndex(item => !item.obtainInfo?.isPersonalOutOfStock) - 1;
  if (index <= 0) return;
  propertyScrollRef.value.scrollLeft = index * propertyItemWidth;
}, { once: true });

$bus?.$on(BoostEvent.GetFissionBatchObtainInfo, (cb: (batch: Partial<ReturnType<typeof useBatchObtain>>) => void) => {
  cb({
    properties,
    canObtainProperties,
    doBatchObtain,
    doSingleObtain: onClickProperty,
  });
});

void init();

const showPropertyDetails = async () => {
  await loginReady;
  if (!await fissionStore.openVipCommonPopup()) {
    return;
  }

  $bus?.$emit(BoostEvent.OpenDetailPopup, DetailPopupType.Points);
};
</script>

<style lang="scss" src="./index.scss" scoped />
