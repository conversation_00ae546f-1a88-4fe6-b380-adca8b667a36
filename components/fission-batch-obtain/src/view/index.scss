@font-face {
  font-family: 'YYB';
  src: url('https://cms.myapp.com/xy/yybtech/Q7TnRdXh.ttf');
}

.fission-batch-obtain {
  width: 3.43rem;
  height: auto;
  padding: 0 0.12rem 0.12rem;
  border-radius: 0.13rem;
  background: transparent;

  .title {
    font-family: Noto Sans CJK SC;
    font-size: 0.14rem;
    font-weight: 500;
    line-height: 0.13rem;
    text-align: center;
    color: rgba(140, 91, 49, 1);
    margin-bottom: 0.12rem;
  }

  .properties-container {
    position: relative;
    display: flex;
    align-items: center;

    .properties-container {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      overflow-x: scroll;
      padding-bottom: 0.09rem;

      &::-webkit-scrollbar {
        display: none;
      }
      .properties {
        display: flex;
        width: 100%;
      }
    }

    .big-prize {
      position: absolute;
      right: 0;
      top: 0;
      background-color: #fff;
      &::after {
        content: '';
        position: absolute;
        bottom: -0.2rem;
        right: 0;
        height: 0.2rem;
        width: 0.76rem;
        background: linear-gradient(270deg, rgba(255, 255, 255, 1) 10%, rgba(255, 255, 255, 0) 100%);
      }
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -0.06rem;
        height: 0.94rem;
        width: 0.06rem;
        background: linear-gradient(270deg, rgba(255, 255, 255, 0.6) 100%, #FFFFFF 50%,);
        z-index: 1;
      }
    }
  }
}