import { Utils } from '@tencent/moka-athena-next-v2';
import { actExecRequester, actUtils } from '@tencent/moka-data-core';
import type { ObtainPropertyInfo } from '@tencent/moka-ui-domain';

import { COMPONENT_TYPE } from '../const';

/** 条件验证参数 */
interface QualifierParam {
  /** 业务名 */
  biz_name: string;
  /** UDF 名 */
  udf_name: string;
  /** 参数名 */
  param_name: string;
  /** 参数值 */
  value: string;
}

/**
 * 获取领取物品信息
 * @param componentID 组件 ID
 * @param modID 领取模块 ID
 * @returns 领取物品信息
 */
export async function fetchObtainInfo(componentID: string, modID: string) {
  try {
    const { code, body } = await actExecRequester.request<{ obtain_iid: string }, ObtainPropertyInfo>({
      componentInfo: { modID },
      componentType: COMPONENT_TYPE,
      componentID,
      invocation: {
        name: '/trpc.component_plat.obtain.Obtain/GetObtainInfo',
        data: {
          obtain_iid: actUtils.getCIIDByModID({ modID }),
        },
      },
      qualifier_params: [],
    });

    if (code !== 0 && !body?.data?.data) {
      return;
    }

    return body?.data?.data;
  } catch (err) {
    console.log('[fission-batch-obtain]获取领取信息失败', err);
    return;
  }
}

/**
 * 领取物品
 * @param componentID 组件 ID
 * @param modID 领取模块 ID
 * @param extendsInfo 扩展信息（前置插件收集到的发货参数）
 * @param qualifierParams 条件验证参数列表
 * @returns 领取结果
 */
export async function doObtain(
  componentID: string,
  modID: string,
  extendsInfo: unknown,
  qualifierParams: QualifierParam[],
): Promise<{
    data?: any;
    qualifier_result?: unknown;
    server_ts?: string;
    qualifierResult?: unknown;
    tip?: string;
  } | undefined> {
  const res = await actExecRequester.request({
    componentInfo: { modID },
    componentType: COMPONENT_TYPE,
    componentID,
    invocation: {
      name: '/trpc.component_plat.obtain.Obtain/DoObtain',
      data: {
        obtain_iid: actUtils.getCIIDByModID({ modID }),
        extends: extendsInfo,
      },
    },
    qualifier_params: qualifierParams,
  });

  if (res.code !== 0) {
    return {
      tip: res.tip,
    };
  }

  return res?.body?.data;
}

/**
 * 获取物品信息（cdk、配送地址等）
 * @param orderId 订单号
 */
export async function getDeliveryInfo(orderId: string): Promise<{
  cdkeyCode: string;
  yybGiftID: string;
} | null> {
  return Utils.getDeliveryInfo(null, orderId || 'GF-25-20241106105139-2f5dti');
}
