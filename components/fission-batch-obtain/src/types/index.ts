import { CSSProperties } from 'vue';

export interface ComponentConfig {
  id: string;
  batchObtainConfigs?: Array<{
    threshold: number | string;
    obtainID: string;
  }>;
  pkgName: string;
  originalStyle: CSSProperties;
  propertyConfigs: {
    title: string;
    obtainedIcon: string;
    defaultPropertyIcon: string;
  };
}

/** 游戏区服类型 */
export enum PartitionType {
  /** 不选区服 */
  NOT_PARTITION = 0,
  /** 只选区服 */
  ONLY_PARTITION = 1,
  /** 区服 + 角色 */
  PARTITION_AND_ROLE = 2,
}
