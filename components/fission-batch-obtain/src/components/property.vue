<template>
  <div class="property-container">
    <div
      :class="['property', { 'big-prize': isBigPrize }]"
      :style="propertyStyle"
    >
      <!-- 已领取遮罩 -->
      <div
        v-if="property.obtainInfo?.isPersonalOutOfStock"
        class="overlay"
      />
      <div class="property-info">
        <!-- 未解锁 -->
        <img
          v-if="!reachedThreshold"
          src="https://yyb.qpic.cn/moka-imgs/1741852082773-hbbbh9wr5du.png"
          alt=""
          class="status-icon"
        >
        <!-- 已领取 -->
        <img
          v-else-if="property.obtainInfo?.isPersonalOutOfStock"
          :src="config.propertyConfigs.obtainedIcon"
          alt=""
          class="status-icon"
        >
        <img
          :src="propertyIcon"
          alt=""
          class="property-icon"
        >
        <p class="property-name">
          {{ property.obtainInfo?.property.instanceInfo.name }}
        </p>
      </div>
      <div
        class="threshold"
        :style="thresholdStyle"
      >
        <span style="transform: translateY(0.01rem);">{{ property.threshold }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import type { ObtainProperty } from '@tencent/moka-ui-domain';

import { THRESHOLD_STYLE } from '../const';
import type { ComponentConfig } from '../types';

const props = defineProps<{
  property: ObtainProperty;
  config: ComponentConfig;
  reachedThreshold: boolean;
  isBigPrize?: boolean;
}>();

const propertyStyle = computed(() => ({
  marginRight: props.isBigPrize ? 0 : '0.07rem',
}));
// 物品图片
const propertyIcon = computed(() => props.property.obtainInfo?.property.instanceInfo.pic_url
  || props.config.propertyConfigs.defaultPropertyIcon);

// 不可领取: 门槛未达到、已领取、已领完
const isDisabled = computed(() => {
  const { isGlobalOutOfStock = false, isPersonalOutOfStock = false } = props.property.obtainInfo || {};
  return !props.reachedThreshold || isGlobalOutOfStock || isPersonalOutOfStock;
});
// 积分门槛样式
const thresholdStyle = computed(() => {
  if (props.isBigPrize) {
    return THRESHOLD_STYLE.bigPrize;
  }

  if (isDisabled.value) {
    return THRESHOLD_STYLE.disable;
  }

  return THRESHOLD_STYLE.canObtain;
});
</script>
<style lang="scss" scoped>
.property-container {
  display: flex;
  flex-direction: column;
}
.property {
  position: relative;
  width: 0.7rem;
  height: 0.94rem;
  border-radius: 0.08rem;
  overflow: hidden;
  flex-shrink: 0;
  &:last-of-type {
    margin-right: 0;
  }
  .overlay {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.12);
  }
  &-info {
    height: 0.7rem;
    width: 100%;
    background-color: rgba(255, 242, 226, 1);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-direction: column;
    padding-bottom: 0.08rem;
  }
  &-icon {
    width: 0.3rem;
    height: 0.3rem;
  }
  &-name {
    width: 90%;
    font-weight: 500;
    margin-top: 0.06rem;
    font-size: 0.1rem;
    line-height: 0.1rem;
    letter-spacing: -0.0024rem;
    color: rgba(0, 0, 0, 0.8);
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .status-icon {
    position: absolute;
    right: 0;
    top: 0;
    width: 0.16rem;
    height: 0.16rem;
  }
  .threshold {
    height: 0.24rem;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'YYB';
    font-size: 0.12rem;
    font-weight: 400;
    line-height: 0.18rem;
    text-align: center;
  }
  &.big-prize {
    border: 1px solid rgba(255, 208, 194, 1);
    .property-info {
      background-image: url('https://yyb.qpic.cn/moka-imgs/1741772883146-le7k05ll0on.png');
      background-size: 100% 100%;
      background-color: #FFEFEF;
    }
  }
}
</style>
