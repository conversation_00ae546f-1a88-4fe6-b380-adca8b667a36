<template>
  <div
    class="point-progress-bar"
    :style="progressStyle"
  >
    <div
      class="active-progress-bar"
      :style="activeProgressStyle"
    >
      <div
        class="triangle"
        :style="pointsTriangleStyle"
      />
    </div>
    <div
      v-for="(property) in properties"
      :key="property.obtainID"
      class="dot-container"
    >
      <div
        :class="['dot', {
          active: ladderPoints >= Number(property.threshold),
        }]"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { CSSProperties } from 'vue';
import { type ObtainProperty } from '@tencent/moka-ui-domain';

defineProps<{
  ladderPoints: number;
  properties: ObtainProperty[];
  progressStyle: CSSProperties;
  activeProgressStyle: CSSProperties;
  pointsTriangleStyle?: CSSProperties;
}>();
</script>
<style lang="scss" scoped>
.point-progress-bar {
  position: relative;
  margin-top: 0.08rem;
  width: 100%;
  height: 0.04rem;
  background-color: rgba(231, 231, 231, 1);
  display: flex;

  .active-progress-bar {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(238, 60, 56, 1);

    .triangle {
      position: absolute;
      bottom: -0.08rem;
      right: -0.04rem;
      width: 0;
      height: 0;
      border: 0.05rem solid transparent;
      border-bottom-color: rgba(255, 231, 186, 1);
    }
  }

  .dot-container {
    position: relative;
    width: 0.77rem;
    height: 0.08rem;

    &:last-child {
      width: 0.7rem;
    }

    .dot {
      position: absolute;
      left: 0.31rem;
      top: -0.02rem;
      width: 0.08rem;
      height: 0.08rem;
      border-radius: 50%;
      background-color: rgba(231, 231, 231, 1);

      &.active {
        background-color: rgba(238, 60, 56, 1);
      }
    }
  }
}
</style>
