<template>
  <van-popup
    v-model:show="show"
    position="center"
    class="obtain-result-popup"
    :close-on-click-overlay="false"
    :style="{
      width: '2.96rem',
      minHeight: '2.44rem',
      background: '#fff',
      borderRadius: '0.16rem',
      padding: '0.24rem 0.16rem 0.16rem',
    }"
    @closed="onClose"
  >
    <div
      class="close"
      @click="show = false"
    />
    <div class="title">
      领取成功
    </div>
    <div class="description">
      请在已领福利内查看
    </div>
    <div class="properties">
      <div
        v-for="property,index in showingProperties"
        :key="index"
        class="property-box"
      >
        <div
          class="property-icon-box"
          :class="{ folded: showFoldedStyle && index !== 0}"
        >
          <img
            :src="property.property.instanceInfo.pic_url"
            alt=""
            class="icon"
          >
        </div>
        <div
          v-if="showPropertyName"
          class="name"
        >
          {{ property?.property.instanceInfo.name }}
        </div>
      </div>
      <div
        v-if="showFoldedStyle"
        class="property-icon-box folded amount"
      >
        +{{ amount - 5 }}
      </div>
    </div>
    <div
      v-if="!showPropertyName"
      class="name overflow-name"
    >
      已领取{{ amount }}个道具
    </div>
    <div
      class="confirm"
      @click="show = false"
    >
      确认
    </div>
  </van-popup>
</template>
<script lang="ts" setup>
import 'vant/es/popup/style';
import { Popup as VanPopup } from 'vant';
import { computed, ref } from 'vue';
import type { ObtainInfo } from '@tencent/moka-ui-domain';

const props = defineProps<{
  properties: ObtainInfo[];
  onClose: () => void;
}>();

const show = ref(true);

const amount = computed(() => props.properties.length);
// 最多展示 5 个商品
const showingProperties = computed(() => props.properties.slice(0, 5));
// 不超过 5 个物品时显示物品名字
const showPropertyName = computed(() => amount.value < 5);
// 超过 5 个展示折叠样式
const showFoldedStyle = computed(() => amount.value > 5);
</script>

<!-- popup 最外部样式不生效 -->
<style lang="scss" scoped>
.obtain-result-popup {
  .close {
    width: 0.24rem;
    height: 0.24rem;
    background: url('https://yyb.qpic.cn/moka-imgs/1730704431213-9zxpep5s9kf.png');
    background-size: 100% 100%;
    position: absolute;
    top: 0.1rem;
    right: 0.1rem;
  }
  .title {
    font-family: Noto Sans CJK SC;
    font-size: 0.18rem;
    font-weight: 700;
    line-height: 0.28rem;
    text-align: center;
    color: rgba(15, 15, 15, 1);
  }
  .description {
    font-family: Noto Sans CJK SC;
    font-size: 0.12rem;
    font-weight: 400;
    line-height: 0.18rem;
    text-align: center;
    color: rgba(15, 15, 15, 0.65);
  }
  .properties {
    display: flex;
    justify-content: space-evenly;
    margin-top: 0.16rem;
  }
  .property-icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.48rem;
    height: 0.48rem;
    background: rgba(15, 15, 15, 0.05);
    border-radius: 0.08rem;
    flex-shrink: 0;
    border: 0.02rem solid rgba(255, 255, 255, 1);
    &.folded {
      margin-left: -0.08rem;
    }
    &.amount {
      font-family: YYB;
      font-size: 0.2rem;
      font-weight: 400;
      line-height: 0.2786rem;
    }
    .icon {
      width: 0.3493rem;
      height: 0.35rem;
    }
  }
  .name {
    margin-top: 0.04rem;
    font-family: Noto Sans CJK SC;
    font-size: 0.12rem;
    font-weight: 400;
    line-height: 0.18rem;
    text-align: center;
    color: #000;
    width: 0.48rem;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  .overflow-name {
    width: 100%;
  }
  .confirm {
    margin-top: 0.24rem;
    width: 2.64rem;
    height: 0.48rem;
    background-color: rgba(238, 60, 56, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-family: Noto Sans CJK SC;
    font-size: 0.16rem;
    font-weight: 500;
    line-height: 0.24rem;
    border-radius: 0.24rem;
  }
}
</style>
