import { type App, createApp } from 'vue';
import type { ObtainInfo } from '@tencent/moka-ui-domain';

import ObtainResultPopup from './obtain-result-popup.vue';

export default ObtainResultPopup;

let instance: App | undefined;
export function showObtainResultPopup(properties: ObtainInfo[]) {
  if (instance) return;

  const root = document.createElement('div');

  function removePopup() {
    instance?.unmount();
    instance = undefined;
    root.remove();
  }

  document.querySelector('body')?.appendChild(root);

  instance = createApp(ObtainResultPopup, {
    properties,
    onClose: () => {
      removePopup();
    },
  });

  instance.mount(root);
}
