import { type App, createApp } from 'vue';

import Progress from './progress.vue';

interface ProgressData {
  show?: boolean;
  percent?: number;
}

/** 进度条弹窗 */
export default class ProgressPopup {
  private root!: HTMLElement;
  private app!: App;
  private instance!: any;
  private endCallBack?: () => void;
  constructor() {
    if (!this.instance) {
      this.initInstance();
    }
  }

  /** 设置进度 */
  public setPercent(percent: number) {
    this.setData({
      percent,
    });
  }

  /** 结束加载并设置结束回调 */
  public endLoadingAndSetEndCB(endCallBackFn: () => void) {
    this.setData({
      percent: 100,
      show: false,
    });

    this.endCallBack = endCallBackFn;
  }

  /** 更改组件数据 */
  private setData(newData: ProgressData) {
    if (!this.instance) return;

    Object.keys(newData).forEach((key) => {
      this.instance[key] = newData[key as keyof ProgressData];
    });
  }

  /** 初始化组件实例 */
  private initInstance() {
    this.root = document.createElement('div');
    document.querySelector('body')?.appendChild(this.root);
    this.app = createApp(Progress, {
      onClose: () => {
        this.removePopup();
        this.endCallBack?.();
      },
    });

    this.instance = this.app.mount(this.root);
  }

  /** 移除弹窗 */
  private removePopup() {
    this.app?.unmount();
    this.instance = undefined;
    this.root?.remove();
  }
}
