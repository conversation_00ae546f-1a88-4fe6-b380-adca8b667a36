<template>
  <van-popup
    v-model:show="show"
    class="loading-wrapper"
    position="bottom"
    :close-on-click-overlay="false"
    :style="{
      height: '2.38rem',
      padding: '0.75rem 0.84rem 0',
      borderRadius: '0.16rem 0.16rem 0 0',
    }"
    @closed="onClose"
  >
    <div class="loading-text">
      奖励领取中，请稍后～
    </div>
    <div class="progress-wrapper">
      <div
        class="progress-inner"
      >
        <div
          class="progress-portion"
          :style="`width: ${percent}%`"
        />
      </div>
      <div class="progress-text">
        领取中: {{ percent }}%
      </div>
    </div>
  </van-popup>
</template>

<script lang='ts' setup>
import 'vant/es/popup/style';
import { Popup as VanPopup } from 'vant';
import { ref } from 'vue';

defineProps<{
  onClose: () => void;
}>();

const show = ref(true);
const percent = ref(0);

defineExpose({
  show,
  percent,
});
</script>

<style lang="scss" scoped>
.loading-wrapper {
  .loading-text {
    font-family: Noto Sans CJK SC;
    font-size: 0.16rem;
    color: #000;
    margin-bottom: 0.34rem;
    text-align: center;
    font-weight: 500;
  }

  .progress-wrapper {
    text-align: center;

    .progress-inner {
      height: 0.06rem;
      background: rgba(245, 245, 245, 1);
      border-radius: 0.03rem;
      width: 100%;
      position: relative;

      .progress-portion {
        position: absolute;
        left: 0;
        border-radius: 0.03rem;
        background: rgba(238, 60, 56, 1);
        height: 0.06rem;
      }
    }

    .progress-text {
      margin-top: 0.24rem;
      font-family: Noto Sans CJK SC;
      font-size: 0.14rem;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
</style>
