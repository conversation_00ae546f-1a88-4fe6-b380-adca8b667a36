<template>
  <div class="container">
    <div
      class="received-points"
      :style="receivedPointsStyle"
    >
      <p class="points">
        已领<span class="points-value">{{ receivedPoints }}</span>积分
      </p>
      <van-icon
        class="arrow"
        name="arrow"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import 'vant/es/icon/style';
import { Icon as VanIcon } from 'vant';
import type { CSSProperties } from 'vue';

defineProps<{
  receivedPoints: number;
  receivedPointsStyle: CSSProperties;
}>();
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  min-height: 0.23rem;
}
.received-points {
  position: absolute;
  margin-top: 0.06rem;
  display: flex;
  align-items: center;
  padding: 0.01rem 0.04rem 0;
  height: 0.16rem;
  background-color: rgba(255, 231, 186, 1);
  border-radius: 0.03rem;
  width: max-content;
  .points {
    font-family: Noto Sans CJK SC;
    font-weight: 500;
    font-size: 0.1rem;
    letter-spacing: 0;
    color: rgba(140, 91, 49, 1);
    margin-right: 0.02rem;
    .points-value {
      font-family: YYB;
      font-weight: 400;
      color: rgba(244, 131, 25, 1);
    }
  }
  .arrow {
    font-weight: 500;
    font-size: 0.1rem;
    color: rgba(140, 91, 49, 1);
  }
}
</style>
