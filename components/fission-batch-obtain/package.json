{"name": "@tencent/moka-ui-fission-batch-obtain", "version": "0.0.11", "description": "活动中台组件模板", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "scripts": {"dev": "vite"}, "author": "pcerypeng<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-fission-batch-obtain"], "repository": {"type": "git", "url": "", "directory": "components/fission-batch-obtain"}, "dependencies": {"@tencent/moka-athena-next-v2": "latest", "@tencent/moka-data-core": "^1.0.39", "@tencent/moka-ui-domain": "moka-beta", "@tencent/mole-report": "^1.0.0", "vant": "^4.6.8", "vue": "^3.3.4"}, "devDependencies": {"husky": "^8.0.3", "sass": "^1.43.4", "turbo": "^1.5.6"}}