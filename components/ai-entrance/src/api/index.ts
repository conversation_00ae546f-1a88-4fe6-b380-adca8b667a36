import { accessRequester } from '@tencent/moka-data-core';
import { utils } from '@tencent/moka-ui-domain';

/** 接入层鉴权 */
const BUSINESS_ID = 'yyb_travel_aigc';
const ACCESS_KEY = 'e77rPxDnN5l6pibUd7yNsoFIfFvJS';

interface BaseResp {
  code: number;
  tip: string;
  /** 和具体接口有关 */
  body: any;
}

/**
 * 获取用户的AI完成信息
 * @param data 接口参数
 * @returns 用户的AI完成信息
 */
export async function getUserFinishStatus(data: unknown): Promise<BaseResp> {
  const resp = await accessRequester.request({
    cmd: 'get_task_num',
    businessID: BUSINESS_ID,
    accessKey: ACCESS_KEY,
    needAuthHeader: true,
    isTest: utils.isTestEnv(),
    data,
  });

  return resp as BaseResp;
}
