import { type Field } from '@formily/core';
import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 状态文案和背景图联动校验 —— 文案和背景图必须配一个
 * @param field 字段
 * @param linkageField 联动字段名
 */
const textBgImageValidator = (field: Field, linkageField: string) => {
  const curField = field;
  if (!field.value && !field.query(linkageField).value()) {
    curField.selfErrors = ['文案和背景图必须配一个'];
  } else {
    curField.selfErrors = [];
  }
};

/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      aiAppId: {
        type: 'string',
        title: 'AI应用appId',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-validator': [
          { required: true, message: 'AI应用appId' },
        ],
      },
      aiTitle: {
        type: 'string',
        title: 'AI标题',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-validator': [
          { required: true, message: 'AI标题' },
        ],
      },
      sourceScene: {
        type: 'string',
        title: 'AI场景来源',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-validator': [
          { required: true, message: 'AI场景来源' },
        ],
      },
      previewStatus: {
        type: 'number',
        title: '预览组件状态',
        default: 'unBook',
        enum: [
          {
            label: '未完成',
            value: 'unFinish',
          },
          {
            label: '已完成',
            value: 'finish',
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
      voidUnFinishBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '未完成状态配置',
        },
        properties: {
          unFinishTitle: {
            type: 'string',
            title: '按钮文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-reactions': field => textBgImageValidator(field, 'unFinishBgImage'),
          },
          unFinishFontColor: {
            type: 'string',
            title: '文案颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          unFinishBgImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-reactions': field => textBgImageValidator(field, 'unFinishTitle'),
          },
          unFinishBgColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
      voidFinishBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '已完成状态配置',
        },
        properties: {
          finishTitle: {
            type: 'string',
            title: '按钮文案',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-reactions': field => textBgImageValidator(field, 'finishBgImage'),
          },
          finishFontColor: {
            type: 'string',
            title: '文案颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
          finishBgImage: {
            type: 'string',
            title: '背景图',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-reactions': field => textBgImageValidator(field, 'finishTitle'),
          },
          finishBgColor: {
            type: 'string',
            title: '背景颜色',
            'x-decorator': 'FormItem',
            'x-component': 'ColorPocker',
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
