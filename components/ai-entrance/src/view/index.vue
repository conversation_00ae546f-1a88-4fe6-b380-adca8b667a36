<template>
  <button
    v-report="{ is_finish: isFinish ? 1 : 0 }"
    :dt-cmd="`hold=${holdReport}`"
    dt-eid="ai-entrance"
    :style="buttonStyle"
    @click="handleOpen"
  >
    {{ buttonTitle }}
  </button>
</template>

<script setup lang="ts">
import Cookies from 'js-cookie';
import { throttle } from 'radash';
import { computed, onMounted, ref, toRefs } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import MoleJSBridge from '@tencent/mole-jsbridge';

import {
  getUserFinishStatus as getUserFinishStatusApi,
} from '../api';
import {
  NEED_IN_YYB,
  SUCCESS_CODE,
  YYB_VERSION_880,
} from '../constant';
import {
  type Config,
  ShowStatus,
} from '../types';
import {
  checkYybVersionGreaterThanVersionCode,
} from '../utils';

const { getLoginUserOpenID } = utils;
const { toast, inMagic, openLogin } = hooks.useMokaInject();
const moleJSBridge = MoleJSBridge.getJSBridge();
const holdReport = ref(true);

const props = defineProps<{
  config: Config;
}>();
const { config } = toRefs(props);

const actID = (globalThis as any).magicUiconfig?.[0]?.actId;
/** 场景id */
const sceneID = `${actID}_${config.value.id}`;
/** 用户是否完成 */
const isFinish = ref<boolean>(false);

/** 是否应用宝有效版本880 */
const isValidVersion = moleJSBridge?.appEnv === 'yyb' && checkYybVersionGreaterThanVersionCode(YYB_VERSION_880);

const buttonTitle = computed(() => {
  if (inMagic) {
    return config.value[`${config.value.previewStatus}Title`];
  }

  const { unFinishTitle, finishTitle } = config.value;
  return isFinish.value ? finishTitle : unFinishTitle;
});

const buttonStyle = computed(() => {
  if (inMagic) {
    return getStyle(config.value.previewStatus);
  }

  return isFinish.value ? getStyle(ShowStatus.Finish) : getStyle(ShowStatus.UnFinish);
});

const getStyle = (prefix: string) => ({
  backgroundColor: config.value[`${prefix}BgColor`],
  backgroundImage: `url(${config.value[`${prefix}BgImage`]})`,
  color: config.value[`${prefix}FontColor`],
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
});

onMounted(async () => {
  if (inMagic) {
    return;
  }

  if (moleJSBridge?.appEnv !== 'yyb') {
    toast?.(NEED_IN_YYB);
    return;
  }

  if (getLoginUserOpenID()) {
    await init();
  }

  holdReport.value = false;
});

/**
 * 组件初始化
 */
const init = async () => {
  await getUserFinishStatus();
  moleJSBridge?.event?.on('pageAppear', () => {
    void getUserFinishStatus();
  });
};

/** 点击事件 */
const handleOpen = throttle({ interval: 500 }, async () => {
  if (!getLoginUserOpenID()) {
    openLogin?.();
    return;
  }

  if (!isValidVersion) {
    toast?.('当前版本过低，请先更新应用宝哦～');
    return;
  }

  const { aiAppId, aiTitle } = config.value;

  // 打开AI制作页
  const urlParams = new URLSearchParams(location.search);
  const scene = urlParams.get('scene') || '10580'; // 10580 为活动默认的 H5 场景值
  const finishParams = new URLSearchParams({
    app_id: aiAppId,
    title: aiTitle,
    scene_id: sceneID,
    page: 'style_detail',
    type: 'game_filter',
    preActivityTagName: scene,
  });

  location.href = `tmast://ai/common?${finishParams.toString()}`;
});

/**
 * 获取用户完成状态
 */
const getUserFinishStatus = async () => {
  const params = {
    header: {
      appid: config.value.aiAppId,
      request_id: `ai-entrance-${actID}-${Math.floor(Math.random() * 100)}-${Date.now()}`, // 请求唯一ID
      scene_id: '1', // 固定值
      user: {
        user_id: getLoginUserOpenID(), // 用户openid
        user_nick: '', // 用户昵称，不传可以
      },
      device: {
        device_id: Cookies.get('guid') || '',
      },
      request_from: '', // h5不用传
    },
    scene: '', // 接口不传，即全场景统一数据
  };

  const { code, tip, body } = await getUserFinishStatusApi(params);
  if (code !== SUCCESS_CODE) {
    // 接口异常按无效处理（接口异常已在 data-core 上报）
    toast?.(tip);
    return;
  }

  isFinish.value = body?.count > 0;
};
</script>

<style lang="scss" src="./index.scss" scoped />
