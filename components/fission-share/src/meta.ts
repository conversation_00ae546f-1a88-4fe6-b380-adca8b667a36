import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      modId: {
        type: 'string',
        title: '模块选择',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'ModSelect',
        'x-component-props': {
          modType: 21,
        },
      },
      btnTitle: {
        type: 'string',
        title: '按钮文案',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        default: '邀请1个新注册领6888积分',
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
