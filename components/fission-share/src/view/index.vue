<template>
  <div
    dt-eid="button"
    :dt-params="utils.getReportParamsStr({
      mod_id: 'activity_principal_card',
      mod_title: '活动主功能卡',
      btn_id: 'invite_btn',
      btn_title: config.btnTitle,
    }, inMagic)"
    :dt-cmd="`hold=${fissionStore.isReportHeld}`"
    class="share"
    @click="shareBoost"
  >
    {{ config.btnTitle }}
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { hooks, store, utils } from '@tencent/moka-ui-domain';

import { useShare } from '../hooks';
import type { ComponentConfig } from '../types';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { inMagic } = hooks.useMokaInject();
const { useFissionBoostStore } = store;
const fissionStore = useFissionBoostStore();
const {
  shareBoost,
} = useShare(props.config);
</script>

<style lang="scss" src="./index.scss" scoped />
