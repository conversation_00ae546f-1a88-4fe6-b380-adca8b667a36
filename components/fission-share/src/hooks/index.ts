import { ref, watch } from 'vue';
import { BoostEvent, common, hooks, store, utils } from '@tencent/moka-ui-domain';
import bridge from '@tencent/txv-athena/bridge';
import { isQQ, isWechat } from '@tencent/txv-athena/helper';
import share from '@tencent/txv-athena/share';

import { ComponentConfig } from '../types';

/** 发起数值助力生成 shareKey */
async function launchValueBoost(
  componentID: string,
  modID: string,
) {
  const result = await common.exec<unknown, {share_key: string}|undefined>({
    componentID,
    modID: Number(modID),
    methodName: '/trpc.activity.boost_server.BoostServer/LaunchValueBoost',
    data: {
      boost_id: utils.getBoostId(modID),
    },
  });

  return result?.data;
}

export function useShare(config: ComponentConfig) {
  const shareOpt = ref();
  const { useFissionBoostStore } = store;
  const fissionStore = useFissionBoostStore();
  const { app, toast, $bus } = hooks.useMokaInject();

  const getShareKey = async () => {
    if (!await fissionStore.openVipCommonPopup()) return;
    const { id, modId } = config;
    const { share_key: shareKey } = await launchValueBoost(id, modId) || {};
    if (!shareKey) {
      toast?.('生成分享 id 失败');
      return;
    }

    fissionStore.myShareKey = shareKey;
    console.log('[launchValueBoost] shareKey', shareKey);
  };

  const modifyShareConfig = () => {
    const shareConfig = app?.shareConfig ?? {};
    shareConfig.link = utils.getShareUrl(fissionStore.myShareKey!);
    shareOpt.value = {
      ...shareConfig,
      singleTitle: shareConfig.timeLineTitle,
      img: shareConfig.imgUrl,
    };
  };

  const setShareConfig = async () => {
    const { myShareKey, openVipCommonPopup } = fissionStore;

    if (!await openVipCommonPopup()) {
      return;
    }

    console.log('[setShareConfig myShareKey]', myShareKey);
    if (!myShareKey) {
      await getShareKey();
      if (!fissionStore.myShareKey) {
        return;
      }
    }

    modifyShareConfig();
    console.log('[setShareConfig][fission-boost]', shareOpt.value);
    return true;
  };

  const shareBoost = async () => {
    if (!await fissionStore.openVipCommonPopup()) {
      return;
    }

    if (!(await setShareConfig())) {
      return;
    }

    if (!shareOpt.value?.link) {
      toast?.('获取分享链接失败，请稍后刷新重试~');
      return;
    }

    share(shareOpt.value);

    if (bridge.openToolsDialog) {
      // iwan-sdk 历史逻辑
      setTimeout(() => {
        bridge.openToolsDialog();
      }, 200);

      return;
    }

    if (isWechat() || isQQ()) {
      toast?.('请点击右上角 ... 进行分享');

      return;
    }
  };

  $bus?.$on(BoostEvent.OpenFissionShare, () => {
    void shareBoost();
  });

  watch([fissionStore.hasAuthorized, fissionStore.hasActivatedVip], ([curAuthStatus], [curActivateStatus]) => {
    if (curAuthStatus && curActivateStatus) {
      modifyShareConfig();
    }
  }, { immediate: true });

  return {
    shareBoost,
  };
}
