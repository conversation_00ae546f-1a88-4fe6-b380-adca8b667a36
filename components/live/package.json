{"name": "@tencent/moka-ui-live", "version": "0.0.3", "description": "活动中台组件模板", "main": "./dist/index.es.js", "scripts": {"dev": "vite"}, "author": "clintlin<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-live"], "repository": {"type": "git", "url": "", "directory": "components/live"}, "dependencies": {"@tencent/mole-report": "^1.0.0", "@tencent/txv-utils": "^1.2.1", "vue": "^3.3.4"}, "devDependencies": {"@tmagic/form": "^1.3.10", "husky": "^8.0.3", "sass": "^1.43.4", "turbo": "^1.5.6", "vite-plugin-css-injected-by-js": "^3.5.0"}}