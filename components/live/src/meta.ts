import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 采用 Formily 协议的表单配置
 * https://designable-antd.formilyjs.org/
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      liveType: {
        title: '直播类型',
        name: '直播类型',
        'x-component': 'Select',
        'x-decorator': 'FormItem',
        enum: [
          {
            children: [],
            label: '腾讯视频',
            value: 1,
          },
          {
            children: [],
            label: '虎牙直播',
            value: 2,
          },
        ],
      },
      bqf6f83h78m: {
        type: 'void',
        'x-component': 'Card',
        'x-component-props': {
          title: '腾讯视频',
        },
        'x-reactions': {
          dependencies: [
            {
              property: 'value',
              type: 'any',
              source: 'liveType',
              name: 'liveType',
            },
          ],
          fulfill: {
            state: {
              visible: '{{  $deps.liveType === 1 }}',
            },
          },
        },
        properties: {
          pid: {
            type: 'string',
            title: '直播pid',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            name: 'pid',
          },
          liveId: {
            type: 'string',
            title: '直播流liveId',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            name: 'liveId',
          },
        },
      },
      bqf6f83h79m: {
        type: 'void',
        'x-component': 'Card',
        'x-component-props': {
          title: '虎牙直播配置',
        },
        'x-reactions': {
          dependencies: [
            {
              property: 'value',
              type: 'any',
              source: 'liveType',
              name: 'liveType',
            },
          ],
          fulfill: {
            state: {
              visible: '{{  $deps.liveType === 2 }}',
            },
          },
        },
        properties: {
          huyaId: {
            type: 'string',
            title: '虎牙直播ID',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            name: 'huyaId',
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
