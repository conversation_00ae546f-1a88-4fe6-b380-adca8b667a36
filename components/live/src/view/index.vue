<template>
  <div class="videoLiv-box">
    <iframe
      v-if="config.liveType === LiveType.HUYA_LIVE"
      ref="iframeRef"
      scrolling="no"
      :src="`https://liveshare.huya.com/iframe/${config.huyaId}`"
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps, inject, onMounted, watch } from 'vue';
import { loadScript } from '@tencent/txv-utils';

import { LiveType } from '../constant';

let player: { on: (arg0: string, arg1: () => void) => void; destroy: () => void } | null;

const props = defineProps<{
  config: {
    liveType: LiveType;
    pid?: string;
    liveId?: string;
    huyaId?: string;
    id: string;
  };
}>();

const inMagic = inject('inMagic');

watch(() => props.config.liveType, (value) => {
  if (!inMagic) return;

  destroyTVPlayer();
  if (value === LiveType.HUYA_LIVE) return;
  initTVPlayer();
});

const createPlayerInstance = () => {
  const { config: { id, pid, liveId } } = props;
  const config = {
    containerId: id,
    vid: liveId,
    livepid: pid,
    width: '100%',
    height: '100%',
    autoplay: false,
  };
  player = new window.TxvLive(config);
  player?.on('ready', () => {
    console.log('player has been already.');
  });
};

const initTVPlayer = () => {
  if (window.TxvLive) {
    createPlayerInstance();
    return;
  }

  loadScript('//vm.gtimg.cn/tencentvideo/txvlive/2017/txvlive.js', () => {
    createPlayerInstance();
  });
};

const destroyTVPlayer = () => {
  player?.destroy();
  player = null;
};

onMounted(() => {
  if (props.config.liveType === LiveType.HUYA_LIVE) return;

  initTVPlayer();
});
</script>

<style lang="scss">
.videoLiv-box {
  height: 100%;

  .videoLive-box-con {
    height: 100%;
  }

  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>
