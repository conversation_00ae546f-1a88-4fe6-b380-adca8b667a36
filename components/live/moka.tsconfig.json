{"compilerOptions": {"target": "es5", "module": "esnext", "strictNullChecks": true, "esModuleInterop": true, "allowSyntheticDefaultImports": false, "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "alwaysStrict": true, "skipLibCheck": true, "disableSolutionSearching": true, "baseUrl": ".", "moduleResolution": "node"}, "include": ["src", "*.d.ts"], "exclude": ["node_modules"]}