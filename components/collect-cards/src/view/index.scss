.collect-cards {
  width: 3.47rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  &-main {
    width: 100%;
    position: relative;
    box-sizing: content-box;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    height: 1.8rem;
    &__header {
      position: relative;
      width: fit-content;
      margin: 0 auto;
      text-align: center;
      height: 0.19rem;
      white-space: nowrap;
      font-size: 0.12rem;
      line-height: 0.14rem;
      font-weight: 800;
      letter-spacing: 0.005rem;
      margin-bottom: 0.12525rem;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
      &-container {
        width: fit-content;
        padding: 0.02rem 0.3rem;
      }
    }

    &__container {
      position: relative;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: center;
      overflow-x: scroll;
      overflow-y: hidden;
      padding: 0 0.16rem;
    }

    &__container.with-scrollbar {
      justify-content: flex-start;
    }
  }

  .collect-cards-main__container::-webkit-scrollbar {
    display: none;
  }

  &__status-tips {
    margin-top: 0.0875rem;
    font-size: 0.1rem;
    font-weight: 400;
  }

  .overlay-mask::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    height: 1.35rem;
    width: 0.66rem;
    background: var(--mask-background);
    opacity: 1;
    transition: opacity 0.5s;
    /** 允许拖动遮罩区域滚动底部*/
    pointer-events: none;
  }

  .overlay-mask.no-overlay::after {
    /* 当没有遮罩时隐藏伪元素 */
    opacity: 0;
  }
}
