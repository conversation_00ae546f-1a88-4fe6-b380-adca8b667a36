<template>
  <div class="collect-cards">
    <div
      class="collect-cards-main"
      :class="{
        'overlay-mask': isNeedScrollMask,
        'no-overlay': !isShowOverlay
      }"
      :style="mainStyle"
    >
      <div
        class="collect-cards-main__header"
        :style="processStyle"
      >
        <div
          class="collect-cards-main__header-container"
        >
          我的集卡<span
            class="current"
            :style="currentProcessStyle"
          >{{ collectedCardsProgress }}</span>/{{ totalCardsNum }}张
        </div>
      </div>
      <div
        ref="mainContainerRef"
        class="collect-cards-main__container"
        :class="{ 'with-scrollbar': hasScrollBar }"
      >
        <card-item
          v-for="cardInfo in cardInfos"
          :key="`${cardInfo.cardConfig.cardId}-${cardInfo.cardStatus}`"
          :is-finished="isFinished"
          :card-info="cardInfo"
          :send-btn-configs="sendBtnConfigs"
          :ask-btn-configs="askBtnConfigs"
          :card-item-configs="props.config.cardItemConfigs"
          @ask="askCard"
          @send="sendCard"
        />
      </div>
    </div>
    <div
      v-if="inMagic || cardInfos"
      class="collect-cards__status-tips"
      :style="statusStyle"
    >
      {{ tips }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, getCurrentInstance, onMounted, onUnmounted, ref } from 'vue';
import { actUtils } from '@tencent/moka-data-core';
import { hooks, utils } from '@tencent/moka-ui-domain';
import moleJsbridge from '@tencent/mole-jsbridge';

import CardItem from '../components/card-item/index.vue';
import { CARD_SEND_KEY, CARD_SHARE_TYPE, COMPONENT_EVENT, COMPONENT_METHODS, MOLE_JSBRIDGE_EVENT_NAME, USER_NAME } from '../constant';
import { ReceiveSendCardStatus, useCollectCardsHook } from '../hooks/use-collect-cards-hook';
import { sendPopupConfigsKeyByStatus, usePopupHook } from '../hooks/use-popup-hook';
import { useScrollMask } from '../hooks/use-scroll-mask';
import { DATONG_RPORT_ID } from '../report';
import type { ComponentConfig, ShareData } from '../types';
import { CardShareType, UserCollectCardStatus } from '../types';
import { getCollectID, handleUserName, replaceTemplate, withEmptyString } from '../utils';

const { getLoginUserOpenID, getUserNameInfo } = utils;

const { isLogin, inMagic, $bus, app, toast } = hooks.useMokaInject();
const { reporter } = hooks.useReport();

const props = defineProps<{
  config: ComponentConfig;
}>();

const reportModule = (props.config as any).report?.module?._module;
const baseReportData = {
  _module: reportModule,
  mod_title: reportModule,
  btn_title: reportModule,
};

const jsbridgeInstance = moleJsbridge.getJSBridge();
const instance = getCurrentInstance();

/** 初始化状态事件 */
const initStatusEvent = (status: UserCollectCardStatus) => {
  $bus?.$emit(COMPONENT_EVENT[status], instance?.proxy);
};

/** 集卡区域主容器 */
const mainContainerRef = ref<HTMLElement>();
/** 是否已完成 */
const isFinished = computed(() => status.value === UserCollectCardStatus.Finished);
/** 当前集卡进度 */
const collectedCardsProgress = computed(() => {
  // 若当前集卡进度为已完成，则需要满进度
  if (isFinished.value) {
    return totalCardsNum.value;
  }

  return curCollectedNum.value;
});

const {
  cardInfos,
  curCollectedNum,
  totalCardsNum,
  status,
  lotteryReward,
  getAskCardURL,
  getSendCardURL,
  initCollectCards,
  receiveSendCard,
  sendAskCard,
  getShowSendAskCardPopupConfig,
  getUserCollectCards,
} = useCollectCardsHook(
  {
    componentID: props.config.id,
    modID: props.config.modId,
    lotteryModID: props.config.lotteryModID,
  },
  initStatusEvent,
  toast,
);

const { showSendOrAskCardPopup } = usePopupHook(props.config.popupConfigs);

const { hasScrollBar, isShowOverlay, initScrollMask } = useScrollMask(mainContainerRef);
const isNeedScrollMask = computed(() => props.config.isNeedScrollMask);
// 当需要滚动遮罩时才兼容容器滚动
if (isNeedScrollMask.value) {
  initScrollMask();
}

/** 索要按钮配置 */
const askBtnConfigs = computed(() => props.config.askBtnConfigs);
/** 赠送按钮配置 */
const sendBtnConfigs = computed(() => props.config.sendBtnConfigs);
/** 提示文案 */
const tips = computed(() => {
  const { collectingTips, collectedTips, finishedTips } = props.config;
  // 收集中状态或者在编辑器中，展示收集中提示文案
  if (inMagic || status.value === UserCollectCardStatus.Collecting) {
    return withEmptyString(collectingTips?.replaceAll('$count', `${totalCardsNum.value - curCollectedNum.value}`));
  }

  // 收集完成状态，展示收集完成提示文案
  if (status.value === UserCollectCardStatus.Collected) {
    return withEmptyString(collectedTips);
  }

  // 完成任务状态，展示完成任务提示文案
  if (status.value === UserCollectCardStatus.Finished) {
    if (!lotteryReward.value) {
      return '恭喜你，已领取奖励~';
    }

    return withEmptyString(finishedTips?.replace('$count', `${lotteryReward.value}`));
  }

  return '';
});
/** 集卡主要区域样式 */
const mainStyle = computed(() => {
  const { mainBackgroundColor, mainBackgroundImage, maskBeginColor, maskEndColor } = props.config;
  const maskBg = !maskBeginColor || !maskEndColor
    ? 'linear-gradient(to left, rgba(24, 29, 39, 1), rgba(15,15,15,0))'
    : `linear-gradient(to right, ${maskBeginColor}, ${maskEndColor})`;

  return {
    backgroundColor: mainBackgroundColor,
    backgroundImage: `url(${withEmptyString(mainBackgroundImage)})`,
    '--mask-background': maskBg,
  };
});
/** 当前进度样式 */
const currentProcessStyle = computed(() => {
  const { processConfigs } = props.config;
  return {
    color: processConfigs.currentTextColor,
  };
});
/** 集卡总进度样式 */
const processStyle = computed(() => {
  const { processConfigs } = props.config;
  return {
    color: processConfigs.textColor,
    backgroundImage: `url(${withEmptyString(processConfigs.backgroundImage)})`,
  };
});
/** 状态提示样式 */
const statusStyle = computed(() => {
  const { statusTipsColor } = props.config;
  return {
    color: statusTipsColor,
  };
});

/**
 * 索要卡片
 * @param data 分享出去的数据
 */
const askCard = (data: ShareData) => {
  const { cardID } = data;
  const username = getUserNameInfo(getLoginUserOpenID())?.nickname ?? '';
  const shareURL = getAskCardURL(cardID, username);
  share(shareURL, username, data);
};

/**
 * 赠送卡片
 * @param data 分享出去的数据
 */
const sendCard = async (data: ShareData) => {
  const { cardID } = data;
  const username = getUserNameInfo(getLoginUserOpenID())?.nickname ?? '';
  const shareURL = await getSendCardURL(cardID, username);

  // URL 为空不执行后面逻辑
  if (!shareURL) {
    console.error('[share] 分享 URL 为空', data, shareURL);
    return;
  }

  share(shareURL, username, data);
};

/**
 * 分享
 * @param url 分享 URL
 * @param username 用户名
 * @param data 分享数据
 */
const share = (url: string, username: string, data: ShareData) => {
  const { shareContent, shareTitle, cardName } = data;
  console.log('[share] 分享', data, url);
  void jsbridgeInstance?.ui?.share({
    shareInfo: {
      /** 分享标题 */
      title: replaceTemplate(shareTitle, { username: handleUserName(username), cardName }),
      /** 分享描述 */
      desc: replaceTemplate(shareContent, { username: handleUserName(username), cardName }),
      /** 分享图标链接 */
      icon: props.config.shareIcon,
      /** 分享链接 */
      url,
      /** 允许分享 */
      allowShare: true,
    },
  });
};

/** 初始化 */
const init = async () => {
  // 编辑器中不进行初始化
  if (inMagic) {
    return;
  }

  // 未登录
  if (!getLoginUserOpenID()) {
    console.warn('[initCollectCards] 用户未登录');
    return;
  }

  const urlParams = new URLSearchParams(location.search);
  const shareType = urlParams.get(CARD_SHARE_TYPE) as (CardShareType | null);
  // 主态初始化
  if (!shareType) {
    void initCollectCards();
    return;
  }

  const { withSelectGoods } = props.config;
  if (withSelectGoods) {
    return;
  }

  const shareKey = urlParams.get(CARD_SEND_KEY) || '';
  const username = urlParams.get(USER_NAME) || '他人';
  await initSendOrAsk(shareType, username, shareKey);
};

/**
 * 初始化赠送和索要弹窗
 * @param shareType 分享类型
 * @param username 用户名
 * @param shareKey 分享 key（赠送需要）
 */
const initSendOrAsk = async (
  shareType: CardShareType,
  username: string,
  shareKey: string,
) => {
  console.log('[initSendOrAsk] 初始化赠送或索要弹窗');
  // 赠送弹窗
  if (Number(shareType) === CardShareType.Send) {
    await initSendStatus(shareKey, username);
    return;
  }

  // 索要弹窗
  if (Number(shareType) === CardShareType.Ask) {
    await initAskStatus(username);
    return;
  }
};

/**
 * 初始化赠送状态
 * @param shareKey 赠送分享 key
 * @param username 赠送者用户名
 */
const initSendStatus = async (shareKey: string, username: string) => {
  const receiveResp = await receiveSendCard(shareKey);
  if (!receiveResp) {
    return;
  }

  const { status: popupStatus, cardConfig } = receiveResp;
  const sendPopupConfig = props.config.sendPopupConfigs[sendPopupConfigsKeyByStatus[popupStatus]];

  showSendOrAskCardPopup(cardConfig, sendPopupConfig, handleUserName(username), () => {
    reporter.reportEvent({
      eventName: 'dt_clck',
      businessParams: {
        ...baseReportData,
        eid: DATONG_RPORT_ID.CollectCardsPopupButton,
        button_text: sendPopupConfig.btnText,
        result_code: popupStatus === ReceiveSendCardStatus.Success ? 1 : 0,
      },
    });

    return true;
  });

  return;
};

/**
 * 初始化索要状态
 * @param username 用户名
 */
const initAskStatus = async (username: string) => {
  const resp = await getShowSendAskCardPopupConfig();
  if (!resp?.cardConfig) {
    return;
  }

  const { cardConfig } = resp;

  showSendOrAskCardPopup(
    cardConfig,
    props.config.requestPopupConfigs,
    handleUserName(username),
    async () => {
      // 未登录需要登录
      if (!isLogin.value) {
        app?.$openLogin();
        return false;
      }

      const result = await sendAskCard(cardConfig.cardId);
      reporter.reportEvent({
        eventName: 'dt_clck',
        businessParams: {
          ...baseReportData,
          eid: DATONG_RPORT_ID.CollectCardsPopupButton,
          button_text: props.config.requestPopupConfigs.btnText,
          result_code: result ? 1 : 0,
        },
      });

      return true;
    },
  );
};

/** 更新集卡信息 */
const updateCollectCards = () => {
  if (!isLogin.value) {
    console.warn('[updateCollectCards] 用户未登录');
    return;
  }

  void getUserCollectCards();
};

void init();

onMounted(() => {
  const modInfo = actUtils.getCInfoByModID({ modID: props.config.modId });
  $bus?.$emit(COMPONENT_EVENT.InitModInfo, instance?.proxy, {
    collect_id: getCollectID(props.config.modId),
    activity_iid: (globalThis as any).magicUiconfig?.[0]?.backendId,
    component_type: modInfo?.component_type || '',
    component_iid: modInfo?.component_iid || '',
  });

  // 页面可见事件监听
  moleJsbridge.getJSBridge()?.event?.on(MOLE_JSBRIDGE_EVENT_NAME.PageAppear, updateCollectCards);
});

onUnmounted(() => {
  moleJsbridge.getJSBridge()?.event?.off(MOLE_JSBRIDGE_EVENT_NAME.PageAppear, updateCollectCards);
});

// 组件抛出事件
defineExpose({
  /** 更新收集卡片 */
  [COMPONENT_METHODS.updateCollectCard]: updateCollectCards,
  /** 初始化赠送 */
  [COMPONENT_METHODS.initPopup]: () => {
    const urlParams = new URLSearchParams(location.search);
    const shareType = urlParams.get(CARD_SHARE_TYPE) as (CardShareType | null);

    if (!shareType) {
      console.info('[COMPONENT_METHODS.initPopup] 非赠送或者索要弹窗情况');
      return;
    }

    const shareKey = urlParams.get(CARD_SEND_KEY) || '';
    const username = urlParams.get(USER_NAME) || '他人';
    void initSendOrAsk(shareType, username, shareKey);
  },
});
</script>

<style lang="scss" src="./index.scss" scoped />
