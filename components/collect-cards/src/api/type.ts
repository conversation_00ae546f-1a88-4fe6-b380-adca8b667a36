import { CardInfo } from '../types/index';

/** 查询用户卡片列表响应 */
export interface SearchUserCardListResp {
  /** 卡片列表 */
  cardList: CardInfo[];
  /** 奖励领取次数 */
  receiveRewardTimes: number;
  /** 是否已经全部收集卡片 */
  hasCollected: boolean;
  /** 加密账号 */
  encryptId: string;
}

/** 发起赠送请求体 */
export interface LaunchSendReq {
  /** 集卡 ID */
  collect_id: string;
  /** 卡片 ID */
  card_id: string;
}

/** 发起赠送请求体 */
export interface LaunchSendResp {
  sendKey: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface SendCardReceiveResp {}

/** 索要卡片请求 */
export interface AskCardSendReq {
  /** 集卡 ID */
  collect_id: string;
  /** 卡片 ID */
  card_id: string;
  /** 索要者 openid */
  ask_openid: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface AskCardSendResp {}
