import { camelize<PERSON><PERSON><PERSON> } from 'humps';
import { actExecRequester } from '@tencent/moka-data-core';
import type { ActExecRespBody, StandardizedResp } from '@tencent/moka-data-core/dist/types/requester';
import { utils } from '@tencent/moka-ui-domain';

import { DEFAULT_ERROR_MESSAGE } from '../constant';
import { LotteryResult } from '../types';
import type {
  AskCardSendReq,
  AskCardSendResp,
  LaunchSendReq,
  LaunchSendResp,
  SearchUserCardListResp,
  SendCardReceiveResp,
} from './type';

/** 集卡服务名 */
const collectServerName = '/trpc.activity.card_collection.CardCollection';
/** 组件类型 */
const componentType = 'collect_cards';

/**
 * 查询用户卡片列表
 * @param componentID 组件 ID
 * @param modID 模块 ID
 * @param collectID 集卡 ID
 * @returns 用户集卡信息
 */
export async function searchUserCardList(
  componentID: string,
  modID: string,
  collectID: string,
) {
  const result = await actExecRequester.request<{ collect_id: string }, SearchUserCardListResp>({
    isTest: utils.isTestEnv(),
    componentType,
    componentID,
    componentInfo: {
      modID,
    },
    invocation: {
      name: `${collectServerName}/SearchUserCardList`,
      data: {
        collect_id: collectID,
      },
    },
    qualifier_params: [],
  });

  return handleResp(result);
}

/**
 * 发起卡片赠送
 * @param componentID 组件 ID
 * @param modID 模块 ID
 * @param data 发起卡片赠送请求参数
 * @returns 发起卡片赠送响应
 */
export async function launchSend(
  componentID: string,
  modID: string,
  data: LaunchSendReq,
) {
  const result = await actExecRequester.request<LaunchSendReq, LaunchSendResp>({
    isTest: utils.isTestEnv(),
    componentType,
    componentID,
    componentInfo: {
      modID,
    },
    invocation: {
      name: `${collectServerName}/LaunchSend`,
      data,
    },
    qualifier_params: [],
  });

  return handleResp(result);
}

/**
 * 赠送卡片
 * @param componentID 组件 ID
 * @param modID 模块 ID
 * @param sendKey 赠送 ID
 */
export async function sendCardReceive(
  componentID: string,
  modID: string,
  sendKey: string,
) {
  const result = await actExecRequester.request<{ send_key: string }, SendCardReceiveResp>({
    isTest: utils.isTestEnv(),
    componentType,
    componentID,
    componentInfo: {
      modID,
    },
    invocation: {
      name: `${collectServerName}/SendCardReceive`,
      data: {
        send_key: sendKey,
      },
    },
    qualifier_params: [],
  });

  return handleResp(result);
}

/**
 * 索要卡片
 * @param componentID 组件 ID
 * @param modID 模块 ID
 * @param data 索要卡片数据
 */
export async function askCardSend(
  componentID: string,
  modID: string,
  data: AskCardSendReq,
) {
  const result = await actExecRequester.request<AskCardSendReq, AskCardSendResp>({
    isTest: utils.isTestEnv(),
    componentType,
    componentID,
    componentInfo: {
      modID,
    },
    invocation: {
      name: `${collectServerName}/AskCardSend`,
      data,
    },
    qualifier_params: [],
  });

  return handleResp(result);
}

/**
 * 获取抽奖奖励列表
 * @param componentID 组件 ID
 * @param modID 抽奖模块 ID
 * @param lotteryIID 抽奖 ID
 * @returns 抽奖奖励列表
 */
export async function getLotteryReward(
  componentID: string,
  modID: string,
  lotteryIID: string,
) {
  const result = await actExecRequester.request<{ lottery_iid: string }, LotteryResult>({
    isTest: utils.isTestEnv(),
    // 抽奖模块 type
    componentType: '11',
    componentID,
    componentInfo: {
      // 抽奖模块 ID
      modID,
    },
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/GetUserLotteryResult',
      data: {
        lottery_iid: lotteryIID,
      },
    },
    qualifier_params: [],
  });

  return handleResp(result);
}

/**
 * 处理响应
 * @param resp 响应数据
 * @returns 处理后的响应结果
 */
function handleResp<T>(resp: StandardizedResp<ActExecRespBody<T>>) {
  const { code, tip, body, statusCode } = resp;

  // 状态码非 200，返回默认错误
  if (statusCode !== 200) {
    return {
      code: -99999,
      msg: DEFAULT_ERROR_MESSAGE,
      data: null,
    };
  }

  return {
    code,
    msg: tip,
    data: body?.data?.data ? camelizeKeys<T>(body.data.data) : null,
  };
}
