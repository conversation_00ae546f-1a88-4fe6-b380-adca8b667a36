import { template } from 'radash';
import { actUtils } from '@tencent/moka-data-core';

/**
 * 兜底返回空字符串
 * @param value 值
 * @returns 值，兜底返回空字符串
 */
export function withEmptyString(value: any) {
  return value ?? '';
}

/**
 * 获取集卡 ID
 * @param modID 后端模块 ID
 * @returns 集卡 ID
 */
export function getCollectID(modID: string | number) {
  // 集卡后端模块 ciid 格式: iid_yybcardcollection_nqwqubw9dr
  const splittedCIID = actUtils.getCIIDByModID({ modID: String(modID) }).split('_');
  return splittedCIID[splittedCIID.length - 1];
}

/**
 * 替换 <xxx> 模板
 * @param str 模板字符串
 * @param data 模板数据，要求<xxx>
 * @returns 替换模板
 */
export function replaceTemplate(str: string, data: Record<string, any>) {
  return template(str ?? '', data, /<(.+?)>/g);
}

/**
 * 获取抽奖 ID
 * @param modID 后端模块 ID
 * @returns 抽奖 ID
 */
export function getLotteryID(modID: string | number) {
  if (!modID) return '';
  return actUtils.getCIIDByModID({ modID: String(modID) });
}

/**
 * 处理用户名展示
 * @param userName 用户名
 */
export function handleUserName(userName: string) {
  if (userName.length > 5) {
    return `${userName.substring(0, 5)}...`;
  }

  return userName;
}
