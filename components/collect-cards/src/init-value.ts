
export default {
  /** 组件类型 */
  type: 'moka-ui-collect-cards',
  /** 模块 ID */
  modId: '',
  /** 抽奖模块 ID */
  lotteryModID: '',
  /** 收集卡片的提示文本 */
  collectingTips: '还差$count张即可抽取，保底必得6Q币',
  /** 完成集卡的提示文本 */
  collectedTips: '恭喜您完成集卡，暴击保底必得6Q币',
  /** 完成活动的提示文本 */
  finishedTips: '恭喜您，成功获得$countQ币！',
  /** 状态文本 */
  statusTipsColor: 'rgba(239, 208, 122, 1)',
  /** 集卡区域背景颜色 */
  mainBackgroundColor: '',
  /** 集卡区域背景图 */
  mainBackgroundImage: '',
  /** 分享 icon */
  shareIcon: '',
  /** 是否与物品选择联动（终端需要使用字段） */
  withSelectGoods: false,
  /** 是否需要展示遮罩 */
  isNeedScrollMask: false,
  /** 遮罩开始颜色，渐变色开始，从左到右 */
  maskBeginColor: 'rgba(15,15,15,0)',
  /** 遮罩结束颜色，渐变色结束，从左到右 */
  maskEndColor: 'rgba(24, 29, 39, 1)',
  /** 卡片项配置 */
  cardItemConfigs: {
    /** 文本颜色 */
    textColor: 'rgba(255, 255, 255, 1)',
    /** 背景颜色 */
    backgroundColor: 'rgba(0, 0, 0, 1)',
    /** 背景图片 */
    backgroundImage: '',
  },

  /** 进度配置 */
  processConfigs: {
    /** 进度文本颜色 */
    textColor: 'rgba(255, 255, 255, 1)',
    /** 当前进度文本颜色 */
    currentTextColor: 'rgba(239, 208, 122, 1)',
    /** 文本背景图 */
    btnBackgroundImage: '',
  },

  /** 弹窗配置 */
  popupConfigs: {
    /** 背景颜色 */
    backgroundColor: 'rgba(0, 0, 0, 1)',
    /** 背景图 */
    backgroundImage: '',
    /** 按钮背景颜色 */
    btnBackgroundColor: 'rgba(255, 255, 255, 1)',
    /** 按钮背景图像 */
    btnBackgroundImage: '',
    /** 按钮文本颜色 */
    btnTextColor: 'rgba(0, 0, 0, 1)',
    /** 弹窗标题颜色 */
    titleColor: 'rgba(0, 0, 0, 1)',
    /** 提示文本颜色 */
    tipsColor: 'rgba(0, 0, 0, 1)',
  },

  /** 赠送弹窗配置 */
  sendPopupConfigs: {
    success: {
      /** 成功赠送卡片的标题 */
      title: '成功获得赠卡 1 张',
      /** 成功赠送卡片的提示 */
      tips: '来自<username>的赠送 集齐卡牌抽88Q币',
      /** 按钮文本 */
      btnText: '开心收下',
      /** 按钮图片 */
      btnTextImage: '',
    },
    hasOwned: {
      /** 已拥有卡片的提示标题 */
      title: '已经拥有该卡片啦',
      /** 已拥有赠送卡片的提示 */
      tips: '集齐参与抽取88Q币',
      /** 按钮文本 */
      btnText: '知道了',
      /** 按钮图片 */
      btnTextImage: '',
    },
    hasReceived: {
      /** 卡片已被领取完的提示标题 */
      title: '卡片已被领完啦',
      /** 已拥有赠送卡片的提示 */
      tips: '集齐参与抽取88Q币',
      /** 按钮文本 */
      btnText: '确定',
      /** 按钮图片 */
      btnTextImage: '',
    },
  },

  /** 索要弹窗配置 */
  requestPopupConfigs: {
    /** 索要弹窗的标题 */
    title: '向您索要卡片',
    /** 索要弹窗提示 */
    tips: '来自<username>的索要 集齐卡牌抽88Q币',
  },

  /** 赠送按钮配置 */
  sendBtnConfigs: {
    /** 按钮文本 */
    btnText: '赠送',
    /** 按钮文本颜色 */
    textColor: 'rgba(0, 0, 0, 1)',
    /** 按钮背景颜色 */
    backgroundColor: 'rgba(255, 255, 255, 1)',
    /** 按钮背景图片 */
    backgroundImage: '',
    /** 文案图片 */
    textImage: '',
    /** 分享标题 */
    shareTitle: '<username>向您赠送<cardName>一张',
    /** 分享内容 */
    shareContent: '',
  },

  /** 索要按钮配置 */
  askBtnConfigs: {
    /** 按钮文本 */
    btnText: '索要',
    /** 按钮文本颜色 */
    textColor: 'rgba(0, 0, 0, 1)',
    /** 文案图片 */
    textImage: '',
    /** 按钮背景颜色 */
    backgroundColor: 'rgba(255, 255, 255, 1)',
    /** 按钮背景图片 */
    backgroundImage: '',
    /** 分享标题 */
    shareTitle: '<username>向您索要<cardName>一张',
  },

  dtEid: 'collect-cards',
  dtTplID: 1412,
};
