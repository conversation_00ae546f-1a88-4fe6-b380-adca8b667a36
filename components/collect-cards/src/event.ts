
import { COMPONENT_EVENT, COMPONENT_METHODS } from './constant/index';
import { UserCollectCardStatus } from './types';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [
    {
      label: '未收集完成卡片状态回调',
      value: COMPONENT_EVENT[UserCollectCardStatus.Collecting],
      desc: '',
    },
    {
      label: '已收集完成卡片状态回调',
      value: COMPONENT_EVENT[UserCollectCardStatus.Collected],
      desc: '',
    },
    {
      label: '已完成集卡任务',
      value: COMPONENT_EVENT[UserCollectCardStatus.Finished],
      desc: '',
    },
    {
      label: '初始化模块信息',
      value: COMPONENT_EVENT.InitModInfo,
      desc: '',
    },
    {
      label: '更新集卡信息',
      value: COMPONENT_EVENT.UpdateCollectInfo,
      desc: '',
    },
  ],
  /** 组件内部方法，提供外部调用 */
  methods: [
    {
      label: '更新集卡信息',
      value: COMPONENT_METHODS.updateCollectCard,
      desc: '',
    },
    {
      label: '初始化赠送弹窗',
      value: COMPONENT_METHODS.initPopup,
      desc: '',
    },
  ],
};
