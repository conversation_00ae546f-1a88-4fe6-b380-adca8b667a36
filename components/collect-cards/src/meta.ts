import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用 formily 协议
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        modId: {
          type: 'string',
          title: '集卡模块',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'ModSelect',
          'x-component-props': {
            modType: 22,
          },
        },
        lotteryModID: {
          type: 'string',
          title: '抽奖模块 ID（用于展示抽取的 Q 币奖励）',
          'x-decorator': 'FormItem',
          'x-component': 'ModSelect',
          'x-component-props': {
            modType: 11,
          },
        },
        // 收集中提示文案
        collectingTips: {
          type: 'string',
          title: '收集中状态文案提示',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          default: '还差$count张即可抽取，保底必得6Q币',
          'x-decorator-props': {
            labelWrap: true,
            extra: '$count 最终会被替换为用户实际已收集卡片进度',
          },
        },
        // 收集完成提示文案
        collectedTips: {
          type: 'string',
          title: '收集完成状态文案提示',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          default: '恭喜您完成集卡，暴击保底必得6Q币',
          'x-decorator-props': {
            labelWrap: true,
          },
        },
        // 完成集卡任务展示文案
        finishedTips: {
          type: 'string',
          title: '已获取奖励状态文案提示',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          default: '恭喜您，成功获得$countQ币！',
          'x-decorator-props': {
            labelWrap: true,
            extra: '$count 最终会被替换为用户实际已获得的 Q 币',
          },
        },
        statusTipsColor: {
          type: 'string',
          title: '状态文本颜色',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          'x-decorator-props': {
            labelWrap: true,
          },
          default: 'rgba(239, 208, 122, 1)',
        },
        mainBackgroundImage: {
          type: 'string',
          title: '集卡区域背景图',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          default: '',
        },
        mainBackgroundColor: {
          type: 'string',
          title: '集卡区域背景颜色',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          'x-decorator-props': {
            labelWrap: true,
            labelWidth: '110',
          },
          default: 'rgba(255, 255, 255, 1)',
        },
        shareIcon: {
          type: 'string',
          required: true,
          title: '分享 ICON',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Upload',
          default: '',
        },
        withSelectGoods: {
          type: 'boolean',
          title: '是否和选择组件联动（终端使用字段）',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          default: false,
        },
        isNeedScrollMask: {
          type: 'boolean',
          title: '是否展示卡片滚动遮罩',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          default: false,
        },
        maskBeginColor: {
          type: 'string',
          title: '遮罩开始颜色，渐变色开始，从左到右',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          'x-decorator-props': {
            labelWrap: true,
          },
          default: 'rgba(15,15,15,0)',
        },
        maskEndColor: {
          type: 'string',
          title: '遮罩结束颜色，渐变色结束，从左到右',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          'x-decorator-props': {
            labelWrap: true,
          },
          default: 'rgba(24, 29, 39, 1)',
        },
        Collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            // 集卡进度配置
            cardItem: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '卡片项配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'cardItemConfigs.textColor': {
                      type: 'string',
                      title: '文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'cardItemConfigs.backgroundColor': {
                      type: 'string',
                      title: '背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'cardItemConfigs.backgroundImage': {
                      type: 'string',
                      title: '背景图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 集卡进度配置
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '集卡进度配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'processConfigs.textColor': {
                      type: 'string',
                      title: '进度文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'processConfigs.currentTextColor': {
                      type: 'string',
                      title: '当前进度文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(239, 208, 122, 1)',
                    },
                    'processConfigs.backgroundImage': {
                      type: 'string',
                      title: '进度背景图',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 弹窗基础配置
            tab2: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '弹窗基础配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'popupConfigs.backgroundImage': {
                      type: 'string',
                      title: '背景图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'popupConfigs.backgroundColor': {
                      type: 'string',
                      title: '背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'popupConfigs.btnBackgroundColor': {
                      type: 'string',
                      title: '按钮背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'popupConfigs.btnBackgroundImage': {
                      type: 'string',
                      title: '按钮背景图',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'popupConfigs.btnTextColor': {
                      type: 'string',
                      title: '按钮文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(0, 0, 0, 1)',
                    },
                    'popupConfigs.titleColor': {
                      type: 'string',
                      title: '标题文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(0, 0, 0, 1)',
                    },
                    'popupConfigs.tipsColor': {
                      type: 'string',
                      title: '提示文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(0, 0, 0, 1)',
                    },
                  },
                },
              },
            },
            // 赠送卡片成功状态弹窗配置
            success: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '赠送卡片弹窗配置：领取成功状态',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'sendPopupConfigs.success.title': {
                      type: 'string',
                      title: '标题文案',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '成功获得赠卡 1 张',
                    },
                    'sendPopupConfigs.success.tips': {
                      type: 'string',
                      title: '提示文案',
                      'x-decorator-props': {
                        labelWrap: true,
                        extra: '<username> 会替换为赠送者名称',
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '来自<username>的赠送 集齐卡牌抽88Q币',
                    },
                    'sendPopupConfigs.success.btnText': {
                      type: 'string',
                      title: '按钮文本',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '开心收下',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                    },
                    'sendPopupConfigs.success.btnTextImage': {
                      type: 'string',
                      title: '按钮文案图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 赠送卡片已拥有状态弹窗配置
            hasOwned: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '赠送卡片弹窗配置：已拥有状态',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'sendPopupConfigs.hasOwned.title': {
                      type: 'string',
                      title: '弹窗标题',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '已经拥有该卡片啦',
                    },
                    'sendPopupConfigs.hasOwned.tips': {
                      type: 'string',
                      title: '弹窗提示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: '集齐参与抽取88Q币',
                    },
                    'sendPopupConfigs.hasOwned.btnText': {
                      type: 'string',
                      title: '按钮文本',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: '知道了',
                    },
                    'sendPopupConfigs.hasOwned.btnTextImage': {
                      type: 'string',
                      title: '按钮文案图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 赠送卡片已被领取状态弹窗配置
            hasReceived: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '赠送卡片弹窗配置：已被领取状态',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'sendPopupConfigs.hasReceived.title': {
                      type: 'string',
                      title: '弹窗标题',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '卡片已被领完啦！',
                    },
                    'sendPopupConfigs.hasReceived.tips': {
                      type: 'string',
                      title: '弹窗提示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: '集齐参与抽取88Q币',
                    },
                    'sendPopupConfigs.hasReceived.btnText': {
                      type: 'string',
                      title: '按钮文本',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: '确定',
                    },
                    'sendPopupConfigs.hasReceived.btnTextImage': {
                      type: 'string',
                      title: '按钮文案图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 赠送按钮配置
            tab4: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '赠送按钮配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'sendBtnConfigs.btnText': {
                      type: 'string',
                      title: '按钮文本',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '赠送',
                    },
                    'sendBtnConfigs.textColor': {
                      type: 'string',
                      title: '文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(0, 0, 0, 1)',
                    },
                    'sendBtnConfigs.backgroundColor': {
                      type: 'string',
                      title: '按钮背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'sendBtnConfigs.backgroundImage': {
                      type: 'string',
                      title: '按钮背景图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'sendBtnConfigs.textImage': {
                      type: 'string',
                      title: ' 文案图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'sendBtnConfigs.shareTitle': {
                      type: 'string',
                      title: '分享标题',
                      'x-decorator-props': {
                        labelWrap: true,
                        extra: '<username> 会替换为赠送者名称、<cardName>会替换为卡片名称',
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '<username>向您赠送<cardName>一张',
                    },
                    'sendBtnConfigs.shareContent': {
                      type: 'string',
                      title: '分享内容',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '',
                    },
                  },
                },
              },
            },
            // 索要弹窗配置
            tab5: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '索要弹窗配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'requestPopupConfigs.title': {
                      type: 'string',
                      title: '弹窗标题',
                      'x-decorator-props': {
                        labelWidth: '100',
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '向您索要卡片',
                    },
                    'requestPopupConfigs.tips': {
                      type: 'string',
                      title: '弹窗提示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '来自<username>的索要 集齐卡牌抽88Q币',
                      'x-decorator-props': {
                        labelWrap: true,
                        extra: '<username> 会替换为索要者名称、<cardName>会替换为卡片名称',
                      },
                    },
                    'requestPopupConfigs.btnText': {
                      type: 'string',
                      title: '按钮文本',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '确定',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                    },
                    'requestPopupConfigs.btnTextImage': {
                      type: 'string',
                      title: '按钮文案图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 索要按钮配置
            tab6: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '索要按钮配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'askBtnConfigs.btnText': {
                      type: 'string',
                      title: '按钮文本',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '索要',
                    },
                    'askBtnConfigs.textColor': {
                      type: 'string',
                      title: '按钮文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(0, 0, 0, 1)',
                    },
                    'askBtnConfigs.backgroundColor': {
                      type: 'string',
                      title: '按钮背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 1)',
                    },
                    'askBtnConfigs.backgroundImage': {
                      type: 'string',
                      title: '按钮背景图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'askBtnConfigs.textImage': {
                      type: 'string',
                      title: ' 文案图片',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'askBtnConfigs.shareTitle': {
                      type: 'string',
                      title: '分享标题',
                      'x-decorator-props': {
                        labelWrap: true,
                        extra: '<username> 会替换为索要者名称、<cardName>会替换为卡片名称',
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '<username>向您索要<cardName>一张',
                    },
                    'askBtnConfigs.shareContent': {
                      type: 'string',
                      title: '分享内容',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
