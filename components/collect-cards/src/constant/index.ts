import { UserCollectCardStatus } from '../types';

/** 赠送卡片 key */
export const CARD_SEND_KEY = 'cardSendKey';
/** 赠送卡片 ID  */
export const CARD_ID = 'cardID';
/** 卡片分享链接类型 */
export const CARD_SHARE_TYPE = 'cardShareType';
/** 卡片集合 ID */
export const COLLECT_ID = 'collectID';
/** 赠送者用户名 */
export const USER_NAME = 'username';
/** 索要卡片 key */
export const CARD_ASK_KEY = 'cardAskKey';
/** 组件内部方法，提供外部调用 */
export const COMPONENT_METHODS = {
  updateCollectCard: 'updateCollectCard',
  initPopup: 'initPopup',
};
/** 默认错误提示文案 */
export const DEFAULT_ERROR_MESSAGE = '系统繁忙，请稍候再试';
/** 主容器两边边距 */
export const MAIN_CONTAINER_PADDING_WIDTH = 32;
/** 组件内抛出事件 */
export const COMPONENT_EVENT = {
  [UserCollectCardStatus.Collecting]: 'collecting',
  [UserCollectCardStatus.Collected]: 'collected',
  [UserCollectCardStatus.Finished]: 'finished',
  InitModInfo: 'initModInfo',
  UpdateCollectInfo: 'updateCollectInfo',
};
/** Mole Jsbridge 事件名称 */
export const MOLE_JSBRIDGE_EVENT_NAME = {
  /** 页面出现 */
  PageAppear: 'pageAppear',
  /** 页面消息 */
  PageDisAppear: 'pageDisappear',
};
