import type { ComponentConfig, IPublicComponentReportSchema } from '@tencent/moka-schema/dist/component-meta-types';
import { BuiltInReportEvent } from '@tencent/mole-report';

/** 大同上报 模版id */
export const DATONG_RPORT_ID = {
  /** 集卡卡片 */
  CollectCardsItem: 'collect-cards-item',
  /** 集卡卡片按钮 */
  CollectCardsItemButton: 'collect-cards-item-button',
  /** 集卡弹窗 */
  CollectCardsPopup: 'collect-cards-popup',
  /** 集卡弹窗按钮 */
  CollectCardsPopupButton: 'collect-cards-popup-button',
};

export default function getReportConfig(componentConfig: ComponentConfig): IPublicComponentReportSchema {
  const reportModule = (componentConfig as any).report?.module?._module;
  const baseReportData = {
    _module: reportModule,
    mod_title: reportModule,
    btn_title: reportModule,
  };

  return {
    schema: {
      // 自定义事件
      publicRules: [],
      nodes: [{
        description: '集卡卡片',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: '.card-item',
          data: {
            ...baseReportData,
            eid: DATONG_RPORT_ID.CollectCardsItem,
          },
        },
      }, {
        description: '集卡卡片按钮',
        rule: {
          events: [BuiltInReportEvent.Exposure, BuiltInReportEvent.Click],
          selector: '.card-item__btn',
          data: {
            ...baseReportData,
            eid: DATONG_RPORT_ID.CollectCardsItemButton,
          },
        },
      }, {
        description: '集卡弹窗',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: '.collect-cards-popup',
          data: {
            ...baseReportData,
            eid: DATONG_RPORT_ID.CollectCardsPopup,
          },
        },
      }, {
        description: '集卡弹窗按钮',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: '.collect-cards-popup__btn',
          data: {
            ...baseReportData,
            eid: DATONG_RPORT_ID.CollectCardsPopupButton,
          },
        },
      }],
    },
  };
}
