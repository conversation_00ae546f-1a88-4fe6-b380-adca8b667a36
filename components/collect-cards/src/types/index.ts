/** 组件配置 */
export interface ComponentConfig {
  /** 组件 ID */
  id: string;
  /** 组件类型 */
  type: string;
  /** 模块 ID */
  modId: string;
  /** 抽奖模块 ID */
  lotteryModID: string;
  /** 收集卡片的提示文本 */
  collectingTips: string;
  /** 完成集卡的提示文本 */
  collectedTips: string;
  /** 完成活动的提示文本 */
  finishedTips: string;
  /** 集卡区域背景图 */
  mainBackgroundImage: string;
  /** 集卡区域背景颜色 */
  mainBackgroundColor: string;
  /** 分享 icon */
  shareIcon: string;
  /** 状态文本颜色 */
  statusTipsColor: string;
  /** 是否需要与弹窗组件联动 */
  withSelectGoods: boolean;
  /** 卡片项配置 */
  cardItemConfigs: CardItemConfigs;
  /** 进度配置 */
  processConfigs: ProcessConfigs;
  /** 弹窗配置 */
  popupConfigs: PopupConfigs;
  /** 赠送弹窗配置 */
  sendPopupConfigs: SendPopupConfigs;
  /** 索要弹窗配置 */
  requestPopupConfigs: RequestPopupConfigs;
  /** 赠送按钮配置 */
  sendBtnConfigs: BtnConfigs;
  /** 索要按钮配置 */
  askBtnConfigs: BtnConfigs;
  /** 是否需要滚动遮罩 */
  isNeedScrollMask: boolean;
  /** 遮罩开始颜色，渐变色开始，从左到右 */
  maskBeginColor: string;
  /** 遮罩结束颜色，渐变色结束，从左到右 */
  maskEndColor: string;
}

/** 卡片项配置 */
export interface CardItemConfigs {
  /** 文本颜色 */
  textColor: string;
  /** 背景颜色 */
  backgroundColor: string;
  /** 背景图片 */
  backgroundImage: string;
}

/** 进度配置 */
export interface ProcessConfigs {
  /** 进度文本颜色 */
  textColor: string;
  /** 当前进度文本颜色 */
  currentTextColor: string;
  /** 背景图片 */
  backgroundImage: string;
}

/** 弹窗基础配置 */
export interface PopupConfigs {
  /** 背景颜色 */
  backgroundColor: string;
  /** 背景图像 */
  backgroundImage: string;
  /** 按钮背景颜色 */
  btnBackgroundColor: string;
  /** 按钮背景图像 */
  btnBackgroundImage: string;
  /** 按钮文本颜色 */
  btnTextColor: string;
  /** 弹窗标题颜色 */
  titleColor: string;
  /** 提示文本颜色 */
  tipsColor: string;
}

/** 赠送弹窗配置 */
export interface SendPopupConfigs {
  /** 成功状态 */
  success: PopupStateConfig;
  /** 已拥有状态 */
  hasOwned: PopupStateConfig;
  /** 已被领取状态 */
  hasReceived: PopupStateConfig;
}

/** 弹窗状态配置 */
export interface PopupStateConfig {
  /** 弹窗标题 */
  title: string;
  /** 弹窗提示 */
  tips: string;
  /** 按钮文本 */
  btnText: string;
  /** 按钮文案图片 */
  btnTextImage: string;
}

/** 索要弹窗配置 */
export interface RequestPopupConfigs {
  /** 索要卡片的提示标题 */
  title: string;
  /** 提示文本 */
  tips: string;
  /** 按钮文本 */
  btnText: string;
  /** 按钮文案图片 */
  btnTextImage: string;
}

/** 赠送 & 索要按钮配置 */
export interface BtnConfigs {
  /** 按钮文本 */
  btnText: string;
  /** 按钮文本颜色 */
  textColor: string;
  /** 按钮背景颜色 */
  backgroundColor: string;
  /** 按钮背景图片 */
  backgroundImage: string;
  /** 按钮图片 */
  textImage: string;
  /** 分享标题 */
  shareTitle: string;
  /** 分享内容 */
  shareContent: string;
}

/** 卡片配置 */
export interface CardConfig {
  /** 卡片 ID */
  cardId: string;
  /** 卡片名称 */
  cardName: string;
  /** 激活卡片图片 */
  activatedImg: string;
  /** 未激活卡片图片 */
  noActivatedImg: string;
}

/** 卡片状态 */
export enum CardStatus {
  /** 默认 */
  DEFAULT = 0,
  /** 可以赠送 */
  CAN_SEND = 1,
  /** 可以索要 */
  CAN_ASK = 2,
}

/** 卡片信息 */
export interface CardInfo {
  /** 卡片配置 */
  cardConfig: CardConfig;
  /** 卡片数量 */
  cardNum: number;
  /** 卡片状态 */
  cardStatus: CardStatus;
}

/** 卡片分享类型 */
export enum CardShareType {
  /** 赠送 */
  Send = 1,
  /** 索要 */
  Ask = 2,
}

/** 分享数据 */
export interface ShareData {
  /** 卡片 ID */
  cardID: string;
  /** 卡片名称 */
  cardName: string;
  /** 分享标题 */
  shareTitle: string;
  /** 分享内容 */
  shareContent: string;
}

/** 用户集卡状态 */
export enum UserCollectCardStatus {
  /** 集卡中 */
  Collecting = 0,
  /** 已收集完毕 */
  Collected = 1,
  /** 已完成集卡任务 */
  Finished = 2,
}

/** 抽奖结果 */
export interface LotteryResult {
  propertyResults: ({
    instanceInfo: {
      desc: string;
    };
  })[];
}
