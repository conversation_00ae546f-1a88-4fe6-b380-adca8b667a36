.card-item {
  position: relative;
  margin-right: 0.13rem;
  width: fit-content;
  height: fit-content;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .card-item__cover {
    width: 0.785rem;
    height: 1.055rem;
    margin-bottom: 0.095rem;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .card-item__btn {
    text-align: center;
    line-height: 0.19rem;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    font-size: 0.11rem;
    font-weight: bold;
  }

  .card-item__num {
    display: flex;
    box-sizing: border-box;
    min-height: 0.115rem;
    min-width: 0.115rem;
    font-size: 0.07rem;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0.005rem;
    right: 0.005rem;
    color: rgba(255, 255, 255, 1);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
  }
}

.card-item:last-child {
  margin-right: 0;
}