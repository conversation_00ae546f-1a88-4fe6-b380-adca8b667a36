<template>
  <div
    class="card-item"
    :data-report="JSON.stringify({
      card_id: cardInfo.cardConfig.cardId,
      card_nums: cardNum,
    })"
  >
    <div
      v-if="isNeedToShowCardNum"
      :style="cardNumStyle"
      class="card-item__num"
    >
      {{ cardNum }}
    </div>
    <div
      class="card-item__cover"
      :style="coverStyle"
    />
    <collect-cards-btn
      class="card-item__btn"
      v-bind="buttonConfig"
      :data-report="JSON.stringify({
        card_id: cardInfo.cardConfig.cardId,
        button_text: buttonConfig.text,
      })"
      @click="() => {
        hasCard ? send() : ask();
      }"
    />
  </div>
</template>

<script setup lang="ts">

import { debounce } from 'radash';
import { computed } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';

import type { BtnConfigs, CardInfo, CardItemConfigs } from '../../types';
import { CardStatus } from '../../types';
import { withEmptyString } from '../../utils/index';
import CollectCardsBtn from '../btn/index.vue';

const props = defineProps<{
  /** 卡片信息 */
  cardInfo: CardInfo;
  /** 是否已完成 */
  isFinished: boolean;
  /** 赠送按钮配置 */
  sendBtnConfigs: BtnConfigs;
  /** 索要按钮配置 */
  askBtnConfigs: BtnConfigs;
  /** 卡片数字配置 */
  cardItemConfigs: CardItemConfigs;
}>();

const emits = defineEmits(['send', 'ask']);

const { toast } = hooks.useMokaInject();

/** 卡片状态 */
const cardStatus = computed(() => props.cardInfo.cardStatus);
/** 卡片数量 */
const cardNum = computed(() => props.cardInfo?.cardNum);
/** 是否有卡片 */
const hasCard = computed(() => props.cardInfo?.cardNum > 0);
/** 卡片图片 */
const coverStyle = computed(() => {
  const { cardInfo } = props;
  // 如果已经完成抽奖，则直接展示已激活状态
  if (props.isFinished) {
    return {
      backgroundImage: `url(${cardInfo.cardConfig.activatedImg})`,
    };
  }

  const coverURL = hasCard.value
    ? cardInfo.cardConfig.activatedImg
    : cardInfo.cardConfig.noActivatedImg;

  return {
    backgroundImage: `url(${coverURL})`,
  };
});
/** 按钮配置 */
const buttonConfig = computed(() => {
  const { askBtnConfigs, sendBtnConfigs } = props;
  const config = hasCard.value ? sendBtnConfigs : askBtnConfigs;
  const { backgroundColor, backgroundImage, btnText, textColor, textImage } = config;

  return {
    width: '0.775rem',
    height: '0.19rem',
    backgroundColor,
    backgroundImage,
    textImage,
    text: btnText,
    color: textColor,
    fontSize: '0.11rem',
  };
});
/** 卡片数字配置 */
const cardNumStyle = computed(() => {
  const { cardItemConfigs } = props;

  const { backgroundColor, backgroundImage, textColor } = cardItemConfigs ?? {};
  return {
    color: textColor,
    backgroundColor,
    backgroundImage: `url(${withEmptyString(backgroundImage)})`,
  };
});
/** 需要展示卡片数量 */
const isNeedToShowCardNum = computed(() => {
  if (props.isFinished) return true;
  return hasCard.value;
});

/** 索要 */
const ask = debounce({ delay: 100 }, () => {
  const { cardInfo, askBtnConfigs } = props;
  if (cardStatus.value !== CardStatus.CAN_ASK) {
    toast?.('您不满足索要卡片的要求哦～');
    return;
  }

  const { shareTitle, shareContent } = askBtnConfigs;
  emits('ask', {
    cardID: cardInfo.cardConfig.cardId,
    cardName: cardInfo.cardConfig.cardName,
    shareTitle,
    shareContent,
  });
});

/** 赠送 */
const send = debounce({ delay: 100 }, () => {
  const { cardInfo, sendBtnConfigs } = props;
  if (cardStatus.value !== CardStatus.CAN_SEND) {
    toast?.(`您当前仅有${cardInfo.cardNum}张该卡片，无法赠送哦～`);
    return;
  }

  const { shareTitle, shareContent } = sendBtnConfigs;
  emits('send', {
    cardID: cardInfo.cardConfig.cardId,
    cardName: cardInfo.cardConfig.cardName,
    shareTitle,
    shareContent,
  });
});
</script>

<style scoped lang="scss" src="./index.scss"></style>
