<template>
  <transition name="fade">
    <div
      class="collect-cards-popup"
      :style="popupStyle"
      :data-report="JSON.stringify({
        popup_title: config.title
      })"
    >
      <div
        class="collect-cards-popup__title"
        :style="titleStyle"
      >
        {{ config.title }}
      </div>
      <div
        v-if="config.tips"
        class="collect-cards-popup__tips"
        :style="tipsStyle"
      >
        {{ config.tips }}
      </div>
      <div
        class="collect-cards-popup__cover"
        :style="cardStyle"
      />
      <collect-cards-btn
        class="collect-cards-popup__btn"
        width="1.5825rem"
        height="0.315rem"
        font-size="0.16rem"
        :background-color="buttonConfigs.backgroundColor"
        :background-image="buttonConfigs.backgroundImage"
        :color="buttonConfigs.color"
        :text="buttonConfigs.text"
        :text-image="buttonConfigs.textImage"
        :data-report="JSON.stringify({
          button_text: buttonConfigs.text
        })"
        @click="handleClick"
      />
      <div
        class="collect-cards-popup-close-btn"
        @click="closePopup"
      />
    </div>
  </transition>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { CollectCardsPopupConfigs } from '../../hooks/use-popup-hook';
import { useResize } from '../../hooks/use-popup-resize';
import { withEmptyString } from '../../utils';
import CollectCardsBtn from '../btn/index.vue';

const props = defineProps<{
  config: CollectCardsPopupConfigs;
}>();
const emits = defineEmits(['close', 'confirm']);

// 弹窗高度：375 设计稿下
const popupHeight = 364.75;
const { popupStyle: resizePopupStyle } = useResize(popupHeight);

/** 弹窗样式 */
const popupStyle = computed(() => {
  const { backgroundImage, backgroundColor } = props.config;
  return {
    ...resizePopupStyle.value,
    backgroundImage: `url(${withEmptyString(backgroundImage)})`,
    backgroundColor,
  };
});
/** 标题文案颜色 */
const titleStyle = computed(() => {
  const { titleColor } = props.config;
  return {
    color: titleColor,
  };
});
/** 提示文案颜色 */
const tipsStyle = computed(() => {
  const { tipsColor } = props.config;
  return {
    color: tipsColor,
  };
});
/** 卡片图片样式 */
const cardStyle = computed(() => {
  const { cardCover } = props.config;
  return {
    backgroundImage: `url(${cardCover ?? ''})`,
  };
});
/** 按钮样式 */
const buttonConfigs = computed(() => props.config.buttonConfig || {});
/** 关闭弹窗 */
const closePopup = () => {
  emits('close');
};

/** 点击确认按钮 */
const handleClick = () => {
  emits('confirm');
};
</script>

<style lang="scss" src="./index.scss"></style>
