.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.collect-cards-popup {
  width: 2.97rem;
  height: 3.6625rem;
  padding-top: 0.14rem;
  display: flex;
  align-items: center;
  flex-direction: column;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  &__title {
    font-size: 0.21rem;
    font-weight: 800;
    color: rgba(0, 0, 0, 1);
    margin-bottom: 0.025rem;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__tips {
    width: 90%;
    font-size: 0.1rem;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__cover {
    width: 1.53rem;
    height: 2.06rem;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    margin-top: 0.165rem;
  }
  &__btn {
    margin-top: 0.251rem;
  }
}

.collect-cards-popup-close-btn {
  position: absolute;
  width: 0.11rem;
  height: 0.11rem;
  top: 0.185rem;
  right: 0.17rem;
  background-image: url('https://cms.myapp.com/xy/yybtech/uon87GDM.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.collect-cards-popup-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}