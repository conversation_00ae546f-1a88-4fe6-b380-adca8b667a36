<template>
  <div
    class="collect-cards-button"
    :style="buttonStyle"
    @click="handleButtonClick"
  >
    <img
      v-if="textImage"
      :src="textImage"
      class="text-image"
    >
    <div
      v-else
      :style="textStyle"
      class="text"
    >
      {{ text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { withEmptyString } from '../../utils';

const props = defineProps<{
  /** 按钮宽度 */
  width?: string;
  /** 按钮高度 */
  height?: string;
  /** 文本颜色 */
  color?: string;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 文本图片 */
  textImage?: string;
  /** 文本 */
  text?: string;
  /** 图片 */
  backgroundImage?: string;
  /** 字号 */
  fontSize?: string;
}>();

const emits = defineEmits(['click']);

/** 按钮样式 */
const buttonStyle = computed(() => {
  const { width, height, color, backgroundColor, backgroundImage } = props;
  return {
    width: withEmptyString(width),
    color: withEmptyString(color),
    height: withEmptyString(height),
    backgroundColor: backgroundImage ? '' : withEmptyString(backgroundColor),
    backgroundImage: `url(${withEmptyString(backgroundImage)})`,
  };
});

const textStyle = computed(() => {
  const { fontSize } = props;
  return {
    fontSize,
  };
});

/** 处理按钮点击 */
const handleButtonClick = () => {
  emits('click');
};

</script>

<style lang="scss" scoped src="./index.scss"></style>
