import { computed, getCurrentInstance, ref } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';

import { CAN_NOT_RECEIVE_POINT_ERROR_CODE, CAN_NOT_SENT_CARD, HAS_RECEIVED, SUCCESS_CODE } from '../api/code';
import { askCardSend, getLotteryReward, launchSend, searchUserCardList, sendCardReceive } from '../api/index';
import { CARD_ASK_KEY, CARD_ID, CARD_SEND_KEY, CARD_SHARE_TYPE, COLLECT_ID, COMPONENT_EVENT, DEFAULT_ERROR_MESSAGE, USER_NAME } from '../constant';
import { type CardInfo, CardShareType, UserCollectCardStatus } from '../types';
import { getCollectID, getLotteryID } from '../utils/index';

const { getShareUrlWithParams, getLoginUserOpenID, isYYB } = utils;

/** 领取赠送卡片的状态 */
export enum ReceiveSendCardStatus {
  /** 成功 */
  Success = 1,
  /** 已拥有卡片 */
  HasOwned = 2,
  /** 已被领取 */
  HasReceived = 3,
}

/** 领取弹窗状态 */
export const ReceiveSendCardStatusByCode: Record<number, ReceiveSendCardStatus> = {
  /** 领取成功 */
  [SUCCESS_CODE]: ReceiveSendCardStatus.Success,
  /** 已拥有卡片 */
  [CAN_NOT_RECEIVE_POINT_ERROR_CODE]: ReceiveSendCardStatus.HasOwned,
  /** 卡片已被领取 */
  [HAS_RECEIVED]: ReceiveSendCardStatus.HasReceived,
};

/** 集卡组件 hook 参数 */
export interface UseCollectCardsHookParams {
  /** 组件 ID */
  componentID: string;
  /** 模块 ID */
  modID: string;
  /** 抽奖模块 ID */
  lotteryModID: string;
}

/** 集卡组件 Hooks */
export function useCollectCardsHook(
  params: UseCollectCardsHookParams,
  /** 需要外部传入，否则获取不到 $bus */
  initStatusEvent: (status: UserCollectCardStatus) => void,
  /** 需要外部传入 */
  toast?: ((text: string) => void) | undefined,
) {
  const { componentID, modID, lotteryModID } = params;
  /** 模块 ID */
  const mID = ref(modID);
  /** 组件 ID */
  const cID = ref(componentID);
  /** 集卡 ID */
  const collectID = ref<string>(getCollectID(modID));
  /** 卡片信息 */
  const cardInfos = ref<CardInfo[]>();
  /** 奖励领取次数 */
  const receiveRewardTimes = ref<number>(0);
  /** 是否已经全部收集卡片 */
  const hasCollected = ref<boolean>(false);
  /** 加密账号 */
  const encryptId = ref<string>('');
  /** 用户集卡状态 */
  const status = ref<UserCollectCardStatus>(UserCollectCardStatus.Collecting);
  /** 卡片总数 */
  const totalCardsNum = computed(() => cardInfos.value?.length || 0);
  /** 抽取的 Q 币奖励 */
  const lotteryReward = ref<string>('');
  /** 当前已收集卡片类型数量 */
  const curCollectedNum = computed(() => {
    const hasCards = cardInfos?.value?.filter(info => info.cardNum > 0);
    return hasCards?.length || 0;
  });

  const instance = getCurrentInstance();
  const { $bus } = hooks.useMokaInject();

  /**
   * 获取赠送卡片链接
   * @param cardID 卡片 ID
   * @param username 用户名称
   * @returns 赠送卡片链接
   */
  async function getSendCardURL(
    cardID: string,
    username: string,
  ) {
    console.log('[shareSendCard] 开始获取分享 shareKey', collectID, cardID);
    const launchSendResp = await launchSend(
      cID.value,
      mID.value,
      {
        collect_id: collectID.value,
        card_id: cardID,
      },
    );

    const { code, msg } = launchSendResp;
    if (code !== SUCCESS_CODE || !launchSendResp?.data?.sendKey) {
      console.error('[shareSendCard] 获取分享 shareKey 失败', collectID.value, cardID, launchSendResp);
      toast?.(msg ?? '系统繁忙，请稍后再试～');
      return;
    }

    console.log('[shareSendCard] 获取分享赠送 shareKey 成功', collectID, cardID, launchSendResp.data.sendKey);
    const shareURL = getShareUrlWithParams({
      [COLLECT_ID]: collectID.value,
      [CARD_ID]: cardID,
      [CARD_SHARE_TYPE]: CardShareType.Send,
      [CARD_SEND_KEY]: launchSendResp.data.sendKey,
      [USER_NAME]: username,
    }, [CARD_ASK_KEY, CARD_ID, CARD_SHARE_TYPE, USER_NAME, COLLECT_ID, CARD_SEND_KEY]);

    return shareURL;
  }

  /**
   * 获取索要卡片链接
   * @param cardID 卡片 ID
   * @param username 用户名称
   * @returns 索要卡片链接
   */
  function getAskCardURL(
    cardID: string,
    username: string,
  ) {
    console.log('[getAskCardURL] 开始获取分享索要 shareURL', collectID, cardID);
    const shareURL = utils.getShareUrlWithParams({
      [COLLECT_ID]: collectID.value,
      [CARD_ID]: cardID,
      [CARD_SHARE_TYPE]: CardShareType.Ask,
      [CARD_ASK_KEY]: encryptId.value,
      [USER_NAME]: username,
    }, [CARD_SEND_KEY, CARD_ID, CARD_SHARE_TYPE, USER_NAME, COLLECT_ID, CARD_ASK_KEY]);

    console.log('[getAskCardURL] 获取分享索要 shareURL 成功', shareURL);
    return shareURL;
  }

  /** 展示索要弹窗 */
  async function getShowSendAskCardPopupConfig() {
    // 校验是否允许拉起索要弹窗
    if (!validateSendAndAsk()) {
      await getUserCollectCards();
      return;
    }

    // 获取用户集卡信息
    await getUserCollectCards();
    const cardInfo = getCardInfoFromShareURL();
    const urlParams = new URLSearchParams(location.search);
    const askKey = urlParams.get(CARD_ASK_KEY);
    // 自己进入自己的分享链接，不拉起弹窗
    if (!encryptId.value || encryptId.value === askKey) {
      console.warn('[getShowSendAskCardPopupConfig] 索要者本人打开自己的索要链接');
      return;
    }

    if (!cardInfo) {
      // 卡片信息异常，上报异常分享链接
      console.error('[getShowSendAskCardPopupConfig] 索要的卡片信息异常', location.href);
      return;
    }

    return {
      cardConfig: cardInfo?.cardConfig,
    };
  }

  /** 赠送索要卡片 */
  async function sendAskCard(cardID: string): Promise<boolean | undefined> {
    console.log('[sendAskCard] 开始赠送卡片', collectID.value, cardID, encryptId.value);
    const urlParams = new URLSearchParams(location.search);
    const askKey = urlParams.get(CARD_ASK_KEY);

    // 不存在赠送者的 key
    if (!askKey) {
      toast?.(DEFAULT_ERROR_MESSAGE);
      return;
    }

    const sendAskCardResp = await askCardSend(
      cID.value,
      mID.value,
      {
        collect_id: collectID.value,
        card_id: cardID,
        ask_openid: askKey,
      },
    );

    // 未满足领取要求
    if (sendAskCardResp.code === CAN_NOT_SENT_CARD) {
      const cardInfo = cardInfos.value?.find(carInfo => carInfo.cardConfig.cardId === cardID);
      const cardNum = cardInfo?.cardNum ?? 0;
      toast?.(cardNum > 0 ? `您当前仅有${cardNum}张该卡片，无法赠送哦～` : sendAskCardResp.msg ?? DEFAULT_ERROR_MESSAGE);
      return;
    }

    // 其余非成功状态码
    if (sendAskCardResp.code !== SUCCESS_CODE) {
      console.error('[sendAskCard] 赠送卡片异常', sendAskCardResp);
      toast?.(sendAskCardResp.msg ?? '系统繁忙，请稍候再试~');
      return;
    }

    console.log('[sendAskCard] 赠送卡片成功', sendAskCardResp);
    toast?.('赠送成功!');
    await getUserCollectCards();
    return true;
  }

  /**
   * 领取赠送卡片卡片
   * @param shareKey 赠送的 shareKey
   * @returns 领取卡片的状态
   */
  async function receiveSendCard(shareKey: string) {
    if (!validateSendAndAsk()) {
      // 直接初始化
      await getUserCollectCards();
      return;
    }

    if (!shareKey) {
      console.error('[receiveSendCard] 非法 shareKey', shareKey);
      // 直接初始化
      await getUserCollectCards();
      return;
    }

    // 先执行领取
    console.log('[receiveSendCard] 开始领取赠送卡片', cID.value, mID.value, shareKey);
    const resp = await sendCardReceive(cID.value, mID.value, shareKey);
    console.log('[receiveSendCard] 领取赠送卡片成功', resp);
    // 后获取最新的集卡信息
    await getUserCollectCards();

    const { code, msg } = resp;
    if (code === undefined || ![SUCCESS_CODE, CAN_NOT_RECEIVE_POINT_ERROR_CODE, HAS_RECEIVED].includes(code)) {
      toast?.(msg ?? DEFAULT_ERROR_MESSAGE);
      return;
    }

    const cardInfo = getCardInfoFromShareURL();
    if (!cardInfo) {
      // 如果用户手动修改 card_id 导致无法获取到匹配的卡片，给予 toast 提示
      toast?.('领取赠送的卡片成功～');
      return;
    }

    return {
      status: ReceiveSendCardStatusByCode[code],
      cardConfig: cardInfo.cardConfig,
    };
  }

  /**
   * 校验赠送和索要
   * @returns
   */
  function validateSendAndAsk() {
    if (!getLoginUserOpenID()) {
      console.warn('[validateSendAndAsk] 领取时用户未登录');
      return false;
    }

    // TODO: 后续需要支持端外打开需要修改
    if (!isYYB()) {
      console.warn('[validateSendAndAsk] 领取时不在应用宝内');
      return false;
    }

    const urlParams = new URLSearchParams(location.search);
    const receivedCollected = urlParams.get(COLLECT_ID);
    if (receivedCollected !== collectID.value) {
      console.warn('[validateSendAndAsk] 领取时集卡 ID 不匹配');
      return false;
    }

    return true;
  }

  /**
   * 根据 URL 参数获取卡片信息
   * @returns 卡片信息
   */
  function getCardInfoFromShareURL() {
    const urlParams = new URLSearchParams(location.search);
    const cardID = urlParams.get(CARD_ID);

    const info = cardInfos.value?.find(cardInfo => String(cardInfo.cardConfig.cardId) === cardID);
    return info;
  }

  /** 获取集卡组件配置 */
  async function getUserCollectCards() {
    console.log('[getUserCollectCards] 开始获取用户集卡信息', { collectID: collectID.value, modID: mID.value });
    const searchUserCardsResp = await searchUserCardList(cID.value, mID.value, collectID.value);

    // 异常情况
    if (searchUserCardsResp.code !== 0 || !searchUserCardsResp.data) {
      console.error('[getUserCollectCards] 获取用户集卡信息失败', { collectID: collectID.value, modID: mID.value }, searchUserCardsResp);
      toast?.(searchUserCardsResp.msg ?? DEFAULT_ERROR_MESSAGE);
      return;
    }

    console.log('[getUserCollectCards] 获取用户集卡信息成功', searchUserCardsResp);
    cardInfos.value = searchUserCardsResp.data.cardList;
    receiveRewardTimes.value = searchUserCardsResp.data.receiveRewardTimes;
    hasCollected.value = searchUserCardsResp.data.hasCollected;
    encryptId.value = searchUserCardsResp.data.encryptId;

    // TODO: 后续抽离成配置化形式
    if (receiveRewardTimes.value > 0) {
      status.value = UserCollectCardStatus.Finished;
      await getUserLotteryReward();
      initStatusEvent(status.value);
    } else {
      status.value = hasCollected.value ? UserCollectCardStatus.Collected : UserCollectCardStatus.Collecting;
      initStatusEvent(status.value);
    }

    $bus?.$emit(COMPONENT_EVENT.UpdateCollectInfo, instance?.proxy);

    console.log('[getUserCollectCards] 当前用户集卡状态为', status.value);
  }

  /** 初始化集卡组件 */
  async function initCollectCards() {
    console.log('[initCollectCards] 初始化集卡组件', cID.value, mID.value, collectID.value);
    await getUserCollectCards();
    console.log('[initCollectCards] 初始化集卡组件成功', cID.value, mID.value, collectID.value);
  }

  /** 获取抽奖奖励 */
  async function getUserLotteryReward() {
    if (!lotteryModID) {
      lotteryReward.value = '';
      return;
    }

    console.log('[getUserLotteryReward] 开始抽奖奖励信息');
    const resp = await getLotteryReward(cID.value, lotteryModID, getLotteryID(lotteryModID));
    console.log('[getUserLotteryReward] 获取抽奖奖励信息成功', resp);
    const { code, data } = resp;
    if (code !== SUCCESS_CODE || !data) {
      lotteryReward.value = '';
      return;
    }

    lotteryReward.value = data?.propertyResults[0]?.instanceInfo?.desc ?? '';
  }

  return {
    cardInfos,
    totalCardsNum,
    curCollectedNum,
    status,
    lotteryReward,
    getUserCollectCards,
    initCollectCards,
    getSendCardURL,
    getAskCardURL,
    sendAskCard,
    getShowSendAskCardPopupConfig,
    receiveSendCard,
  };
}
