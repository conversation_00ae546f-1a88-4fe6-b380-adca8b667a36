import { createApp, h } from 'vue';

import Popup from '../components/popup/index.vue';
import type { CardConfig, PopupConfigs, PopupStateConfig, SendPopupConfigs } from '../types';
import { replaceTemplate } from '../utils';
import { ReceiveSendCardStatus } from './use-collect-cards-hook';

/** 集卡弹窗配置 */
export interface CollectCardsPopupConfigs {
  /** 弹窗标题 */
  title: string;
  /** 卡片图片 */
  cardCover: string;
  /** 弹窗标题颜色 */
  titleColor?: string;
  /** 弹窗描述 */
  tips?: string;
  /** 弹窗描述颜色 */
  tipsColor?: string;
  /** 背景图 */
  backgroundImage?: string;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 按钮配置 */
  buttonConfig: {
    /** 按钮文本 */
    text: string;
    /** 按钮文本 */
    color?: string;
    /** 按钮文案背景图 */
    textImage?: string;
    /** 按钮背景图 */
    backgroundImage?: string;
    /** 按钮背景图 */
    backgroundColor?: string;
  };
}

/** 赠送弹窗状态对应配置 key */
export const sendPopupConfigsKeyByStatus: Record<ReceiveSendCardStatus, keyof SendPopupConfigs> = {
  /** 领取成功 */
  [ReceiveSendCardStatus.Success]: 'success',
  /** 已拥有卡片 */
  [ReceiveSendCardStatus.HasOwned]: 'hasOwned',
  /** 已领取卡片 */
  [ReceiveSendCardStatus.HasReceived]: 'hasReceived',
};

export function usePopupHook(popupConfigs: PopupConfigs) {
  /**
   * 展示领取赠送 / 索要卡片弹窗
   * @param cardConfig 卡片配置
   * @param popupStateConfigs 弹窗状态配置
   * @param username 用户名
   */
  function showSendOrAskCardPopup(
    cardConfig: CardConfig,
    popupStateConfigs: PopupStateConfig,
    username: string,
    confirm?: () => boolean | Promise<boolean>,
  ) {
    const { activatedImg, cardName } = cardConfig;
    const { tips, title, btnText, btnTextImage } = popupStateConfigs;
    const {
      tipsColor,
      titleColor,
      btnBackgroundImage,
      btnTextColor,
      backgroundImage,
      btnBackgroundColor,
      backgroundColor,
    } = popupConfigs;

    disableScroll();
    showPopup(
      {
        title,
        tips: replaceTemplate(tips, { cardName, username }),
        titleColor,
        tipsColor,
        backgroundImage,
        backgroundColor,
        cardCover: activatedImg,
        buttonConfig: {
          text: btnText,
          color: btnTextColor,
          textImage: btnTextImage,
          backgroundImage: btnBackgroundImage,
          backgroundColor: btnBackgroundColor,
        },
      },
      confirm,
    );
  }

  return {
    showSendOrAskCardPopup,
  };
}

/**
 * 展示弹窗
 * @param config 弹窗配置
 * @param confirm 点击按钮确认函数
 */
function showPopup(
  config: CollectCardsPopupConfigs,
  confirm?: () => boolean | Promise<boolean>,
) {
  let container = document.getElementById('collect-cards-popup-modal');
  // 不存在该容器
  if (!container) {
    container = document.createElement('div');
    container.classList.add('collect-cards-popup-modal');
    document.documentElement.appendChild(container);
  }

  const app = createApp({
    render() {
      return h(Popup, {
        config,
        onClose: () => {
          enableScroll();
          app.unmount();
          document.documentElement.removeChild(container as HTMLElement);
        },
        onConfirm: async () => {
          let needToClose = true;
          if (confirm) {
            needToClose = await confirm();
          }

          // 需要关闭，则关闭
          if (needToClose) {
            enableScroll();
            app.unmount();
            document.documentElement.removeChild(container as HTMLElement);
          }
        },
      });
    },
  });

  app.mount(container);
}

/** 禁用 body 滚动，用于解决滚动穿透问题 */
function disableScroll() {
  document.body.style.overflow = 'hidden';
}

/** 重新启用 body 滚动 */
function enableScroll() {
  document.body.style.overflow = '';
}
