import { throttle } from 'radash';
import { computed, onMounted, onUnmounted, Ref, ref } from 'vue';

import { MAIN_CONTAINER_PADDING_WIDTH } from '../constant/index';

/**
 * 使用滚动遮罩
 * @param scrollContainerRef 滚动容器
 */
export function useScrollMask(scrollContainerRef: Ref<HTMLElement | undefined>) {
  const isShowOverlay = ref(false);

  /** 判断是否有滚动条 */
  const hasScrollBar = computed(() => {
    const container = scrollContainerRef.value;
    if (!container) return false;

    return container.scrollWidth > container.clientWidth - (MAIN_CONTAINER_PADDING_WIDTH * 2);
  });

  /** 检查是否可以滚动，此处防抖 50 ms */
  const debounceCheckMainContainerScroll = throttle({ interval: 50 }, () => {
    const container = scrollContainerRef.value;
    if (!container) return;

    // 检查内容是否全部展示
    isShowOverlay.value = hasScrollBar.value
    && container.scrollLeft + MAIN_CONTAINER_PADDING_WIDTH < (container.scrollWidth - container.clientWidth);
  });

  /** 初始化滚动遮罩 */
  const initScrollMask = () => {
    onMounted(() => {
      // 滚动事件
      scrollContainerRef.value?.addEventListener('scroll', debounceCheckMainContainerScroll);
      // 监听窗口大小变化事件
      window.addEventListener('resize', debounceCheckMainContainerScroll);
      // 不存在滚动条，则隐藏遮罩，否则则展示
      isShowOverlay.value = hasScrollBar.value;
    });

    onUnmounted(() => {
      // 滚动事件
      scrollContainerRef.value?.removeEventListener('scroll', debounceCheckMainContainerScroll);
      // 监听窗口大小变化事件
      window.removeEventListener('resize', debounceCheckMainContainerScroll);
    });
  };

  return {
    isShowOverlay,
    hasScrollBar,
    initScrollMask,
  };
}
