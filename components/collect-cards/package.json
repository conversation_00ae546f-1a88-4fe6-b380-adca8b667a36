{"name": "@tencent/moka-ui-collect-cards", "version": "0.0.5", "description": "活动中台组件模板", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "scripts": {"dev": "vite"}, "author": "ziyanou<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-collect-cards"], "repository": {"type": "git", "url": "", "directory": "components/collect-cards"}, "dependencies": {"vue": "^3.3.4", "@tencent/mole-report": "^1.0.0", "@tencent/moka-ui-domain": "moka-latest", "@tencent/mole-jsbridge": "^1.0.4", "@tencent/moka-data-core": "^1.0.38", "humps": "^2.0.1", "radash": "^12.1.0"}, "devDependencies": {"turbo": "^1.5.6", "husky": "^8.0.3", "sass": "^1.43.4", "@types/humps": "^2.0.6"}}