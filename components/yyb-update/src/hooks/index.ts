import { showDialog } from 'vant';

import { UPDATE_YYB_VERSION_URL } from '../constant';
import { ComponentConfig } from '../types';

export function updateYyb(config: ComponentConfig) {
  void showDialog({
    message: config.updateTips || '更新应用宝后，可领取奖励',
    confirmButtonText: '更新应用宝',
    showCancelButton: true,
    beforeClose: async (action: string) => {
      if (action === 'cancel') return true;
      location.href = UPDATE_YYB_VERSION_URL;
      return true;
    },
  });
}
