<template>
  <div />
</template>

<script setup lang="ts">
import 'vant/es/dialog/style';
import { defineProps, onMounted } from 'vue';
import { constants, hooks, store } from '@tencent/moka-ui-domain';

import { updateYyb } from '../hooks';
import type { ComponentConfig } from '../types';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { $bus } = hooks.useMokaInject();
const { useYybUpdateStore } = store;
const yybUpdateStore = useYybUpdateStore();
const { UPDATE_YYB_EVENT } = constants;

onMounted(() => {
  console.log('挂载应用宝更新组件');
  yybUpdateStore.checkVersion(props.config.minYybVersion);
  if (yybUpdateStore.isVersionValid) return;
  console.log('应用宝版本低，需要更新');
  $bus?.$on(UPDATE_YYB_EVENT, () => {
    updateYyb(props.config);
  });
});

</script>
