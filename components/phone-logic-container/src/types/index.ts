import { OSType } from '../utils/device';

/** 屏蔽规则 */
export interface BlockRule {
  /** 品牌，多个品牌用逗号分隔 */
  brands?: string;
  /** 机型，多个机型用逗号分隔 */
  models?: string;
  /** 安卓最低版本 */
  minAndroidVersion?: string;
  /** 手机系统 ios/android */
  os?: OSType;
}

/** 设备信息 */
export interface DeviceInfo {
  /** 品牌 */
  brand: string;
  /** 型号 */
  model: string;
  /** 安卓版本 */
  androidVersion: string;
}

export type PhoneConfig = { containerId: string } & BlockRule;
