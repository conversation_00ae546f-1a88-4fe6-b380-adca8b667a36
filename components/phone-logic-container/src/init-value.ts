/**
 * 生成组件的唯一 ID，对齐 moka 平台的生成
 * @param componentName 组件名称，中划线格式
 * @param digit 位数，默认值8
 */
function generateComponentUniqueID(componentName: string, digit = 8) {
  // 生成指定位数的 GUID，无【-】格式
  const guid = 'x'.repeat(digit).replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });

  return `${componentName}_${guid}`;
}

const container = {
  type: 'combination-item',
  style: {
    width: '100%',
    backgroundColor: 'rgba(201, 201, 201, 1)',
  },
  layout: 'absolute',
  tabTitle: {
    style: {
      width: '100%',
      backgroundColor: 'rgba(201, 201, 201, 1)',
      height: '40',
    },
    disabledStyle: {
      backgroundColor: 'rgba(226, 226, 226, 0.5)',
    },
  },
  items: [],
  devconfig: {
    lock: true,
    pack: false,
    resizable: false,
    aspectRatio: false,
    ratio: 1,
  },
};

export default {
  type: 'moka-ui-phone-logic-container',
  phoneConfigs: [{
    containerId: '显示容器【2】',
    minAndroidVersion: '',
    brands: '',
    models: '',
  }],

  items: [{
    id: generateComponentUniqueID('combination-item'),
    name: '默认容器',
    title: '默认容器',
    ...JSON.parse(JSON.stringify(container)),
  }, {
    id: generateComponentUniqueID('combination-item'),
    name: '显示容器【2】',
    title: '显示容器【2】',
    ...JSON.parse(JSON.stringify(container)),
  }],

  style: {
    width: '100%',
    height: '250',
    position: 'absolute',
    left: 0,
  },

  packageName: '',
};
