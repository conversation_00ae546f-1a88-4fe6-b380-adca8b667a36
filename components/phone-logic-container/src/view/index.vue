<template>
  <div
    :id="config.id"
    class="moka-ui-phone-logic-container"
    :style="containerStyle"
  >
    <div
      class="moka-ui-phone-logic-container-header"
      :class="{ invisible: !inMagic }"
      :style="`height: ${navHeight / 100}rem; line-height: ${
        navHeight / 100
      }rem;`"
    >
      点击浅蓝区域，选中容器组件
    </div>
    <template v-for="(item, index) in config.children">
      <proxy-component
        is="Container"
        v-if="index === active"
        :key="item.name"
        :style="formatItemStyle(item.style)"
        :config="{
          ...item.props,
          children: item.children,
          meta: item.meta,
          componentName: item.componentName,
        }"
      >
        <dynamic-container :schema="convertConfigToSchema(item)" />
      </proxy-component>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, onMounted, ref, watch } from 'vue';
import { transformStyle } from '@tencent/magic-ui-util';
import { hooks, store, utils } from '@tencent/moka-ui-domain';

import type { BlockRule, PhoneConfig } from '../types';
import { getDeviceInfo, IS_SERVER, OSType, shouldBlock } from '../utils/device';

const props = defineProps<{
  config: {
    phoneConfigs: PhoneConfig[];
    packageName: string;
  };
}>();

const { usePartitionRoleStore, getUserKey } = store;
const partitionStore = usePartitionRoleStore();

const userSelectOS = ref(OSType.UNKNOWN);

const openID = utils.getLoginUserOpenID();
const userKey = getUserKey(props.config.packageName, openID);
partitionStore.$subscribe((mutation, state) => {
  const partitionRole = state.partitionRolesByOpenIDAndPackageName[userKey];
  if (partitionRole?.osType?.id) userSelectOS.value = partitionRole.osType.id;

  console.log('[phone-logic-container] $subscribe:', mutation, state, userSelectOS.value);
});

watch(userSelectOS, (newVal, oldVal) => {
  console.log(`[phone-logic-container] userSelectOS newVal: ${newVal}, oldVal: ${oldVal}`);

  if (newVal !== oldVal) {
    void initContainer(userSelectOS.value);
  }
});

// 样式相关
const navHeight = computed(() => Math.max(...props.config.children.map(item => item.props.tabTitle.style.height)));
const concentHeight = computed(() => parseInt(props.config.originalStyle.height, 10) - navHeight.value);
const containerStyle = computed(() => {
  let style = props.config.originalStyle || {};

  if (typeof style === 'function') {
    style = style.bind(this)(this, { model: props.model });
  } else {
    style = transformStyle(style);
  }

  return style;
});

// 将配置转成组件的格式
const convertConfigToSchema = config => ({
  componentsTree: [config],
});

const formatItemStyle = (style = {}) => {
  const configStyle = transformStyle(style);
  configStyle.width = '100% !important';
  if (concentHeight.value > 0) {
    configStyle.height = `${concentHeight.value / 100}rem`;
  }

  return configStyle;
};

// 激活容器索引
const active = ref(-1);
const { inMagic, $bus } = hooks.useMokaInject();
const initListener = () => {
  if (!$bus) return;
  $bus.$on('select-in-editor', (el) => {
    const { node, parent } = el;
    let index = -1;

    if (
      parent.type.includes('phone-logic-container')
          && node.type === 'combination-item'
    ) {
      // 编辑器模式下，点击子容器自动切换
      index = parent.children.findIndex(item => item.id === node.id);
    } else if (parent.type === 'combination-item') {
      // 编辑器模式下，点击子容器下的子组件也能自动切换
      index = props.config.children.findIndex(item => item.props.id === parent.id);
    }

    if (index > -1) {
      active.value = index;
    }
  });
};

const DEFAULT_CONTAINER_ID = '默认容器';

/**
 * 设置显示容器
 * @param containerId 容器 id
 */
const setContainer = (containerId: string) => {
  active.value = props.config.children.findIndex(item => item.name === containerId);
};

// 屏蔽规则和容器 id 的映射 Map
const blockRulesByContainerId = new Map<string, BlockRule[]>();
props.config.phoneConfigs.forEach((config) => {
  const { containerId, brands, models, minAndroidVersion, os } = config;
  if (!blockRulesByContainerId.has(containerId)) {
    blockRulesByContainerId.set(containerId, []);
  }

  const blockRules = blockRulesByContainerId.get(containerId);
  blockRules?.push({ brands, models, minAndroidVersion, os });
});

// 初始化容器
const initContainer = async (userSelectOSType: OSType) => {
  if (IS_SERVER) return;
  const deviceInfo = await getDeviceInfo(navigator.userAgent);
  console.log('[phone-logic-container] deviceInfo: ', userSelectOSType, deviceInfo, blockRulesByContainerId);

  // 容器 id 的形式为 显示容器【2】、显示容器【3】、显示容器【4】，需要根据 2、3、4 从小到大排序
  const sortedContainerId = [...blockRulesByContainerId.keys()].sort((a, b) => {
    // 使用正则表达式提取数字
    const numA = parseInt(/\d+/.exec(a)![0], 10);
    const numB = parseInt(/\d+/.exec(b)![0], 10);
    return numA - numB;
  });

  const containerId = sortedContainerId.find((id) => {
    const blockRules = blockRulesByContainerId.get(id)!;
    const isBlock = shouldBlock(deviceInfo, userSelectOSType, blockRules);
    return !isBlock;
  });

  console.log('[phone-logic-container] containerId: ', containerId);

  setContainer(containerId || DEFAULT_CONTAINER_ID);
};

onMounted(() => {
  if (inMagic) {
    active.value = 0;
    initListener();
  } else {
    void initContainer(userSelectOS.value);
  }
});
</script>

<style lang="scss" src="./index.scss" scoped />
