import MoleJSBridge from '@tencent/mole-jsbridge';

import type { BlockRule, DeviceInfo } from '../types';

/** 是否是服务端环境 */
export const IS_SERVER = typeof window === 'undefined';

/** 系统类型 */
export enum OSType {
  /** 未知 */
  UNKNOWN = 0,
  /** 安卓系统 */
  ANDROID = 1,
  /** IOS系统 */
  IOS = 2,
}

/**
 * 是否为应用宝环境
 * @param userAgent 浏览器 ua
 */
function isYYB(userAgent: string) {
  return /\/qqdownloader\/(\d+)(?:\/(appdetail|external))?/.test(userAgent);
}

/**
 * 是否为安卓
 * @param userAgent 浏览器 ua
 */
function isAndroid(userAgent: string) {
  return /android/i.test(userAgent) || (/UCBrowser/i.test(userAgent) && /Adr[\s/]*([\d.]+)/.test(userAgent));
}

/** 获取手机的品牌和型号 */
async function getPhoneBrandAndModel() {
  const jsbridge = MoleJSBridge.getJSBridge();
  const resp = await jsbridge?.device?.getDeviceInfo();
  const { code, data } = resp || {};
  if (code !== 0 || !data?.qua) {
    console.error('[phone-logic-container] getPhoneBrandAndModel error', resp);
    return {
      brand: '',
      model: '',
    };
  }

  const deviceInfoStr = (data.qua as string).split('&')[4]; // 与终端确认设备信息固定在 qua 字符串 & 的第三段
  const [brand, model] = deviceInfoStr.split('_');

  return {
    brand,
    model,
  };
}

/**
 * 获取设备信息
 * @param userAgent 浏览器 ua
 */
export async function getDeviceInfo(userAgent: string): Promise<DeviceInfo | null> {
  if (!isAndroid(userAgent) || !isYYB(userAgent)) return null;
  const { brand, model } = await getPhoneBrandAndModel();
  const android = /([a|A]ndroid)[\s/]*([\d.]+)/.exec(userAgent);
  const [, , androidVersion] = android || [];
  // 任意设备信息获取失败返回 null
  if (!androidVersion || !brand || !model) return null;
  return {
    androidVersion,
    brand,
    model,
  };
}

/**
 * 判断用户的设备信息是否在屏蔽规则中
 * @param deviceInfo 用户设备信息
 * @param userSelectOS 用户选择的系统类型
 * @param blockRules 屏蔽规则
 * @returns 是否需要屏蔽
 */
export function shouldBlock(deviceInfo: DeviceInfo | null, userSelectOS: OSType, blockRules: BlockRule[]) {
  console.log('[phone-logic-container] shouldBlock', deviceInfo, userSelectOS, blockRules);
  for (const rule of blockRules) {
    const brands = rule.brands ? rule.brands.split(',').map(item => item.toLocaleLowerCase()) : [];
    const models = rule.models ? rule.models.split(',').map(item => item.toLocaleLowerCase()) : [];

    // 配置了该容器可见的系统, 用户选择的手机系统非该系统，则需要屏蔽该容器
    if (rule.os) {
      const isNeedBlock = userSelectOS !== rule.os;
      console.log(`[phone-logic-container] isNeedBlock: ${isNeedBlock}, rule.os: ${rule.os}, userSelectOS: ${userSelectOS}`);
      return isNeedBlock;
    }

    if (!deviceInfo) return true;
    const { androidVersion } = deviceInfo;

    // 配置了最低版本，且用户的版本低于配置的最低版本
    if (rule.minAndroidVersion && Number(androidVersion) < Number(rule.minAndroidVersion)) {
      // 未配置品牌，全量屏蔽
      if (!brands.length) return true;

      const isBrandAndModelBlock = checkBrandAndModelBlock(brands, models, deviceInfo);
      if (isBrandAndModelBlock) return isBrandAndModelBlock;
    }

    // 未配置最低版本
    if (!rule.minAndroidVersion) {
      const isBrandAndModelBlock = checkBrandAndModelBlock(brands, models, deviceInfo);
      if (isBrandAndModelBlock) return isBrandAndModelBlock;
    }
  }

  return false;
}

/**
 * 检查手机品牌和型号
 * @param brands 屏蔽的品牌列表
 * @param models 屏蔽的型号列表
 * @param deviceInfo 设备信息
 */
export function checkBrandAndModelBlock(brands: string[], models: string[], deviceInfo: DeviceInfo) {
  const { brand, model } = deviceInfo;
  // 配置了品牌屏蔽，且用户手机品牌命中了品牌屏蔽
  if (brands.includes(brand.toLocaleLowerCase())) {
    // 未配置型号，全量屏蔽
    if (!models.length) return true;
    // 配置了型号 && 命中了型号屏蔽
    if (models.includes(model.toLocaleLowerCase())) {
      return true;
    }
  }

  return false;
}
