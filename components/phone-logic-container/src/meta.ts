import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        phoneConfigOptionalBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '容器屏蔽规则',
          },
          properties: {
            phoneConfigs: {
              type: 'array',
              'x-decorator': 'FormItem',
              'x-component': 'ArrayTable',
              'x-component-props': {
                emptyText: '暂无数据',
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '容器Id',
                      align: 'center',
                    },
                    properties: {
                      containerId: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          placeholder: '请输入容器id',
                        },
                      },
                    },
                  },
                  column2: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '版本',
                      align: 'center',
                    },
                    properties: {
                      minAndroidVersion: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          placeholder: '请输入正常展示的最低安卓版本',
                        },
                      },
                    },
                  },
                  column3: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '手机品牌',
                      align: 'center',
                    },
                    properties: {
                      brands: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          placeholder: '请输入需要屏蔽的手机品牌(英文逗号分隔)',
                        },
                      },
                    },
                  },
                  column4: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '手机型号',
                      align: 'center',
                    },
                    properties: {
                      models: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-component-props': {
                          placeholder: '请输入需要屏蔽的手机型号(英文逗号分隔)',
                        },
                      },
                    },
                  },
                  column5: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '手机系统',
                      align: 'center',
                    },
                    properties: {
                      os: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Select',
                        'x-component-props': {
                          placeholder: '请选择需要展示的手机系统',
                        },
                        enum: [
                          { label: '安卓', value: 1 },
                          { label: 'ios', value: 2 },
                        ],
                      },
                    },
                  },
                  column6: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '操作',
                      prop: 'operations',
                      width: 50,
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  title: '新增条目',
                },
              },
            },
            packageName: {
              type: 'string',
              title: '包名（区服系统选择专用）',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
