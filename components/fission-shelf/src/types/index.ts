/** 组件配置 */
export interface ComponentConfig {
  id: string;
  appId: number;
  sourceId: string;
}

export interface SkuDetail {
  /** 兑换类型 */
  exchangeType?: EExchangeType;
  /** 二级分类 */
  secondLevelCategory?: number;
  /** ID */
  id?: string;
  /** 模板ID */
  templateId?: number;
  /** 礼品ID */
  giftId?: (string|null);
  /** 名称 */
  name?: (string|null);
  /** 图片 */
  image?: string;
  /** 原始兑换点 */
  originExchangePoint?: number;
  /** 兑换点 */
  exchangePoint?: string;
  /** 子标记 */
  subMark?: string;
  /** 限制提示 */
  limitHint?: (string|null);
  /** 折扣 */
  discount?: (string|null);
  /** 兑换状态 */
  exchangeStatus?: EGiftStatus;
  /** 预兑换提醒 */
  preExchangeRemind?: {
    title: string;
    tips: string[];
  };
  /** 应用信息 */
  appInfo?: {
    appid: string;
  };
  /** 过期时间 */
  expireAt?: number;
  /** 价值 */
  value?: number;
  /** 阈值 */
  threshold?: number;
  /** 支持的游戏数量 */
  supportGameSum?: number;
  /** 收到后的有效天数 */
  validDaysAfterReceived?: number;
}

export interface SpuDetail {
  description: string;
  id: string;
  image: string;
  name: string;
  orderOnShelf: string;
  selectSkuId: string;
  skuList: SkuDetail[];
  shelfKey: {
    id: number;
  };
}

export enum TemplateGiftType {
  /** 占位使用(无意义) */
  TEMPLATE_GIFT_TYPE_NONE = 0,
  /** 宝券 */
  TEMPLATE_GIFT_TYPE_COUPON = 1,
  /** 精品免首充宝券 */
  PREMIUM_FREE_FIRST_COUPON = 2,
  /** 道具（目前仅精品游戏） */
  TEMPLATE_GIFT_TYPE_PROP = 3,
  /** 月卡 */
  TEMPLATE_GIFT_TYPE_MONTH_CARD = 4,
  /** 周卡 */
  TEMPLATE_GIFT_TYPE_WEEK_CARD = 5,
  /** 精品满减券 */
  JP_DISCOUNT_COUPON = 6,
  /** 联运免首充宝券类型 */
  LY_FREE_FIRST_COUPON = 7,
  /** 王者荣耀道具 */
  TEMPLATE_GIFT_WZ_PROP = 8,
  /** QB类型 */
  TEMPLATE_GIFT_QB = 9,
  /** 联运累充宝券 */
  TEMPLATE_GIFT_TYPE_LY_ACCUMULATIVE_COUPON = 10,
  /** 腾讯视频会员 */
  TEMPLATE_GIFT_TYPE_TX_VIDEO_MEMBERSHIP = 11,
  /** 鼎信视听会员 */
  TEMPLATE_GIFT_TYPE_DX_VIDEO_MEMBERSHIP = 12,
  /** 实物 */
  TEMPLATE_GIFT_TYPE_INKIND_GOODS = 13,
  /** 王者实物 */
  SECOND_LEVEL_CATEGORY_WZ_INKIND_GOODS = 15,
  /** 微信立减金 */
  WX_COUPON = 16,
  /** 联运游戏道具 */
  LY_GAME_PROP = 17,
  /** 代金券 */
  TEMPLATE_CDKEY_VOUCHER_PROP = 18,
}

/** 全部兑换类型 */
export enum EExchangeType {
  /** 红包 */
  RED_PACKET = 0,
  /** QB */
  GIFT_QB = 1,
  /** 视频VIP 腾讯视频爱玩发放能力 */
  GIFT_VIP = 2,
  /** 宝券 */
  COUPON = 3,
  /** 游戏道具 */
  GAME_PROP = 4,
  /** 增值会员（橙券） 应用宝福利平台的发放能力 */
  MEMBER_SHIP = 5,
  /** 月卡 */
  MONTH_CARD = 6,
  /** 周卡 */
  WEEK_CARD = 7,
  /** 实物 */
  IN_KIND_GOOD = 8,
  /** 加量包 */
  INCREMENT_PACKAGE = 9,
  /** 微信立减金 */
  WECHAT_COUPON = 10,
  /** 联运游戏礼包 */
  GIFT_COOP_GAME_PROP = 11,
}

export enum EGiftStatus {
  GIFT_STATUS_NORMAL = 0,
  /** 已兑完 */
  GIFT_STATUS_EXCHANGE_OUT = 1,
  /** 达到礼包维度日限制 */
  GIFT_STATUS_GIFT_DAY_LIMIT = 2,
  /** 已领取 */
  GIFT_STATUS_GIFT_HAS_GOT = 3,
  /** 不可兑换 */
  GIFT_STATUS_GIFT_CANT_EXCHANGE = 4,
  /** 达到用户周期限量 */
  GIFT_STATUS_USER_LIMIT = 5,
  /** 前端弹窗提示 */
  GIFT_STATUS_RECEIVE_REMIND = 6,
}

export interface GetActivityRecommendGoodsRsp {
  goods: SpuDetail[];
  msg: string;
  ret: number;
}
