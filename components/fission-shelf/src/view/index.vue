<template>
  <div
    class="fission-shelf"
    dt-eid="card"
    :dt-params="utils.getReportParamsStr(cardReportParam, inMagic)"
  >
    <div class="top-area">
      <div class="title" />
      <div
        v-if="!isLogin"
        class="sub-title"
        dt-eid="button"
        :dt-params="utils.getReportParamsStr({
          ...cardReportParam,
          btn_title: '登录查看剩余积分'
        }, inMagic)"
        @click="openLogin"
      >
        登录查看剩余积分
      </div>
      <div
        v-else
        class="sub-title"
      >
        账户总积分：{{ points }}（价值{{ Math.floor(Number(points) / 1000 * 100) / 100 }}元）
      </div>
    </div>
    <div
      v-if="spuList.length > 0"
      class="spu-list"
    >
      <div
        v-for="(row, index) in spuList"
        :key="index"
        class="row"
      >
        <spu-card-item
          v-for="(product, index) in row"
          :key="product?.id || index"
          :spu="product!"
          :position="index + 1"
          :source-id="config.sourceId"
          :app-id="config.appId"
          :report-dict="cardReportParam"
        />
      </div>
    </div>
    <div
      v-else
      class="loading-hold"
    >
      加载中...
    </div>
    <div class="bottom-area">
      <p
        dt-eid="button"
        :dt-params="utils.getReportParamsStr({
          ...cardReportParam,
          btn_title: '查看更多可兑福利'
        }, inMagic)"
        @click="jumpPointsMall"
      >
        查看更多可兑福利 >
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { hooks, store, utils } from '@tencent/moka-ui-domain';

import { getActivityRecommendGoods, getMemberPoint } from '../api';
import { SPLIT_NUM } from '../constant';
import type { ComponentConfig, SpuDetail } from '../types';
import { chunkArray } from '../utils/chunk';
import { goPointsMallByProduct } from '../utils/jump';
import SpuCardItem from './spu-card-item.vue';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { inMagic, isLogin, loginReady } = hooks.useMokaInject();

const spuList = ref<(SpuDetail | null)[][]>([]);

const points = ref<number>(0);

const initActivityRecommendGoods = async () => {
  const resp = await getActivityRecommendGoods({
    appid: props.config.appId,
  });

  // 无数据
  if (!resp) return;

  spuList.value = chunkArray(resp.goods, SPLIT_NUM);
};

const initVipPoints = async () => {
  await loginReady;
  if (!isLogin.value) return;
  const remotePoints = await getMemberPoint({
    id: props.config.id,
  });
  if (!remotePoints) return;
  points.value = remotePoints;
};

const initData = async () => {
  // 获取推荐数据
  void initActivityRecommendGoods();
  // 获取登录积分
  void initVipPoints();
};

const jumpPointsMall = () => {
  goPointsMallByProduct(props.config.sourceId);
};

void initData();

const openLogin = () => {
  (window as any).vipCommonLogic?.openLogin();
};

const cardReportParam = {
  mod_id: 'point_exchange_card',
  mod_title: '积分兑换卡',
};

const { useFissionBoostStore } = store;
const fissionStore = useFissionBoostStore();
watch(() => fissionStore.hasActivatedVip, (cur) => {
  if (!cur) return;
  void initData();
});
</script>

<style lang="scss" src="./index.scss" scoped />
