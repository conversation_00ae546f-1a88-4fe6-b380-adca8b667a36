<template>
  <div
    v-if="product"
    dt-eid="goods_card"
    :dt-params="utils.getReportParamsStr({
      ...reportDict,
      goods_id: spu.id,
    }, inMagic)"
    class="recommendProduct"
  >
    <div class="productBox">
      <div
        v-if="product.subMark"
        class="tag"
      >
        {{ product.subMark }}
      </div>
      <!-- 适配大图 SKU 封面图 -->
      <spu-icon
        v-if="isNeedAapterSkuImgTypes(product)"
        :product="product"
      />
      <img
        v-else
        class="skuImage"
        :src="product.image"
      >
    </div>
    <div
      class="productInfo"
      dt-eid="button"
      :dt-params="utils.getReportParamsStr({
        ...reportDict,
        goods_id: spu.id,
        btn_title: '兑换'
      }, inMagic)"
      :dt-cmd="`hold=${fissionStore.isReportHeld}`"
      @click="exchange"
    >
      <div class="nameBox">
        <p class="name">
          {{ product.name }}
        </p>
        <p
          class="originPoint"
        >
          {{ Number(product.originExchangePoint) ? `${product.originExchangePoint}积分` : '' }}
        </p>
      </div>
      <div
        :class="{
          purchaseBox: true,
          disbale: !canExchange
        }"
      >
        <div v-if="canExchange">
          <div
            class="value"
          >
            <span
              class="point"
            >
              {{ formattedNeedPoints.points }}
            </span>
            <span class="unit">{{ formattedNeedPoints.unit }}</span>
          </div>
        </div>
        <div class="text">
          {{ btnText }}
        </div>
      </div>
    </div>
  </div>
  <div
    v-else
    class="emptyHoldItem"
  />
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { hooks, store, utils } from '@tencent/moka-ui-domain';

import { NEED_ADAPTER_SKU_IMG_SECOND_TYPES, NEED_ADAPTER_SKU_IMG_TYPES } from '../constant';
import { EGiftStatus, SkuDetail, SpuDetail } from '../types';
import { formatPoints } from '../utils/format';
import { goPointsMallByProduct } from '../utils/jump';
import SpuIcon from './icon/spu-icon.vue';

const { useFissionBoostStore } = store;
const fissionStore = useFissionBoostStore();
const { inMagic } = hooks.useMokaInject();
const props = defineProps<{
  spu: SpuDetail;
  position: number;
  reportDict?: Record<string, string>;
  sourceId: string;
  appId: number;
}>();
const product = computed<SkuDetail|undefined>(() => {
  const { selectSkuId, skuList, description } = props.spu || {};
  const spu = skuList?.find(item => item.id === selectSkuId) || skuList?.[0];
  if (!spu) {
    return undefined;
  }

  return {
    ...spu,
    description,
    reportDict: {
      ...props.reportDict,
      position: String(props.position),
    },
  };
});

const formattedNeedPoints = computed(() => formatPoints(product.value?.exchangePoint || '0'));

const canExchange = computed(() => Number(product.value?.exchangeStatus) === EGiftStatus.GIFT_STATUS_NORMAL);

const btnText = computed(() => {
  if (canExchange.value) return '兑';
  if ([EGiftStatus.GIFT_STATUS_GIFT_CANT_EXCHANGE, EGiftStatus.GIFT_STATUS_GIFT_HAS_GOT].includes(Number(product.value?.exchangeStatus))) return '已兑换';
  return '已达限量';
});

const exchange = () => {
  if (!canExchange.value) return;
  goPointsMallByProduct(props.sourceId, props.spu);
};

// 用于判断商品是否需要适配 SKU 图片类型
const isNeedAapterSkuImgTypes = (good: SkuDetail) => {
  const isMatchFirstCategory = NEED_ADAPTER_SKU_IMG_TYPES.includes(Number(good.exchangeType));
  const isMatchSecondCategory = NEED_ADAPTER_SKU_IMG_SECOND_TYPES.includes(Number(good.secondLevelCategory));
  return isMatchFirstCategory || isMatchSecondCategory;
};
</script>
<style lang="scss" scoped>
@mixin ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommendProduct {
  width: 1rem;
  overflow: hidden;
  border-radius: 0.08rem;
  background-color: #FFFFFF;
  flex-shrink: 0;
  padding-bottom: 0.06rem;
}
.productBox {
  position: relative;
  background-color: rgba(255, 242, 226, 1);
}
.nameBox {
  padding: 0.05rem 0.06rem;
}
.tag {
  position: absolute;
  top: 0.06rem;
  left: 0.06rem;
  z-index: 10;
  max-width: calc(100% - 0.12rem);
  padding: 0.01rem 0.06rem;
  font-size: 0.1rem;
  font-weight: 500;
  color: rgb(244 131 25 / 1);
  background: #FFE7BA;
  border-radius: 0.04rem;
}
.skuImage {
  width: 1rem;
  height: 0.96rem;
  border-radius: 0.08rem 0.08rem 0 0;
}
.productInfo {
  background-color: #fff;
}
.name {
  font-size: 0.12rem;
  font-weight: 500;
  line-height: 0.14rem;
  color: #000;
  text-align: justify;
  letter-spacing: -0.371px;

  @include ellipsis;
}
.gameIcon {
  position: absolute;
  bottom: 0.1rem;
  left: 0.1rem;
  width: 0.3rem;
  height: 0.3rem;
}
.originPoint {
  flex-shrink: 0;
  overflow: hidden;
  font-size: 0.08rem;
  color: rgb(0 0 0 / 0.40);
  text-decoration: line-through;
  letter-spacing: -0.371px;
  height: 0.1rem;
}
.purchaseBox {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin: 0 0.04rem;
  height: 0.24rem;
  width: 0.88rem;
  padding: 0 0.06rem;
  background: url('https://cdn.yyb.gtimg.com/wupload/xy/yybtech/IQfdjHhY.png');
  background-size: 100% 100%;
  border-radius: 0.12rem;
  &.disbale {
    background: rgba(238, 60, 56, 0.2);
    justify-content: center;
    .text {
      color: #FFFFFF;
    }
  }
}
.value {
  display: flex;
  align-items: center;
  color: #FFFFFF;
  height: 0.24rem;
  width: 0.5rem;
  overflow: hidden;
}
.point {
  font-family: YYB;
  font-size: 0.12rem;
  line-height: 0.15rem;
  transform: translate(0, 0.01rem);
}
.unit {
  text-wrap: nowrap;
  font-size: 0.1rem;
  @include ellipsis;
}
.text {
  font-size: 0.12rem;
  line-height: 0.24rem;
  font-weight: 700;
  color: #825237;
}
.emptyHoldItem {
  width: 0.96rem;
}
</style>
