<template>
  <div :class="$style.couponIcon">
    <img
      v-if="[
        TemplateGiftType.PREMIUM_FREE_FIRST_COUPON,
        TemplateGiftType.LY_FREE_FIRST_COUPON,
      ].includes(Number(couponData.secondLevelCategory))"
      :class="$style.firstRecharge"
      :src="FIRST_FLUSH"
      alt="首充免单"
    >
    <div :class="$style.welfareValue">
      <span :class="$style.cny">¥</span>
      <span :class="$style.amount">{{ Number(couponData.value) / 100 }}</span>
    </div>
    <div :class="$style.welfareValueDesc">
      满{{ Number(couponData.threshold) / 100 }}元可用
    </div>
  </div>
</template>
<script setup lang="ts">
import { FIRST_FLUSH } from '../../constant';
import { type SkuDetail, TemplateGiftType } from '../../types';

withDefaults(defineProps<{
  couponData: SkuDetail;
  reportFirstBlush?: boolean;
}>(), {
  reportFirstBlush: false,
});

</script>
<style lang="scss" module>
.couponIcon {
  position: relative;
  display: flex;
  justify-content: center;
  width: 0.58rem;
  height: 0.36rem;
  background: url("https://vfiles.gtimg.cn/wupload/fileupload/518edc17_z-Cnn5xlRMtzbnTVVpYx1jHafPOMniJv.png");
  background-size: 100% 100%;
}
.firstRecharge {
  position: absolute;
  top: 0;
  width: 0.46rem;
  height: 0.1rem;
}
.welfareValue {
  position: absolute;
  top: 0.07rem;
  height: 0.26rem;
  color: #4E2804;
}
.cny {
  font-family: 'YYB';
  font-size: 0.13rem;
}
.amount {
  font-family: 'YYB';
  font-size: 0.15rem;
}
.welfareValueDesc {
  position: absolute;
  bottom: 0.02rem;
  width: 100%;
  font-family: Noto Sans CJK SC;
  font-size: 0.06rem;
  color: rgb(78 40 4);
  text-align: center;
}
</style>
