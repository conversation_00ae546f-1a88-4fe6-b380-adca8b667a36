<template>
  <div :class="$style.recommendProductIcon">
    <coupon-icon
      v-if="Number(product.exchangeType) === EExchangeType.COUPON"
      :coupon-data="product"
    />
    <img
      v-else
      :class="$style.adatper"
      :src="product.image"
      alt=""
    >
  </div>
</template>
<script setup lang="ts">
import { EExchangeType, SkuDetail } from '../../types';
import CouponIcon from './coupon-icon.vue';

defineProps<{
  product: SkuDetail;
}>();
</script>

<style lang="scss" module>
.recommendProductIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 0.96rem;
  background-color: rgb(255 247 224);
}
.adatper {
  width: 0.58rem;
  height: 0.36rem;
  border-radius: 0.03rem;
}
</style>
