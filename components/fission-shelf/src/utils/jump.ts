import { inject } from 'vue';
import { utils } from '@tencent/moka-ui-domain';

import type { SpuDetail } from '../types';
import { getPointsMallUrl } from './format';

/**
 * 根据产品信息跳转到积分商城。
 * @param {string} sourceId - 来源ID
 * @param {string} spu - 商品的SPU信息
 */
export function goPointsMallByProduct(sourceId: string, spu?: SpuDetail) {
  const tvfJsapi = inject<{env: {isYyb: boolean}}>('tvfJsapi');
  let param: Record<string, string> = {
    source_id: sourceId,
  };

  if (spu) {
    param = {
      ...param,
      // SPUID
      product_ids: spu.id,
      // 自动领取标识
      auto_exchange: '1',
      // 定义TAB
      points_exchange_tab: `${spu.shelfKey.id}`,
    };

    // 游戏类型传入appid
    if (spu.skuList?.length > 0 && spu.skuList[0]?.appInfo?.appid) {
      param.appid = spu.skuList[0].appInfo.appid;
    }
  }

  const url = `https://m${utils.isTestEnv() ? '-test' : ''}.iwan.yyb.qq.com/game-private/points-mall/points?${new URLSearchParams(param).toString()}`;
  utils.jumpUrl(getPointsMallUrl(url, tvfJsapi?.env.isYyb));
}
