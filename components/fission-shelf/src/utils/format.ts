/** 格式化积分，超过一万展示中文 */
export const formatPoints = (_points: string) => {
  const points = Number(_points);
  if (points >= 10000) {
    return {
      points: `${points / 10000}`,
      unit: '万积分',
    };
  }

  return {
    points: String(points),
    unit: '积分',
  };
};

export function getPointsMallUrl(url: string, isYYB = false) {
  if (isYYB) {
    return `tmast://webview?mode=0&url=${encodeURIComponent(url)}&goback=3&navStyle=1&navStyleMaskStyle=1&titlebar=0`;
  }

  return url;
}
