/**
 * 将一个数组分割成指定大小的多个子数组, 如果末尾子数组长度不足，则用 null 填充。
 * @param {T[]} array - 需要被分割的原始数组。
 * @param {number} chunkSize - 每个子数组的目标大小。
 * @returns {T[][]} - 返回一个包含所有子数组的数组。
 */
export function chunkArray<T>(array: T[], chunkSize: number): (T | null)[][] {
  const result: (T | null)[][] = [];

  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk: (T | null)[] = array.slice(i, i + chunkSize); // 获取当前分组

    // 如果当前分组的长度不足 chunkSize，则用 null 填充
    while (chunk.length < chunkSize) {
      chunk.push(null); // 用 null 填充
    }

    result.push(chunk); // 将分组添加到结果中
  }

  return result;
}
