import { camelizeKeys } from 'humps';
import { accessRequester, commonRequester } from '@tencent/moka-data-core';
import { constants, utils } from '@tencent/moka-ui-domain';

import { COMPONENT_TYPE, REQUEST_SUCCESS_CODE } from '../constant';
import { GetActivityRecommendGoodsRsp } from '../types/index';
const { ACCESS_KEY, BUSINESS_ID } = constants;

/** 激活授权状态 */
export enum ActivateAuthStatus {
  ActivatedAndAuthorized,
  ActivatedAndUnauthorized,
  InactivatedAndAuthorized,
  InactivatedAndUnauthorized,
}

/** 拉取推荐货架数据 */
export async function getActivityRecommendGoods(data: {
  /** 授权游戏 appid */
  appid: number;
}) {
  const resp = await accessRequester.request<typeof data, GetActivityRecommendGoodsRsp>({
    cmd: 'activity_recommend_goods_for_h5',
    data: {
      ...data,
    },
    businessID: BUSINESS_ID,
    accessKey: ACCESS_KEY,
    needAuthHeader: true,
  });

  const { code, tip, body: res } = resp;

  if (!res) {
    console.error(`[accessRequest: getActivityRecommendGoods] 请求异常, code: ${code}, tip: ${tip}`);
    return;
  }

  const { ret, msg } = res;

  if (ret !== REQUEST_SUCCESS_CODE) {
    console.error(`[accessRequest: getActivityRecommendGoods] 请求异常，code: ${ret}, msg: ${msg}`);
    return;
  }

  return camelizeKeys(res) as GetActivityRecommendGoodsRsp;
}

/** 查询会员积分 */
export async function getMemberPoint({ id }: {
  id: string;
}) {
  const host = utils.isTestEnv() ? 'ydd-test.yyb.qq.com' : 'ydd.yyb.qq.com';

  const resp = await commonRequester.request<any, {
    code: number;
    msg: string;
    data: string;
    quota: {name: string; quota: string}[];
  }>({
    componentID: id,
    componentType: COMPONENT_TYPE,
    url: `https://${host}/trpc.private_domain.common_page.CommonPageSvr/GetUserPointBalance`,
    withCredentials: true,
  });

  const { code, tip, body: res } = resp;

  if (!res) {
    console.error('[getMemberPoint] 获取积分异常', code, tip);
    return;
  }

  const { code: bizCode, msg, data, quota } = res;
  if (bizCode !== REQUEST_SUCCESS_CODE) {
    console.error('[getMemberPoint] 获取积分业务接口异常', bizCode, msg);
    return;
  }

  const [quotaItem] = quota ?? [];
  const { quota: quotaWz = 0 } = quotaItem ?? {};

  return Number(data) + Number(quotaWz);
}
