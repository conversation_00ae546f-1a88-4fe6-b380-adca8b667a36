import type { IPublicComponentMetaSchema } from '@tencent/moka-schema';

const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        appInfo: {
          type: 'object',
          title: '游戏配置',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'AppSelect',
          'x-component-props': {
            test: 10,
            bindType: 'appInfo',
          },
        },
        appId: {
          type: 'string',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-hidden': true,
          'x-reactions': {
            dependencies: ['..appInfo'],
            fulfill: {
              schema: {
                'x-value': '{{$deps[0].appId}}',
              },
            },
          },
        },
        sourceId: {
          type: 'string',
          title: '场景ID(sourceId)',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
