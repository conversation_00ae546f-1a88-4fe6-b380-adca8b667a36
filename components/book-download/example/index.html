<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>调试页面</title>
    <style>
      html{font-size: 100px; -ms-overflow-style: none; overflow: -moz-scrollbars-none;}
      html,body,.magic-ui {height: 100%; margin: 0;}
      html::-webkit-scrollbar, .magic-ui::-webkit-scrollbar { width: 0 !important }

      .magic-ui-pop.v-modal {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 999;
      }

      .selectedArea, .selectedArea-parent {
        z-index: 666;
      }

      .action-area {
        background-color: rgba(51, 153, 255, .5) !important;
      }

      #app {
        position: relative;
        overflow: auto;
        width: 100%;
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script>
      // moka-runtime-framework 依赖 window.magicUiconfig, 这里注入一个 magicUiconfig 对象以消除报错
      window.magicUiconfig = [{
        items: [{
          type: "page",
          items: [],
        }],
      }];
    </script>
    <script type="module" src="./main.ts"></script>
  </body>
</html>
