<script setup lang="ts">
import { ProxyComponent } from '@tencent/moka-runtime-framework';
import { merge } from 'lodash-es';
import initValue from '../src/init-value';
import { debugValue } from './debug-value';

const config: any = merge(initValue, debugValue);
const debugStyle = {top: 100, left: 100, position: 'absolute'};
console.log(config);

</script>

<template>
  <ProxyComponent is="example" :config="config" :style="merge(config.style, debugStyle)"></ProxyComponent>
</template>

