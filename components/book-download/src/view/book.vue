<template>
  <div
    class="btn-book"
    :style="btnStyle"
    @click="handleClick"
  >
    {{ buttonText }}
  </div>
</template>

<script setup lang="ts">
import { omit } from 'lodash-es';
import { computed, toRefs } from 'vue';
import { transformStyle } from '@tencent/magic-ui-util';
import { useMokaEditor } from '@tencent/moka-editor-helper';

import { useBookShareStore } from '../store';
import { BookStatus } from '../types';
import type { CommonConfig, PreviewConfig, StyleConfig } from '../types/component';
import { PreviewStatus } from '../types/component';

const props = defineProps<{
  id: string;
  commonConfig: CommonConfig;
  styleConfig: StyleConfig;
  preview: PreviewConfig;
}>();
const { commonConfig, id, styleConfig, preview } = toRefs(props);

const { inEditor } = useMokaEditor();
const bookStore = useBookShareStore(id.value);
const status = computed(() => bookStore.bookStatusByAppID[commonConfig.value?.appID]);
/** 显示中的状态，需要考虑编辑器预览 */
const displayStatus = computed(() => {
  if (!inEditor) return status.value;
  return preview.value.previewStatus === PreviewStatus.Booked ? BookStatus.HasBooked : BookStatus.NotBook;
});
// 合并样式
const bookStyle = computed(() => ({ ...styleConfig.value.commonStyle, ...styleConfig.value.bookStyle }));
const bookedStyle = computed(() => ({ ...styleConfig.value.commonStyle, ...styleConfig.value.bookedStyle }));

const isBooked = computed(() => [BookStatus.HasBooked, BookStatus.MicroClientDownload].includes(status.value));
const isShowBooked = computed(() => [
  BookStatus.HasBooked,
  BookStatus.MicroClientDownload,
].includes(displayStatus.value));

const btnStyle = computed(() => transformStyle(omit(isShowBooked.value ? bookedStyle.value : bookStyle.value, 'buttonText')));
const buttonText = computed(() => (isShowBooked.value ? bookedStyle.value.buttonText : bookStyle.value.buttonText));

function handleClick() {
  if (isBooked.value) return;

  void bookStore.book(commonConfig.value.appID);
}

</script>
