<template>
  <div class="moka-ui-book-download">
    <book
      :id="config.id"
      :common-config="config.common!"
      :preview="config.preview!"
      :style-config="config.styleConfig!"
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps, toRef } from 'vue';

import { useBookShareStore } from '../store';
import { BookDownloadConfig } from '../types/component';
import Book from './book.vue';

const props = defineProps<{
  config: BookDownloadConfig;
}>();
const config = toRef(props, 'config');
const bookStore = useBookShareStore(config.value.id);

void bookStore.loadBookStatus(config.value.common?.appID || 0);

</script>

<style lang="scss" src="./index.scss" />
