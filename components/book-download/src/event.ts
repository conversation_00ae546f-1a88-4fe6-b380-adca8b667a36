
const events = [
  {
    label: '开始下载',
    value: 'startDownload',
  },
  {
    label: '下载成功',
    value: 'downloadSuccess',
  },
  {
    label: '下载失败',
    value: 'downloadFail',
  },
  {
    label: '预约成功',
    value: 'bookSuccess',
  },
  {
    label: '预约成功（包含重复预约）',
    value: 'booSuccessWithRepeat',
  },
];

const methods = [
  {
    label: '拉起下载（未下载时）',
    value: 'pullDownload',
  },
  {
    label: '隐藏背景图',
    value: 'hideBackgroundImage',
  },
  {
    label: '执行预约',
    value: 'book',
  },
  {
    label: '静默预约',
    value: 'silentlyBook',
  },
];

export default {
  events,
  methods,
};
