import type { FormConfig } from '@tmagic/form';
// 正式环境域名为 moka.woa.com, 测试环境为 yyb-act.testsite.woa.com
const apiPrefix = 'https://moka.woa.com/yyb-act/api/app/';
// 获取应用信息接口 url
const getAppInfoUrl = `${apiPrefix}getAppInfo`;
// 获取渠道包列表接口 url
const getChannelListUrl = `${apiPrefix}getChannelList`;

const FORM_CONFIG: FormConfig = [{
  name: 'common',
  items: [{
    text: '按钮类型',
    type: 'radio-group',
    name: 'buttonType',
    defaultValue: 'normal',
    options: [
      { text: '预约下载', value: 'normal' },
      { text: '仅预约', value: 'only-book' },
      { text: '仅下载', value: 'only-download' },
    ],
  }, {
    text: '隐私六要素放置位置',
    type: 'ratio-group',
    name: 'privacyPosition',
    defaultValue: 'top',
    options: [
      { text: '置顶', value: 'top' },
      { text: '置底', value: 'bottom' },
    ],
  }, {
    type: 'fieldset',
    legend: '应用信息',
    items: [{
      text: 'appId',
      type: 'input',
      name: 'appID',
      trim: true,
      rules: [{ required: true, message: '请输入应用的应用宝AppId', trigger: 'blur' }],
      onChange(mForm: any, value: any, data: { model: any }) {
        const { model } = data;
        model.pkgName = 'TODO 包名';
        model.appName = 'TODO 应用名';
      },
    }, {
      text: '包名',
      type: 'display',
      name: 'pkgName',
      defaultValue: '暂无',
    }, {
      text: '应用名',
      type: 'display',
      name: 'appName',
      defaultValue: '暂无',
    }],
  }],
}, {
  type: 'fieldset',
  legend: '下载配置',
  name: 'downloadConfig',
  items: [{
    text: '包类型',
    type: 'radio-group',
    options: [
      { text: '常规包', value: 'normal' },
      { text: '微端包', value: 'micro' },
    ],
  }, {
    text: '应用渠道',
    type: 'select',
    name: 'channelID',
    placeholder: '请选择应用渠道',
    remote: true,
    tooltip: '推广对象如果是游戏广告，这里下拉选项可不选',
    option: {
      url: getChannelListUrl,
      root: 'data',
      method: 'get',
      mode: 'cors',
      headers: { 'Content-Type': 'application/json' },
      json: true,
      text: option => `${option.name}`,
      value: option => `${option.id}`,
      beforeRequest: (_, postOptions, { model }) => {
        if (!model.appId) {
          return postOptions;
        }

        return {
          ...postOptions,
          url: `${postOptions.url}?yybAppId=${model.appId}&`,
        };
      },
    },
    onChange: async (_, channelID, data) => {
      const { model } = data;
      if (channelID) {
        model.longChannelID = channelID;
      }
    },
  }, {
    text: '长渠道号',
    type: 'display',
    name: 'longChannelID',
  }, {
    text: '手写长渠道号',
    type: 'display',
    name: 'inputChannelID',
  }, {
    text: '短渠道号',
    type: 'display',
    name: 'shortChannelID',
    tip: `TODO: 需要校验 ${getAppInfoUrl}`,
  }, {
    text: '跳转 appLink',
    type: 'input',
    name: 'appLink',
  }, {
    text: 'AppStoreId',
    type: 'input',
    name: 'appStoreID',
  }],
}, {
  name: 'preview',
  items: [],
}];

export default FORM_CONFIG;
