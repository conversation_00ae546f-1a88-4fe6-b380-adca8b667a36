import { MokaCSSProperties } from 'shared/types/moka';

export type CommonStyle = Pick<MokaCSSProperties, 'fontSize' | 'color' | 'backgroundColor' | 'backgroundImage' | 'fontFamily'>;

export type ButtonStyle = Pick<MokaCSSProperties, 'backgroundColor' | 'backgroundImage'> & { buttonText: string };

export enum PreviewStatus {
  Book = 'book',
  Booked = 'booked',
  Download = 'download',
  Downloading = 'downloading',
  Continue = 'continue',
  Update = 'update',
  Waiting = 'waiting',
  Install = 'install',
  Installing = 'installing',
  Open = 'open',
  WaitWIFI = 'waitWIFI',
}

/**
 * 预约下载组件配置
 */
export interface BookDownloadConfig {
  id: string;
  /** 公共配置 */
  common?: CommonConfig;
  /** 下载相关配置 */
  downloadConfig?: DownloadConfig;
  /** 预览配置 */
  preview?: PreviewConfig;
  /** 样式配置 */
  styleConfig?: StyleConfig;
  /** 首发时间 */
  releaseDate?: Date;
}

/** 公共配置 */
export interface CommonConfig {
  buttonType: 'normal' | 'only-book' | 'only-download';
  privacyPosition: 'top' | 'bottom';
  appID: number;
  pkgName: string;
  appName: string;
}

/** 下载相关配置 */
export interface DownloadConfig {
  packageType: 'normal' | 'micro';
  channelID?: string;
  longChannelID?: string;
  inputChannelID?: string;
  shortChannelID?: string;
  appLink?: string;
  appStoreID?: string;
  downloadYYBChannelID?: string;
  h5URLInYYB?: string;
  via?: string;
  releaseDate?: Date;
  yybSceneID?: string;
  outSceneID?: string;
  isAutoDownload: boolean;
  isAutoInstall: boolean;
  isAutoOpen: boolean;
  isAutoUpdate: boolean;
  isShowProgress: boolean;
}

/** 预览配置 */
export interface PreviewConfig {
  previewStatus: PreviewStatus;
  previewDownloadProgress: number;
}

/** 样式配置 */
export interface StyleConfig {
  commonStyle: CommonStyle;
  bookStyle: ButtonStyle;
  bookedStyle: ButtonStyle;
}
