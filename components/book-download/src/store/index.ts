import { defineStore } from '@tencent/moka-data-core';

import { type AppInfo, BookStatus, SubscribeStatus } from '../types';

export interface DownloadShareState {
  /** 应用信息 */
  appInfosByID: Record<string, AppInfo>;
  /** 微端信息 */
  microClientDownloadInfosByID: Record<string, AppInfo>;
}

export interface BookShareState {
  /** 公众号关注状态 */
  subscribeStatus: SubscribeStatus;
  /** 预约状态 */
  bookStatusByAppID: Record<string, BookStatus>;
  /** 是否已经到首发时间 */
  isAppReleasedByAppID: Record<string, boolean>;
}

/**
 * 多个下载组件共享的数据
 */
export const useDownloadShareStore = defineStore({
  id: 'download',
  state: (): DownloadShareState => ({
    appInfosByID: {},
    microClientDownloadInfosByID: {},
  }),
  actions: {
    /** 拉取应用信息 */
    async loadAppInfo(): Promise<void> {

    },
    /**
     * 增加微端包下载数据，这个数据一般是从预约状态接口获取到的
     * @param app 应用信息
     */
    async addMicroClientDownloadInfo(app: AppInfo): Promise<void> {
      throw new Error(`Not implemented ${app.appID}`);
    },
  },
}, {
  isolate: false,
});

/**
 * 多个预约组件共享的数据
 */
export const useBookShareStore = defineStore({
  id: 'book',
  state: (): BookShareState => ({
    subscribeStatus: SubscribeStatus.Unknown,
    bookStatusByAppID: {},
    isAppReleasedByAppID: {},
  }),
  actions: {
    /** 拉取公众号关注状态 */
    async loadSubscribeStatus(): Promise<void> {

    },
    /** 拉取预约状态信息 */
    async loadBookStatus(appID: number): Promise<void> {
      this.bookStatusByAppID[appID] = BookStatus.NotBook;
    },
    /** 拉取游戏首发状态 */
    async loadAppReleaseStatus(): Promise<void> {

    },
    /** 关注公众号 */
    async subscribe(): Promise<void> {

    },
    /** 预约游戏 */
    async book(appID: number): Promise<void> {
      this.bookStatusByAppID[appID] = BookStatus.HasBooked;
    },
  },
}, {
  isolate: false,
});
