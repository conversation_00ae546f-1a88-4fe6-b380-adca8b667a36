<template>
  <div
    class="fission-download-register"
  >
    <div
      dt-eid="card"
      :dt-params="utils.getReportParamsStr({
        mod_id: 'one_task_card',
      }, inMagic)"
      :dt-cmd="`hold=${fissionStore.isReportHeld}`"
      class="download-register-btn"
    >
      <register-recharge
        v-if="!showDownloadButton && !inMagic"
        :config="config"
        :is-vip-login="isVipLogin"
        :is-installed="isInstalled"
        :is-y-y-b-channel-p-k-g="isYYBChannelPKG"
      />
      <yyb-download
        v-show="showDownloadButton || inMagic"
        ref="downloadButton"
        class="yyb-download-fission"
        :config="{
          ...(value ?? {}),
          ...config,
          appId: String(config.modConfig.app_id),
          channelId: String(config.modConfig.channel_id),
          text: {
            ...value?.text,
            ready: downloadText,
            update: updateText,
            installed: config.buttonConfig.received,
          }
        }"
        :style="{
          ...customStyle,
          height: '100%',
          width: '100%',
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import YybDownload, { value } from '@tencent/magic-ui-yyb-download';
import { hooks, store, utils } from '@tencent/moka-ui-domain';

import { type ComponentConfig, DownloadState } from '../../types';
import RegisterRecharge from './register-recharge.vue';

const props = defineProps<{
  config: ComponentConfig;
}>();

const downloadButton = ref();
const { useFissionBoostStore } = store;
const fissionStore = useFissionBoostStore();
const { inMagic, isLogin, loginReady } = hooks.useMokaInject();

const customStyle = computed(() => {
  const { position, left, top, ...restStyle } = props.config.originalStyle;
  return restStyle;
});
// 兼容线上环境和测试环境
const downloadState = computed(() => downloadButton.value?.downloadState
  ?? downloadButton.value?._?.refs?.downloadComp?.$data?.downloadState);
const isInstalled = computed(() => downloadState.value === DownloadState.STATE_INSTALLED);
const hasReceived = computed(() => fissionStore.downloadRegisterStatus?.has_get);
// 是否开启回流
const isOpenComeback = computed(() => fissionStore.activityInfo?.activityConfig?.is_open_comeback);
const isComebackCondition = computed(() => fissionStore.comebackInfo?.is_comeback_condition);
// 根据回流条件判断下载文案
const downloadText = computed(() => (isOpenComeback.value && isComebackCondition.value
  ? props.config.buttonConfig.comebackReady
  : props.config.buttonConfig.ready));
const updateText = computed(() => (isOpenComeback.value ? props.config.buttonConfig.update : '更新'));

// 是否三合一登录
const isVipLogin = computed<boolean>(() => {
  if (inMagic) return true;
  const { hasAuthorized, hasActivatedVip } = fissionStore;
  return isLogin.value && hasAuthorized && hasActivatedVip;
});

// 是否判断了本地安装包渠道号
const isJudgedLocalPKGChannel = ref(false);
const showDownloadButton = computed(() => (
  // 未完成校验
  !isJudgedLocalPKGChannel.value
  // 已领取(未三合一仍展示三合一)
  || (hasReceived.value && isVipLogin.value)
  // 未安装且满足三合一
  || (!isInstalled.value && isVipLogin.value)));

const channelIdInfo = ref();
const initActivityData = async () => {
  if (inMagic) return;
  await loginReady;

  const { id, modId, pkgName } = props.config;
  const boostId = utils.getBoostId(String(modId));
  const [channelIdInfoResp] = await Promise.all([
    await utils.getChannelIdInfo(pkgName),
    fissionStore.getActivityInfo(id, Number(modId), boostId),
    fissionStore.getComebackJudgeInfo(id, Number(modId), boostId, pkgName),
  ]);

  channelIdInfo.value = channelIdInfoResp;

  await judgeYYBChannel();

  isJudgedLocalPKGChannel.value = true;
};

// 回流判断在装包的渠道信息
const isYYBChannelPKG = ref(true);
const judgeYYBChannel = async () => {
  const {
    is_open_comeback: isOpenComeBack,
    channel_group: channelGroup,
  } = fissionStore.activityInfo?.activityConfig || {};
  // 非回流或者未拉取到渠道组不判断
  if (!isOpenComeBack || !channelGroup) return;

  // 判断在装包渠道
  channelIdInfo.value = channelIdInfo.value || await utils.getChannelIdInfo(props.config.pkgName) || {};
  updateIsYybChannel(channelGroup);
};

const updateIsYybChannel = (channelGroup: string) => {
  const { channelId, installed } = channelIdInfo.value || {};
  if (!channelId || !installed) return;
  isYYBChannelPKG.value = channelGroup.includes(channelId);
};

watch(() => isInstalled.value, () => {
  if (!isInstalled.value) return;
  void judgeYYBChannel();
});

// 统一在下载注册组件获取裂变活动信息
void initActivityData();
</script>

<style lang="scss" src="./download-register-btn.scss" scoped />
