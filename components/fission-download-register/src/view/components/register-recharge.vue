<template>
  <div
    dt-eid="button"
    :dt-params="utils.getReportParamsStr({
      btn_title: buttonTitle,
      btn_id: 'download_btn'
    }, inMagic)"
    class="receive-button"
    @click="handleReceive"
  >
    {{ buttonTitle }}
  </div>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { BoostEvent, BoostPopupType, constants, GameStage, hooks, OSType, store, utils } from '@tencent/moka-ui-domain';

import { useFissionDownloadRegister } from '../../hooks/use-fission';
import { type ComponentConfig, PartitionType } from '../../types';
import { showTipDialog } from '../../utils';

const props = defineProps<{
  config: ComponentConfig;
  isVipLogin: boolean;
  isInstalled: boolean;
  isYYBChannelPKG: boolean;
}>();

const { toast, inMagic, loginReady, isLogin, $bus } = hooks.useMokaInject();

const { useYybUpdateStore } = store;
const { fissionStore, hasReceived, isVipLoginMet } = useFissionDownloadRegister();
const { UPDATE_YYB_EVENT } = constants;
const yybUpdateStore = useYybUpdateStore();

// 是否是预下载阶段
const gameStage = computed(() => Number(fissionStore.activityInfo?.activityConfig?.game_stage));
const isPreDownload = computed(() => gameStage.value === GameStage.PreDownload);

// 是否在装且非应用宝渠道
const isInstalledAndNotYYBChannel = computed(() => props.isInstalled && !props.isYYBChannelPKG);
// 是否开启回流
const isOpenComeback = computed(() => fissionStore.activityInfo?.activityConfig?.is_open_comeback);
// 是否开启拉新注册
const isOpenRegister = computed(() => fissionStore.activityInfo?.activityConfig?.is_open_register);
const buttonTitle = computed(() => {
  const {
    received,
    canReceiveRegisterReward,
    preConditionsNotSatisfied,
    comebackInstalledPKGNotYYBChannel,
    comebackUnregister,
    comebackNeedActive,
    comebackNotSatisfied,
    canReceiveComebackReward,
    preDownload,
  } = props.config.buttonConfig;

  if (!props.isVipLogin) return preConditionsNotSatisfied;
  if (hasReceived.value) return received;

  //  预下载阶段判断
  if (isPreDownload.value) return preDownload;

  // 回流判断
  if (isOpenComeback.value) {
    // 已安装非应用宝渠道游戏
    if (isInstalledAndNotYYBChannel.value) return comebackInstalledPKGNotYYBChannel;
    const {
      is_comeback_condition: isComebackCondition,
      is_register: isRegister,
      is_comeback_login: isComebackLogin,
    } = fissionStore.comebackInfo || {};
    // 同时开启回流、拉新
    if (isOpenRegister.value) {
      // 满足回流条件
      if (isComebackCondition && isRegister) {
        return isComebackLogin ? canReceiveComebackReward : comebackNeedActive;
      }

      // 未注册领展示回流未注册文案 已注册走拉新的旧逻辑 通过接口拦截
      return !isRegister ? comebackUnregister : canReceiveRegisterReward;
    }

    // 满足回流条件且未活跃
    if (isComebackCondition && !isComebackLogin) return comebackNeedActive;
    // 满足回流且已活跃
    if (isComebackCondition && isComebackLogin) return canReceiveComebackReward;
    // 不满足回流条件
    return comebackNotSatisfied;
  }

  return canReceiveRegisterReward;
});

const setDownloadRegisterStatus = async () => {
  if (inMagic) return;
  await loginReady;
  if (!isLogin.value) return;
  const { id, modId } = props.config;
  void fissionStore.getDownloadRegisterStatus(id, Number(modId));
};

const isRequesting = ref(false);

const handleReceive = async () => {
  if (!(await isVipLoginMet())) {
    return;
  }

  if (hasReceived.value) {
    toast?.('您已领取过奖励~');
    return;
  }

  // 预下载前置拦截
  if (isPreDownload.value) {
    if (!props.config.preDownloadTips) return;

    showTipDialog({
      title: '奖励领取提醒',
      message: props.config.preDownloadTips,
      showCancelButton: false,
    });

    return;
  }

  if (!yybUpdateStore.isVersionValid) {
    $bus?.$emit(UPDATE_YYB_EVENT, { config: props.config });
    return;
  }

  // 仅回流前置拦截
  if (isOpenComeback.value && !isOpenRegister.value) {
    // 在装非应用宝渠道
    if (isInstalledAndNotYYBChannel.value) return;
    // 不满足回流条件
    const { is_comeback_condition: isComebackCondition } = fissionStore.comebackInfo || {};
    if (!isComebackCondition) return;
  }

  if (isRequesting.value) return;

  try {
    isRequesting.value = true;
    const { id, modId, pkgName, modConfig } = props.config;
    const { data: partitionInfo, onBusinessError, onBusinessSuccess } = (await formatPartitionRoleInfo()) || {};

    isRequesting.value = false;
    if (!partitionInfo) return;

    const result = await fissionStore.sendDownloadRegisterPoints(id, Number(modId), {
      boost_id: utils.getBoostId(modId),
      pkg_name: pkgName,
      partition_info: partitionInfo,
    });
    const hasSend = result?.body?.data?.data;
    if (hasSend) {
      onBusinessSuccess?.();
      const {
        is_comeback_condition: isComebackCondition,
      } = fissionStore.comebackInfo || {};
      const showComeBackPoints = isOpenComeback.value && isComebackCondition;
      $bus?.$emit(BoostEvent.OpenBoostPopup, {
        points: showComeBackPoints ? props.config.comebackCanObtainPoints : props.config.canObtainPoints,
        boostPopupType: BoostPopupType.Receive,
      });

      // 积分刷新可能延迟
      setTimeout(() => {
        void fissionStore.getUserDetail(id, Number(modId), String(modConfig.app_id), utils.getBoostId(modId));
      }, 500);
    } else {
      onBusinessError?.();
      showTipDialog({
        title: '领取失败',
        message: result?.tip || '系统繁忙，请稍候再试～',
        showCancelButton: Boolean(props.config.rule),
        cancelButtonJumpUrl: props.config.rule,
      });
    }
  } catch (error) {
    console.error('method: handleReceive', error);
  } finally {
    isRequesting.value = false;
    const { id, modId, pkgName } = props.config;
    void fissionStore.getComebackJudgeInfo(id, Number(modId), utils.getBoostId(modId), pkgName);
  }
};

const { partitionType, getPartitionRoleInfo } = hooks.useBoundPartitionInfo(props.config.pkgName);
async function formatPartitionRoleInfo() {
  if (partitionType.value === PartitionType.NOT_PARTITION) return {
    data: { device_type: OSType.ANDROID },
  };

  const { data: boundPartitionAndRole, onBusinessSuccess, onBusinessError } = await getPartitionRoleInfo() || {};

  // 无区服角色第一次点击 data 也是空 需要额外判断
  if (!boundPartitionAndRole) {
    if (partitionType.value === PartitionType.NOT_PARTITION) return {
      data: { device_type: OSType.ANDROID },
    };
    return;
  }

  const { role, partition, osType } = boundPartitionAndRole;

  return {
    onBusinessError,
    onBusinessSuccess,
    data: {
      role_id: role?.id,
      role_name: role?.name,
      partition_id: partition?.id,
      device_type: osType?.id ?? OSType.ANDROID,
    } };
}

void setDownloadRegisterStatus();
</script>
<style lang="scss" scoped>
.receive-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
</style>
