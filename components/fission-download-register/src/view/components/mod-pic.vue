<template>
  <div
    class="mod-pic"
    :style="{backgroundImage: `url(${pic})`}"
  >
    <div class="loading">
      <van-overlay
        class="overlay"
        :show="loading"
      >
        <van-loading
          color="#fff"
        />
      </van-overlay>
    </div>
  </div>
</template>
<script lang="ts" setup>
import 'vant/es/loading/style';
import 'vant/es/overlay/style';
import { Loading as VanLoading, Overlay as VanOverlay } from 'vant';

defineProps<{
  pic: string;
  loading?: boolean;
}>();
</script>
<style lang="scss" scoped>
.mod-pic {
  position: relative;
  width: 1rem;
  height: 0.8rem;
  background-size: 100% 100%;
  .loading {
    width: 100%;
    height: 100%;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.08rem;
    overflow: hidden;
    .overlay {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
