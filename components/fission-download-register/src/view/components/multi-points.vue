<template>
  <div
    class="multi-points"
    :style="{backgroundImage: `url(${pic})`}"
  >
    <div
      v-if="multiplier > 1"
      class="tag"
    >
      <span>{{ multiplier }}</span>
    </div>
    <div
      v-if="rate"
      class="points"
    >
      <span class="rate">{{ rate }}%</span>积分
    </div>
    <p class="desc">
      充值返
    </p>
  </div>
</template>
<script lang="ts" setup>
defineProps<{
  pic: string;
  rate?: number;
  multiplier: number;
}>();
</script>
<style lang="scss" scoped>
.multi-points {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 1rem;
  height: 0.8rem;
  background-size: 100% 100%;
  color: rgba(138, 89, 54, 1);
  .tag {
    position: absolute;
    top: 0;
    width: 0.49rem;
    height: 0.14rem;
    background-image: url('https://yyb.qpic.cn/moka-imgs/1741851395650-ht19ulv7n3g.png');
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    span {
      position: absolute;
      left: 0.11rem;
      color: rgba(255, 255, 255, 1);
      line-height: 0.14rem;
      font-weight: 700;
    }
  }
  .points {
    position: absolute;
    bottom: 0.35rem;
    font-family: Noto Sans CJK SC;
    font-weight: 500;
    font-size: 0.14rem;
    line-height: 0.1rem;
    letter-spacing: -0.0037rem;
    .rate {
      font-family: YYB;
      font-weight: 400;
      font-size: 0.24rem;
      line-height: 100%;
      letter-spacing: 0;
      margin-right: 0.01rem;
    }
  }
  .desc {
    position: absolute;
    bottom: 0.19rem;
    font-weight: 400;
    font-size: 0.12rem;
    line-height: 0.12rem;
  }
}
</style>
