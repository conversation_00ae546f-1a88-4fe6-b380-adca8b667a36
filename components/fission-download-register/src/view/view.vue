<template>
  <div class="container">
    <div class="mod-pics">
      <!-- 注册领取状态图 -->
      <mod-pic :pic="registerReceiveRewardPic" />
      <!-- 多倍积分 -->
      <multi-points
        :pic="config.modPicConfig?.multiPointBg"
        :rate="lotteryInfo?.pointRate"
        :multiplier="lotteryInfo?.multiplier ?? 0"
      />
      <!-- 抽免单状态图 -->
      <mod-pic
        dt-eid="button"
        :dt-params="utils.getReportParamsStr({
          mod_id: 'raffle_free_order_card',
          mod_title: '抽奖免单卡',
          btn_id: 'raffle_free_order_btn',
          btn_title: '抽免单',
          button_status: String(isLotteryTimeout ? 1 : 0),
        })"
        :dt-cmd="`hold=${fissionStore.isReportHeld && !lotteryInfo}`"
        :pic="lotteryPic"
        :loading="isDoingLottery"
        @click="doLottery"
      />
    </div>
    <!-- 下载注册领取组件 -->
    <download-register-btn :config="config" />
  </div>
</template>
<script lang="ts" setup>
import 'vant/es/toast/style';
import 'vant/es/dialog/style';
import { computed } from 'vue';
import { utils } from '@tencent/moka-ui-domain';

import { useFissionDownloadRegister } from '../hooks/use-fission';
import { useLottery } from '../hooks/use-lottery';
import type { ComponentConfig } from '../types';
import DownloadRegisterBtn from './components/download-register-btn.vue';
import ModPic from './components/mod-pic.vue';
import MultiPoints from './components/multi-points.vue';

const props = defineProps<{
  config: ComponentConfig;
}>();
const { hasReceived, fissionStore } = useFissionDownloadRegister();
const registerReceiveRewardPic = computed(() => {
  const { notReceiveRegisterReward, receivedRegisterReward } = props.config.modPicConfig || {};
  return hasReceived.value ? receivedRegisterReward : notReceiveRegisterReward;
});

const {
  lotteryPic,
  lotteryInfo,
  isLotteryTimeout,
  isDoingLottery,
  doLottery,
  updateLotteryInfo,
} = useLottery(props.config);
void updateLotteryInfo();
</script>
<style lang="scss" scoped>
.container {
  width: 3.43rem;
  height: 1.92rem;
  border-radius: .13rem;
  background-color: #fff;
  padding: 0.12rem 0.12rem 0.4rem;
  .mod-pics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.1174rem;
  }
}
</style>
