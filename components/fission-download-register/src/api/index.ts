import { common, utils } from '@tencent/moka-ui-domain';
import { camelizeKeys } from '@tencent/mole-utils-lib';

import type { GetUserLotteryInfo } from '../types';
/**
 * 获取用户抽奖信息
 * @param componentID 组件 ID
 * @param modID 模块 ID
 * @param pkgName 包名
 * @returns 用户抽奖信息
 */
export async function getUserLotteryInfo(
  componentID: string,
  modID: number,
  pkgName: string,
) {
  const result = await common.exec({
    componentID,
    modID,
    methodName: '/trpc.activity.fission_point.FissionPoint/GetUserLotteryInfo',
    data: {
      boost_id: utils.getBoostId(modID),
      pkg_name: pkgName,
    },
  });
  if (!result?.data) return;

  return camelizeKeys<GetUserLotteryInfo>(result.data);
}
