import { showDialog } from 'vant';
import { utils } from '@tencent/moka-ui-domain';

/**
 * 展示提醒弹窗
 * @param dialogConfig 弹窗配置
 */
export const showTipDialog = (dialogConfig: {
  title: string;
  message: string;
  showCancelButton: boolean;
  cancelButtonJumpUrl?: string;
}) => {
  const { title, message, showCancelButton, cancelButtonJumpUrl } = dialogConfig;
  void showDialog({
    title,
    message,
    showCancelButton,
    cancelButtonText: '活动攻略',
    cancelButtonColor: 'rgba(0, 0, 0, 0.65)',
    confirmButtonText: '确认',
    confirmButtonColor: 'rgba(0, 128, 255, 1)',
  }).catch(() => {
    // 点击取消按钮 按需跳转
    if (cancelButtonJumpUrl) {
      utils.jumpUrl(cancelButtonJumpUrl);
    }
  });
};
