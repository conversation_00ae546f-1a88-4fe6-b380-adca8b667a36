import { computed } from 'vue';
import { store } from '@tencent/moka-ui-domain';

export const useFissionDownloadRegister = () => {
  const { useFissionBoostStore } = store;
  const fissionStore = useFissionBoostStore();

  /** 是否已领取奖励 */
  const hasReceived = computed(() => fissionStore.downloadRegisterStatus?.has_get);

  /** 是否满足会员登录要求 */
  const isVipLoginMet = async () => fissionStore.openVipCommonPopup();
  return {
    fissionStore,
    hasReceived,
    isVipLoginMet,
  };
};
