import { computed, ref } from 'vue';
import { Lottery, ParamCollect } from '@tencent/moka-athena-next-v2';
import {
  BoostEvent,
  BoostPopupType,
  hooks,
  type PropertyResult,
  UserLotteryResultStatus,
} from '@tencent/moka-ui-domain';
import { camelizeKeys, safeJsonStringify } from '@tencent/mole-utils-lib';

import { getUserLotteryInfo } from '../api';
import { COMPONENT_TYPE, type ComponentConfig, type GetUserLotteryInfo } from '../types';
import { showTipDialog } from '../utils';
import { useFissionDownloadRegister } from './use-fission';

export const useLottery = (config: ComponentConfig) => {
  const { $bus, isLogin } = hooks.useMokaInject();
  const lotteryInfo = ref<GetUserLotteryInfo>();
  // 抽奖次数是否已用完
  const isLotteryTimeout = computed(() => {
    if (!isLogin.value) return false;
    if (!lotteryInfo.value) return false;
    const { lotteryMaxTime, lotteryNum } = lotteryInfo.value;
    return lotteryNum >= lotteryMaxTime;
  });
  // 是否无抽奖次数
  const isNoLotteryTime = computed(() => {
    const { remainLotteryNum = 0 } = lotteryInfo.value || {};
    return !isLotteryTimeout.value && remainLotteryNum === 0;
  });
  // 展示的抽奖图片
  const lotteryPic = computed(() => {
    const { canLotteryPic, cannotLotteryPic } = config.modPicConfig || {};
    return isLotteryTimeout.value ? cannotLotteryPic : canLotteryPic;
  });
  // 更新抽奖信息
  const updateLotteryInfo = async () => {
    const { modId, id: componentID, pkgName } = config;
    const data = await getUserLotteryInfo(componentID, modId, pkgName);
    if (!data) return;
    lotteryInfo.value = {
      ...data,
      lotteryMaxTime: Number(data.lotteryMaxTime),
      lotteryNum: Number(data.lotteryNum),
      lotteryThrehold: Number(data.lotteryThrehold),
      rechargeNum: Number(data.rechargeNum),
      remainLotteryNum: Number(data.remainLotteryNum),
    };

    console.log('[useLottery] 获取抽奖信息', data);
  };

  const { isVipLoginMet } = useFissionDownloadRegister();
  /** 抽奖前置校验是否通过 */
  const isPreCheckLotteryMet = () => {
    // 没有抽奖信息 默认失败
    if (!lotteryInfo.value) return false;

    const { rechargeNum, lotteryNum, lotteryMaxTime, lotteryThrehold, remainLotteryNum } = lotteryInfo.value;
    // 有剩余抽奖次数
    if (remainLotteryNum > 0) return true;

    // 已抽完
    if (isLotteryTimeout.value) {
      showTipDialog({
        title: '抽免单额度已用完',
        message: `已充${rechargeNum / 100}元 已抽免单${lotteryNum}次(上限${lotteryMaxTime}次)\n腾讯游戏充值结果最长2天内可查`,
        showCancelButton: Boolean(config.rule),
        cancelButtonJumpUrl: config.rule,
      });

      return false;
    }

    // 没有抽奖次数
    if (isNoLotteryTime.value) {
      showTipDialog({
        title: `再充${((lotteryNum + 1) * lotteryThrehold - rechargeNum) / 100}元可抽免单`,
        message: `已充${rechargeNum / 100}元 已抽免单${lotteryNum}次(上限${lotteryMaxTime}次)\n腾讯游戏充值结果最长2天内可查`,
        showCancelButton: Boolean(config.rule),
        cancelButtonJumpUrl: config.rule,
      });

      return false;
    }

    return false;
  };

  const isDoingLottery = ref(false);
  // 抽奖
  const doLottery = async () => {
    if (!(await isVipLoginMet())) return;
    await updateLotteryInfo();
    if (!isPreCheckLotteryMet()) return;
    if (isDoingLottery.value) return;
    isDoingLottery.value = true;

    const {
      lotteryModID,
      id: componentID,
    } = config;

    let lotteryErrorCallback;
    try {
      const paramCollect = new ParamCollect();

      const [
        params,
        isAuth,
        isCancel,
        successCallbacks,
        errorCallbacks,
      ] = await paramCollect.getQualifierParamByModId(lotteryModID, 'DoLottery');

      lotteryErrorCallback = errorCallbacks;

      if (!isAuth || isCancel) {
        return;
      }

      const qualifierParams = params;
      console.log(`[doLottery] 开始执行裂变抽奖，modID: ${lotteryModID}, qualifierParams: ${safeJsonStringify(qualifierParams)}`);

      const lottery = Lottery.default.create(lotteryModID, componentID, COMPONENT_TYPE);
      const result = await lottery.getExecResult(qualifierParams);
      console.log('[doLottery] 执行裂变抽奖成功，抽奖结果：', result);
      const { originResult } = result;

      successCallbacks?.forEach((fn) => {
        if (typeof fn === 'function') {
          void fn();
        }
      });

      // 抽中物品已达到限量，关闭弹窗并提示
      if (originResult.status !== UserLotteryResultStatus.WON || !originResult.property_results[0]) {
        console.error('[doLottery] 抽奖异常', result);
        showTipDialog({
          title: '抽奖失败',
          showCancelButton: Boolean(config.rule),
          cancelButtonJumpUrl: config.rule,
          message: '礼包发放异常，请稍后再试或查看活动规则联系客服协助。',
        });

        isDoingLottery.value = false;
        return;
      }

      // 获取抽奖的物品
      const property = camelizeKeys<PropertyResult>(originResult.property_results[0]);
      $bus?.$emit(BoostEvent.OpenBoostPopup, {
        points: property.instanceInfo.price * 1000,
        boostPopupType: BoostPopupType.Lottery,
      });
    } catch (e: any) {
      showTipDialog({
        title: '抽奖失败',
        showCancelButton: Boolean(config.rule),
        cancelButtonJumpUrl: config.rule,
        message: e.tip || e.msg || '礼包发放异常，请稍后再试或查看活动规则联系客服协助。',
      });

      console.error('[doLottery] 执行裂变抽奖异常', e);

      // 抽奖出现异常需要执行的回调函数
      lotteryErrorCallback?.forEach((fn) => {
        if (typeof fn === 'function') {
          void fn();
        }
      });
    } finally {
      void updateLotteryInfo();
      isDoingLottery.value = false;
    }
  };

  return {
    isLotteryTimeout,
    lotteryPic,
    lotteryInfo,
    isDoingLottery,
    doLottery,
    updateLotteryInfo,
  };
};
