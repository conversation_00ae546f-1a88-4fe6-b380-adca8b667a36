import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        modId: {
          type: 'string',
          title: '模块选择',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'ModSelect',
          'x-component-props': {
            modType: 21,
          },
        },
        // 有点坑😭实际 app 选择器返回的 appid是 number 不会受此处类型约束
        // 下载组件 props 要求 appid 为 string
        appInfo: {
          type: 'object',
          title: '游戏配置',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'AppSelect',
          'x-component-props': {
            test: 10,
            bindType: 'appInfo',
          },
        },
        modConfig: {
          type: 'object',
          title: '下载模块配置',
          required: true,
          properties: {
            // 业务模块实例 ID
            compId: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'input',
              default: '',
              'x-hidden': true,
            },
            // 业务模块实例类型
            compType: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'input',
              default: 2001,
              'x-hidden': true,
            },
            // 后端模块实例 ID
            modId: {
              type: 'string',
              'x-decorator': 'FormItem',
              'x-component': 'input',
              default: '',
              'x-hidden': true,
            },
            // 后端模块与前端模块对应关系，'one' 表示 一对一，'many' 表示多个前端组件对应一个后端组件
            modRelation: {
              type: 'string',
              default: 'many',
              'x-decorator': 'FormItem',
              'x-component': 'input',
              'x-hidden': true,
            },
            // 放到这里的 app_id 可以透传给后台组件
            app_id: {
              type: 'string',
              required: true,
              default: '',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-hidden': true,
              'x-reactions': {
                dependencies: ['..appInfo'],
                fulfill: {
                  schema: {
                    'x-value': '{{$deps[0].appId}}',
                  },
                },
              },
            },
            // 放到这里的 channel_id 可以透传给后台组件
            channel_id: {
              type: 'string', // 实际也是 number 导致后端模块保存有有问题 默认填裂变的渠道号
              title: '长渠道号',
              required: true,
              default: '0002162647414d455f43454e5445525f4348414e4e454c5f3830303030303031335f3130323131373739',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-validator': [
                { required: true, message: '请输入长渠道号' },
              ],
            },
          },
        },
        sixElementsPlacement: {
          type: 'string',
          title: '下载六要素位置',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          required: true,
          default: 'top',
          enum: [
            { label: '置顶', value: 'top' },
            { label: '置底', value: 'bottom' },
          ],
        },
        // 下载组件要求必传包名
        pkgName: {
          required: true,
          type: 'string',
          title: '包名',
          'x-disabled': true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-reactions': {
            dependencies: ['.appInfo'],
            fulfill: {
              schema: {
                'x-value': '{{$deps[0].pkgName}}',
              },
            },
          },
        },
        canObtainPoints: {
          type: 'string',
          title: '新注册领取积分数量',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          default: 0,
        },
        comebackCanObtainPoints: {
          type: 'string',
          title: '回流领取积分数量',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          default: 0,
        },
        rule: {
          type: 'string',
          title: '积分攻略地址',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          default: '',
        },
        preDownloadTips: {
          type: 'string',
          title: '预下载期间领取奖励提醒文案',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          default: '当前活动下载注册游戏正式上线达标后可领取积分',
        },
        collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '按钮文案配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'buttonConfig.preConditionsNotSatisfied': {
                      type: 'string',
                      title: '不满足登录激活授权判断',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '立即领5888积分',
                    },
                    'buttonConfig.preDownload': {
                      type: 'string',
                      title: '预下载阶段',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '游戏上线后满足条件领5888积分',
                    },
                    'buttonConfig.ready': {
                      type: 'string',
                      title: '新注册_未下载游戏',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '首次下载注册领5888积分',
                    },
                    'buttonConfig.update': {
                      type: 'string',
                      title: '联运回流_本地在装包可更新',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '更新登录后领5888积分',
                    },
                    'buttonConfig.comebackReady': {
                      type: 'string',
                      title: '联运回流_满足回流条件未下载游戏',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '登录游戏后领5888积分',
                    },
                    'buttonConfig.canReceiveRegisterReward': {
                      type: 'string',
                      title: '新注册_用户已完成注册条件',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '立即领5888积分',
                    },
                    'buttonConfig.received': {
                      type: 'string',
                      title: '新注册&回流_已领取奖励',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '打开游戏',
                    },
                    'buttonConfig.comebackInstalledPKGNotYYBChannel': {
                      type: 'string',
                      title: '联运回流_本地在装包非应用宝渠道',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '仅限应用宝渠道下载注册用户参与',
                    },
                    'buttonConfig.comebackUnregister': {
                      type: 'string',
                      title: '联运回流_用户是游戏未注册用户',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '首次注册领58888积分',
                    },
                    'buttonConfig.comebackNeedActive': {
                      type: 'string',
                      title: '联运回流_未完成登录活跃',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '登录游戏后领5888积分',
                    },
                    'buttonConfig.comebackNotSatisfied': {
                      type: 'string',
                      title: '联运回流_注册/活跃时间不满足回流判断',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '仅限回归用户领取',
                    },
                    'buttonConfig.canReceiveComebackReward': {
                      type: 'string',
                      title: '联运回流_用户已完成回流任务',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '立即领5888积分',
                    },
                  },
                },
              },
            },
          },
        },
        lotteryModID: {
          type: 'string',
          title: '抽奖模块 ID（抽免单）',
          'x-decorator': 'FormItem',
          'x-component': 'ModSelect',
          'x-component-props': {
            modType: 11,
          },
        },
        collapse2: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '模块图片配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'modPicConfig.notReceiveRegisterReward': {
                      type: 'string',
                      title: '注册领积分未领取图片',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'modPicConfig.receivedRegisterReward': {
                      type: 'string',
                      title: '注册领积分已领取图片',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'modPicConfig.multiPointBg': {
                      type: 'string',
                      title: '多倍积分背景图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'modPicConfig.canLotteryPic': {
                      type: 'string',
                      title: '可抽奖图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                    'modPicConfig.cannotLotteryPic': {
                      type: 'string',
                      title: '不可抽奖图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
