import { CSSProperties } from 'vue';

/** 下载状态 */
export enum DownloadState {
  /** 下载实例准备就绪 */
  STATE_READY = 1,
  /** 更新 */
  STATE_UPDATE = 2,
  /** 下载队列中 */
  STATE_QUEUING = 3,
  /** 下载中 */
  STATE_DOWNLOADING = 4,
  /** 暂停 */
  STATE_PAUSED = 5,
  /** 下载完成 */
  STATE_DOWNLOADED = 6,
  /** 安装中 */
  STATE_INSTALLING = 7,
  /** 安装完成 */
  STATE_INSTALLED = 8,
}

/** 组件配置 */
export interface ComponentConfig {
  modConfig: {
    app_id: number;
    channel_id: string;
  };
  pkgName: string;
  buttonConfig: {
    /** 不满足前置三合一条件 即激活、授权、登录前置条件拦截 */
    preConditionsNotSatisfied: string;
    /** 预下载阶段提示文案 */
    preDownload: string;
    /** 下载按钮未下载状态 字段名和下载组件保持一致 */
    ready: string;
    /** 下载按钮可更新状态 字段名和下载组件保持一致 */
    update: string;
    /** 满足回流条件未下载 */
    comebackReady: string;
    /** 可领取注册奖励 */
    canReceiveRegisterReward: string;
    /** 已领取 */
    received: string;
    /** 回流本地包非应用宝渠道包 */
    comebackInstalledPKGNotYYBChannel: string;
    /** 回流未注册 */
    comebackUnregister: string;
    /** 回流不满足活跃 */
    comebackNeedActive: string;
    /** 回流不满足回流条件 */
    comebackNotSatisfied: string;
    /** 可领取回流奖励 */
    canReceiveComebackReward: string;
  };
  modId: number;
  id: string;
  originalStyle: CSSProperties;
  ready: string;
  canObtainPoints: number;
  comebackCanObtainPoints: number;
  rule: string;
  /** 预下载阶段提示 */
  preDownloadTips: string;
  /** 抽免单抽奖模块 ID */
  lotteryModID: number;
  /** 模块图片配置 */
  modPicConfig: {
    /** 未领取注册奖励 */
    notReceiveRegisterReward: string;
    /** 已领取注册奖励 */
    receivedRegisterReward: string;
    /** 多倍积分背景图 */
    multiPointBg: string;
    /** 可抽奖：抽奖次数未达上限 */
    canLotteryPic: string;
    /** 不可抽奖：抽奖次数已达上限 */
    cannotLotteryPic: string;
  };
}

/** 游戏区服类型 */
export enum PartitionType {
  /** 不选区服 */
  NOT_PARTITION = 0,
  /** 只选区服 */
  ONLY_PARTITION = 1,
  /** 区服 + 角色 */
  PARTITION_AND_ROLE = 2,
}

export const COMPONENT_TYPE = 'moka-ui-fission-download-register';

/**
 * 抽免单抽奖信息
 */
export interface GetUserLotteryInfo {
  /**
   * 抽奖上限
   */
  lotteryMaxTime: number;
  /**
   * 已充金额
   */
  rechargeNum: number;
  /**
   * 已抽次数
   */
  lotteryNum: number;
  /**
   * 返积分比例
   */
  pointRate: number;
  /**
   * 多倍积分倍数
   */
  multiplier: number;
  /**
   * 抽奖门槛
   */
  lotteryThrehold: number;
  /**
   * 剩余抽奖次数
   */
  remainLotteryNum: number;
}
