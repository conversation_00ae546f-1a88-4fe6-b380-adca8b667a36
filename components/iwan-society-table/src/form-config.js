export default [{
  type: 'tab',
  active: '0',
  items: [{
    title: '属性',
    items: [{
      name: 'template',
      text: '模版类型',
      inputType: 'radioGroup',
      defaultValue: 'white',
      options: [
        {
          value: 'white',
          text: '亮色',
        },
        {
          value: 'black',
          text: '暗色',
        },
      ],
    }, {
      text: 'type',
      name: 'type',
      inputType: 'hidden',
    }, {
      text: '组件名称',
      name: 'name',
      inputType: 'hidden',
    }, {
      type: 'form',
      labelWidth: '100px',
      items: [{
        text: 'type',
        name: 'type',
        inputType: 'hidden',
      }, {
        text: '组件名称',
        name: 'name',
        inputType: 'hidden',
      }, {
        name: 'teamId',
        text: '组队活动ID',
        onChange(vm, value, { model: modelData }) {
          const model = modelData;
          model.report.module.teamId = value;
        },
        append: {
          type: 'button',
          text: '组队配置',
          handler() {
            window.open('http://wuji.oa.com/p/edit?appid=team_config_manage&schemaid=tbl_team_conf');
          },
        },
      }, {
        name: 'ghUrl',
        text: '跳转活动url',
        placeholder: 'https://magic.iwan.qq.com/magic-act/xxxxxx/index.html?ovscroll=0&page=index&actpayid=xxxxx',
        onChange(vm, value, { model: modelData }) {
          const model = modelData;
          model.pmsConfigTName = {
            isPmsField: true,
            pmsName: '公会跳转活动url',
            pmsValue: value,
          };
        },
      }, {
        type: 'radio-group',
        name: 'sortType',
        text: '排序方式',
        options: [
          { text: '升序（由低到高）', value: 'asc' },
          { text: '降序（由高到低）', value: 'desc' },
        ],
      }],
    }],
  }],
}];
