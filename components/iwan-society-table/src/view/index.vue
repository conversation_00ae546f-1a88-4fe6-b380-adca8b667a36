<template>
  <section class="select-container">
    <section class="option-content">
      <template v-for="(item, index) in config.options" :key="index">
        <p :data-id="`${item.value}`"
          class="option-item"
          :class="{ selected: index === config.sItem}"
          @click="handleChange(index)"
        >{{ item.label }}</p>
      </template>
    </section>
  </section>
</template>

<script setup lang="ts">
  import { defineProps, defineEmits } from 'vue';

  import { SocietyTableOptions } from '../types';

  enum EmitEvents {
    Confirm = 'onConfirm',
  }

  const { config } = defineProps<{
    /** 配置参数 */
    config: {
      /** 选项配置 */
      options: SocietyTableOptions[];
      /** 当前渲染序号 */
      sItem: number;
    }
  }>();

  const emit = defineEmits<{
    (e: EmitEvents.Confirm, value: number): void;
  }>();

  const handleChange = (index: number) => {
    emit(EmitEvents.Confirm, index);
  }

</script>

<style lang="scss" src="./index.scss" scoped />
