.select-container {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #FFFFFF;
  border-radius: 0.1rem 0.1rem 0 0;
  z-index: 99999;
  .action-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: solid 1px #E5E5E5;
    padding: 0.1rem 0.2rem;
    .action-btn {
      color: blue
    }
  }
  .option-content {
    .option-item {
      height: 0.4rem;
      display: flex;
      justify-content: center;
      align-items: center;
      &:not(:last-child) {
        border-bottom: solid 1px #E5E5E5
      }
      &.selected {
        font-weight: 600;
      }
    }
  }
}