<template>
  <div
    class="download-button"
    @click="handleDownload"
  >
    下载{{ config.tmast }}
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
// import { download } from '@tencent/moka-ui-domain';

const props = defineProps<{
  config: {
    tmast: string;
  };
}>();

const handleDownload = async () => {
  console.log('下载参数', props.config.tmast);

  // const downloader = await download.downloadEntity.getDownloadTask({
  //   packageName: 'com.tencent.android.qqdownloader',
  // });

  // downloader.setParams({
  //   tmast: props.config.tmast,
  // });

  // return downloader.download();
};
</script>

<style lang="scss" src="./index.scss" />
