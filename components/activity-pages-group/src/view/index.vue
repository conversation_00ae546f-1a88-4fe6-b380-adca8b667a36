<template>
  <div>
    <!-- <pc-content :info="actPageInfo" v-if="isPCStyle" />
     <mobile-content :info="actPageInfo" v-else /> -->
    <mobile-content :info="actPageInfo" background="" />
  </div>
</template>


<script setup lang="ts">
import { ref } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';

import MobileContent from '../components/mobile/mobile-content.vue';
import { ActPageCardInfo } from '../types';

/** 组件配置 */
interface ComponentConfig {

}

const props = defineProps<{
  config: ComponentConfig;
}>();


const isPCStyle = ref(utils.isPCStyle());

const testMock = {
  /** 卡片 ID */
    id: '1',
    /** 应用信息 */
    appInfo: {
      icon: 'https://yyb.qpic.cn/moka-imgs/1752203635775-td04gp2qpnj.png',
      name: '和平精英',
    },
    /** 封面 */
    cover: 'https://yyb.qpic.cn/moka-imgs/1752202924514-fyylnu1ve6d.jpg',
    /** 跳转链接 */
    jumpURL: 'https://mdb.woa.com/',
    /** 描述信息 */
    desc: '限时新皮肤抽免单',
    /** 开始时间 */
    startTime: '7.10',
    /** 结束时间 */
    endTime: '7.20',
}

const actPageInfo = ref<ActPageCardInfo[]>(Array(8).fill(testMock))

</script>

<style lang="scss" src="./index.scss" scoped />
