<template>
  <div class="mobile-container">
    <!-- 网格部分 -->
    <div class="card-list">
      <div v-for="(item, index) in info" class="card-item" :key="index">
        <!-- 卡片上 -->
        <div class="card-image">
          <img :src="item.cover" alt="">
          <div class="card-date">{{ `${item.startTime}-${item.endTime}` }}</div>
        </div>

        <div class="card-info">
          <div class="card-info-left">
            <img :src="item.appInfo.icon" alt="">
          </div>
          <div class="card-info-right">
            <div class="card-game-name">
              {{ item.appInfo.name }}
              <arrow-right />
            </div>
            <div class="card-game-desc">{{ item.desc }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 查看日历 -->
    <div class="calendar-btn">
      查看全部日历活动
      <arrow-right fillColor="#0A618F"/>
    </div>
  </div>
</template>


<script setup lang="ts">
import { ref } from 'vue';

import ArrowRight from './arrow-right.vue'
import { ActPageCardInfo } from '../types';

const props = defineProps<{
  info: ActPageCardInfo[]
}>();

</script>

<style lang="scss" scoped>
.mobile-container {
  width: 3.4rem;
  padding: 0.16rem 0.04rem 0.08rem;
  border-radius: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;

  .card-list {
    width: 100%;
    display: grid;
    grid-template-columns: 50% 50%;
    justify-items: center;
    align-items: center;
    grid-row-gap: 0.08rem;
    margin-bottom: 0.06rem;

    .card-item {
      width: 1.58rem;
      height: 1.22rem;
      background: #fff;
      border-radius: 14px;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .card-image {
        height: 60%;
        position: relative;

        img {
          width: 100%;
          height: 100%;
        }

        .card-date {
          position: absolute;
          color: #fff;
          left: 10px;
          bottom: 2px;
        }
      }

      .card-info {
        height: 40%;
        display: flex;
        align-items: center;
        padding-left: 0.08rem;

        &-left {
          width: 0.38rem;
          height: 0.38rem;
          margin-right: 0.04rem;

          img {
            width: 100%;
            height: 100%;
          }
        }

        &-right {
          .card-game-name {
            font-size: 0.13rem;
            font-weight: 500;

            /* &::after {
              content: ">";
              margin-left: 0.04rem;
            } */
          }

          .card-game-desc {
            font-size: 0.12rem;
            color: #9597AE;
          }
        }
      }
    }
  }

  .calendar-btn {
    padding: 0.04rem 0.04rem;
    color: #0A618F;

    /* &::after {
      content: ">";
      margin-left: 0.04rem;
    } */
  }
}
</style>
