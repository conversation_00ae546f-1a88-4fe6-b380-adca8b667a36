<!-- eslint-disable max-len -->
<template>
  <svg width="4" height="8" viewBox="0 0 4 8" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
      d="M8.17778e-05 7.17151L2.62229 4.0001L0 0.828584L0.686034 0L4 3.99743L0.686034 8L8.17778e-05 7.17151Z"
      :fill="fillColor" />
  </svg>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  fillColor?: string // 是否必传
}>(), {
  fillColor: '#1A1B4B'
});


</script>

<style scoped></style>
