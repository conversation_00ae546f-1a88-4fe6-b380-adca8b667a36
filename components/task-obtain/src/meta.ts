import type { IPublicComponentMetaSchema } from '@tencent/moka-schema';

const env = location.host === 'moka.woa.com' ? 'prod' : 'test';
/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      singleTask: {
        type: 'boolean',
        title: '单任务领取模式',
        default: false,
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-decorator-props': {
          labelWrap: true,
          extra: '切换会改变UI布局，请先确认好该项配置再上传图片',
        },
        'x-reactions': [
          {
            dependencies: ['onlyBatchBtn'],
            when: '{{$deps[0] === true}}',
            fulfill: {
              state: {
                visible: false,
              },
            },
            otherwise: {
              state: {
                visible: true,
              },
            },
          },
        ],
      },
      onlyBatchBtn: {
        type: 'boolean',
        title: '仅展示批量按钮',
        default: false,
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-decorator-props': {
          labelWrap: true,
          extra: '切换会改变UI布局，请先确认好该项配置再上传图片',
        },
        'x-reactions': [
          {
            dependencies: ['singleTask'],
            when: '{{$deps[0] === true}}',
            fulfill: {
              state: {
                visible: false,
              },
            },
            otherwise: {
              state: {
                visible: true,
              },
            },
          },
        ],
      },
      taskGroupId: {
        // TODO: 无疆表的读接口不支持跨域调用，暂使用 TextLink
        type: 'string',
        title: '任务集',
        'x-decorator': 'FormItem',
        'x-component': 'TextLink',
        'x-component-props': {
          placeholder: '请输入任务集ID',
          linkTitle: '任务中心',
          linkStrOrFunc() {
            return `https://wujiang.woa.com/xy/app/${env}/task_center/task_group`;
          },
        },
        'x-validator': [
          { required: true, message: '请输入任务集ID' },
        ],
      },
      taskId: {
        // TODO: 无疆表的读接口不支持跨域调用，暂使用 TextLink
        type: 'string',
        title: '任务',
        'x-decorator': 'FormItem',
        'x-component': 'TextLink',
        'x-component-props': {
          placeholder: '请输入任务ID',
          linkTitle: '任务中心',
          linkStrOrFunc() {
            return `https://wujiang.woa.com/xy/app/${env}/task_center/task_group`;
          },
        },
        'x-validator': [
          { required: true, message: '请输入任务ID' },
        ],
        'x-reactions': [
          {
            dependencies: ['singleTask'],
            when: '{{$deps[0] === true}}',
            fulfill: {
              state: {
                visible: true,
              },
            },
            otherwise: {
              state: {
                visible: false,
              },
            },
          },
        ],
      },
      isSilentObtain: {
        type: 'boolean',
        title: '是否静默领取',
        default: false,
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-decorator-props': {
          labelWrap: true,
          extra: '开启后，领取任务时不会有领取成功的提示',
        },
      },
      isNeedCondition: {
        type: 'boolean',
        title: '是否需要查询条件',
        default: true,
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-decorator-props': {
          labelWrap: true,
          extra: '开启后，后台查询任务集会查询条件',
        },
      },
      isWatchPageVisibleAndLoad: {
        type: 'boolean',
        title: '页面可见是否查询任务集',
        default: true,
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-decorator-props': {
          labelWrap: true,
          extra: '开启后，页面可见时会重新查询任务集',
        },
      },
      voidObtainBatchBtnBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '批量领取按钮配置',
        },
        'x-reactions': [
          {
            dependencies: ['singleTask', 'isSilentObtain'],
            when: '{{!$deps[0] && !$deps[1]}}',
            fulfill: {
              state: {
                visible: true,
              },
            },
            otherwise: {
              state: {
                visible: false,
              },
            },
          },
        ],
        properties: {
          previewBatchStatus: {
            type: 'number',
            title: '预览批量领取状态',
            default: 'canObtainBatchBtnBg',
            enum: [
              {
                label: '可领取',
                value: 'canObtainBatchBtnBg',
              },
              {
                label: '不可领取',
                value: 'notObtainBatchBtnBg',
              },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Radio.Group',
            'x-decorator-props': {
              labelWrap: true,
            },
          },
          obtainBatchBtnWidth: {
            type: 'number',
            title: '批量领取按钮宽度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          obtainBatchBtnHeight: {
            type: 'number',
            title: '批量领取按钮高度',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-display': 'hidden',
          },
          canObtainBatchBtnBg: {
            type: 'string',
            title: '可领取背景图（含可领任务）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
                widthPath: 'obtainBatchBtnWidth',
                heightPath: 'obtainBatchBtnHeight',
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
            'x-reactions': [
              {
                dependencies: ['onlyBatchBtn'],
                when: '{{!$deps[0]}}',
                fulfill: {
                  state: {
                    visible: true,
                  },
                },
                otherwise: {
                  state: {
                    visible: false,
                  },
                },
              },
            ],
          },
          notObtainBatchBtnBg: {
            type: 'string',
            title: '不可领取背景图（不含可领任务）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
                widthPath: 'obtainBatchBtnWidth',
                heightPath: 'obtainBatchBtnHeight',
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
            'x-reactions': [
              {
                dependencies: ['onlyBatchBtn'],
                when: '{{!$deps[0]}}',
                fulfill: {
                  state: {
                    visible: true,
                  },
                },
                otherwise: {
                  state: {
                    visible: false,
                  },
                },
              },
            ],
          },
          canOnlyObtainBatchBtnBg: {
            type: 'string',
            title: '可领取背景图（含可领任务）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
            'x-reactions': [
              {
                dependencies: ['onlyBatchBtn'],
                when: '{{!!$deps[0]}}',
                fulfill: {
                  state: {
                    visible: true,
                  },
                },
                otherwise: {
                  state: {
                    visible: false,
                  },
                },
              },
            ],
          },
          notOnlyObtainBatchBtnBg: {
            type: 'string',
            title: '不可领取背景图（不含可领任务）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
            'x-reactions': [
              {
                dependencies: ['onlyBatchBtn'],
                when: '{{!!$deps[0]}}',
                fulfill: {
                  state: {
                    visible: true,
                  },
                },
                otherwise: {
                  state: {
                    visible: false,
                  },
                },
              },
            ],
          },
        },
      },
      voidObtainBtnBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '领取按钮配置',
        },
        'x-reactions': [
          {
            dependencies: ['singleTask', 'onlyBatchBtn', 'isSilentObtain'],
            when: '{{!$deps[0] && !$deps[1] && !$deps[2]}}',
            fulfill: {
              state: {
                visible: true,
              },
            },
            otherwise: {
              state: {
                visible: false,
              },
            },
          },
        ],
        properties: {
          canObtainBtnBg: {
            type: 'string',
            title: '可领取背景图（已满足条件）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
          notObtainBtnBg: {
            type: 'string',
            title: '不可领取背景图（未满足条件）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
          completeObtainBtnBg: {
            type: 'string',
            title: '已领取背景图（已达个人限量）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
          noneObtainBtnBg: {
            type: 'string',
            title: '已领完背景图（已达全局限量）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
        },
      },
      voidOnlyObtainBtnBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '领取按钮配置',
        },
        'x-reactions': [
          {
            dependencies: ['singleTask', 'isSilentObtain'],
            when: '{{!!$deps[0] && !$deps[1]}}',
            fulfill: {
              state: {
                visible: true,
              },
            },
            otherwise: {
              state: {
                visible: false,
              },
            },
          },
        ],
        properties: {
          previewOnlyObtainStatus: {
            type: 'number',
            title: '预览领取状态',
            default: 'canOnlyObtainBtnBg',
            enum: [
              {
                label: '可领取',
                value: 'canOnlyObtainBtnBg',
              },
              {
                label: '不可领取',
                value: 'notOnlyObtainBtnBg',
              },
              {
                label: '已领取',
                value: 'completeOnlyObtainBtnBg',
              },
              {
                label: '已领完',
                value: 'noneOnlyObtainBtnBg',
              },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Radio.Group',
            'x-decorator-props': {
              labelWrap: true,
            },
          },
          canOnlyObtainBtnBg: {
            type: 'string',
            title: '可领取背景图（已满足条件）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
          notOnlyObtainBtnBg: {
            type: 'string',
            title: '不可领取背景图（未满足条件）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
          completeOnlyObtainBtnBg: {
            type: 'string',
            title: '已领取背景图（已达个人限量）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
          noneOnlyObtainBtnBg: {
            type: 'string',
            title: '已领完背景图（已达全局限量）',
            'x-decorator': 'FormItem',
            'x-component': 'Upload',
            'x-decorator-props': {
              labelWrap: true,
            },
            'x-component-props': {
              drag: true,
              lockImgNorm: {
                width: 0.5,
                height: 0.5,
              },
            },
            'x-validator': [
              { required: true, message: '请上传背景图' },
            ],
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [{
      label: '任务领取完成',
      value: 'act:component:task-obtain:completed',
      desc: '任务领取完成事件',
    }],
    /** 组件内部方法，提供外部调用 */
    methods: [{
      label: '重新查询任务集',
      value: 'getTaskGroup',
      desc: '',
    }],
  },
};

export default meta;
