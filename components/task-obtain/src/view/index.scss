.moka-ui-task-obtain {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .task-list {
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .task-item {
      width: 23.25%;
      margin-right: 2%;
      margin-bottom: 0.05rem;

      &:nth-child(4n) {
        margin-right: 0;
      }

      .info {
        border-radius: 0.05rem;
        background-color: rgba(230, 255, 255, 1);;
        padding: 0.05rem 0.05rem 0;
        margin-bottom: 0.05rem;
      }

      .icon {
        position: relative;
        width: 100%;
        padding-top: 100%;
        border-radius: 0.07rem;

        img {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          font-size: 0;
          width: 100%;
          height: 100%;
        }
      }

      .title {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 0.2rem;

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: rgba(0, 0, 0, 0.85);
          font-size: 0.08rem;
          line-height: 0.1rem;
          width: 100%;
          text-align: center;
        }
      }

      .obtain-btn {
        margin: 0;
        width: 100%;
        height: auto;
        font-size: 0;
      }
    }
  }

  .task-obtain-btn-pc {
    cursor: pointer;
  }
}