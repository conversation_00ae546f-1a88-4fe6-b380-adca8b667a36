<template>
  <div
    v-show="!config.isSilentObtain"
    dt-eid="task-obtain"
    class="moka-ui-task-obtain"
  >
    <div
      v-if="!config.singleTask && !config.onlyBatchBtn"
      class="task-list"
      :style="{ height: `calc(100% - ${`${config.obtainBatchBtnHeight / 100 + 0.1}rem`})` }"
    >
      <div
        v-for="item in curTasks"
        :key="item.id"
        class="task-item"
      >
        <div class="info">
          <div class="icon">
            <img
              :src="item.gifts?.[0]?.icon || ''"
            >
          </div>
          <p class="title">
            <span>{{ item.gifts?.[0]?.title || '' }}</span>
            <span v-if="item.gifts?.length > 1">{{ `等${item.gifts.length}个` }}</span>
          </p>
        </div>
        <img
          :class="{ 'obtain-btn': true, 'task-obtain-btn-pc': isPCStyle }"
          dt-eid="task-obtain-btn"
          :src="getObtainBtnStyle(item).backgroundImage"
          @click="()=>{ doObtain(item) }"
        >
      </div>
    </div>
    <div
      :class="{ 'task-obtain-btn-pc': isPCStyle }"
      v-show="isInit"
      :style="batchBtnStyle"
      dt-eid="task-obtain-batch-btn"
      @click="doObtainBatch"
    />
  </div>
</template>

<script setup lang="ts">
import '@tencent/moka-task-lib/dist/style.css';
import { computed, defineProps, getCurrentInstance, onMounted, onUnmounted, ref, toRefs } from 'vue';
import { Types, useObtainStore } from '@tencent/moka-task-lib';
import { hooks } from '@tencent/moka-ui-domain';
import { throttle, isPC } from '@tencent/mole-utils-lib';

import { COMPONENT_METHODS, MOCK_TASKS, TASK_OBTAIN_COMPLETED } from '../constant/index';
import { ShowBatchStatus } from '../types';
import { type Config, GrantType, TaskInfo, TaskStatus } from '../types/index';

// 是否为 PC 宽屏，pageStyle = 2 则为宽屏
const isPCStyle = isPC(navigator.userAgent) && window.magicUiconfig[0]?.items[0]?.pageStyle === 2;

const props = defineProps<{
  config: Config;
}>();

const { isLogin, inMagic, openLogin, $bus, loginReady } = hooks.useMokaInject();
const { config } = toRefs(props);
const isInit = ref(false);
const instance = getCurrentInstance();

const obtainStore = useObtainStore(config.value.taskGroupId, config.value.isNeedCondition ?? true);

onUnmounted(() => {
  if (config.value.isWatchPageVisibleAndLoad) {
    obtainStore.unWatchPageVisibleAndLoad();
  }
});

onMounted(async () => {
  if (inMagic) {
    isInit.value = true;
    return;
  }

  if (config.value.isWatchPageVisibleAndLoad) {
    obtainStore.watchPageVisibleAndLoad();
  }

  await obtainStore.getTaskGroup();
  isInit.value = true;

  // 如果配置了静默领取模式，直接执行领取
  if (config.value.isSilentObtain) {
    console.log('[task-obtain] 静默领取执行开始');
    await loginReady;
    console.log('[task-obtain] 登录准备完成');
    if (!isLogin.value) {
      return;
    }

    let obtainResult = false;
    if (config.value.singleTask) {
      obtainResult = await obtainStore.doObtain(curTask.value?.id || '', true, isPCStyle);
      console.log('[task-obtain] 静默单个领取执行结束');
    } else {
      obtainResult = await obtainStore.doBatchObtain(true, isPCStyle);
      console.log('[task-obtain] 静默批量领取执行结束');
    }

    // 只有领取成功时才触发事件
    if (obtainResult) {
      $bus?.$emit(TASK_OBTAIN_COMPLETED, instance?.proxy);
      console.log('[task-obtain] 发送领取完成事件');
    }
  }
});

const curTasks = computed(() => (!inMagic ? obtainStore.tasks : MOCK_TASKS));

/** 单任务信息（单任务领取模式） */
const curTask = computed(() => curTasks.value?.find((item: Types.TaskInfo) => item.id === config.value.taskId));

const checkIsTaskLimit = (task: TaskInfo) => {
  // 针对福利礼包和游戏内测礼包需要查询限量
  const limitGift = task?.gifts
    ?.find(gift => [GrantType.Welfare, GrantType.InternalTesting]
      .includes(gift.grantType) && Number(gift.giftUsed) >= Number(gift.giftLimit));
  if (limitGift) return true;
  return false;
};

/** 批量领取按钮状态 */
const batchBtnStyle = computed(() => {
  const {
    singleTask,
    onlyBatchBtn,
    obtainBatchBtnWidth,
    obtainBatchBtnHeight,
    previewBatchStatus,
    previewOnlyObtainStatus,
  } = config.value;

  if (singleTask) {
    const obtainBtnStyle = getObtainBtnStyle(curTask.value);
    // 只有在编辑器 && 单任务模式 才支持预览领取按钮状态
    const backgroundImage = inMagic && singleTask
      ? config.value[String(previewOnlyObtainStatus)] : obtainBtnStyle.backgroundImage;

    return {
      ...obtainBtnStyle,
      width: '100%',
      height: '100%',
      backgroundImage: `url(${backgroundImage})`,
      backgroundSize: 'contain',
      backgroundRepeat: 'no-repeat',
    };
  }

  const canReceiveTask = curTasks.value?.find((item: Types.TaskInfo) => item.status === Types.TaskStatus.CanReceive);

  const prefix = config.value.onlyBatchBtn ? 'Only' : '';
  let backgroundImage = config.value[`can${prefix}ObtainBatchBtnBg`];
  // 已登录且全部不可领 || 编辑器内且预览不可领
  if ((isLogin.value && !canReceiveTask)
  || (inMagic && previewBatchStatus === ShowBatchStatus.NotObtainBatch)) {
    backgroundImage = config.value[`not${prefix}ObtainBatchBtnBg`];
  }

  return {
    width: onlyBatchBtn ? '100%' : `${(obtainBatchBtnWidth as number) / 100}rem`,
    height: onlyBatchBtn ? '100%' : `${(obtainBatchBtnHeight as number) / 100}rem`,
    background: `url(${backgroundImage})`,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
  };
});

/** 领取按钮的背景图状态映射 */
const ObtainBtnBgMapping = computed(() => {
  const prefix = config.value.singleTask ? 'Only' : '';
  return {
    [TaskStatus.NotReachConditon]: config.value[`not${prefix}ObtainBtnBg`],
    [TaskStatus.CanReceive]: config.value[`can${prefix}ObtainBtnBg`],
    [TaskStatus.HasReceived]: config.value[`complete${prefix}ObtainBtnBg`],
    [TaskStatus.ReachDeviceLimit]: config.value[`none${prefix}ObtainBtnBg`],
    [TaskStatus.ReachUserLimit]: config.value[`none${prefix}ObtainBtnBg`],
    [TaskStatus.HitRiskControl]: config.value[`none${prefix}ObtainBtnBg`],
    [TaskStatus.Unknown]: config.value[`none${prefix}ObtainBtnBg`],
  };
});

/**
 * 列表的单任务领取按钮状态
 */
const getObtainBtnStyle = (task: TaskInfo) => {
  let taskStatus = task?.status || TaskStatus.NotReachConditon;
  // 任务为非已领状态则需要查询限量
  if (taskStatus !== TaskStatus.HasReceived) {
    const isTaskLimit = checkIsTaskLimit(task);
    taskStatus = isTaskLimit ? TaskStatus.ReachUserLimit : taskStatus;
  }

  return {
    backgroundImage: ObtainBtnBgMapping.value[taskStatus],
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
  };
};

const doObtain = throttle(async (task: TaskInfo) => {
  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  if (checkIsTaskLimit(task)) return;

  const isSuccess = await obtainStore.doObtain(task.id, config.value.isSilentObtain, isPCStyle);
  if (isSuccess) $bus?.$emit(TASK_OBTAIN_COMPLETED, instance?.proxy);
}, 500);

const doObtainBatch = throttle(async () => {
  if (config.value.singleTask) {
    doObtain(curTask.value);
    return;
  }

  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  const isSuccess = await obtainStore.doBatchObtain(config.value.isSilentObtain, isPCStyle);
  if (isSuccess) $bus?.$emit(TASK_OBTAIN_COMPLETED, instance?.proxy);
}, 500);

defineExpose({
  [COMPONENT_METHODS.GetTaskGroup]: () => obtainStore.getTaskGroup(true),
});
</script>

<style lang="scss" src="./index.scss" scoped />
