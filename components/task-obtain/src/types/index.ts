/** 预览批量录取状态 */
export enum ShowBatchStatus {
  /** 可领取 */
  CanObtainBatch = 'canObtainBatchBtnBg',
  /** 不可领取 */
  NotObtainBatch = 'notObtainBatchBtnBg',
}

/** 预览单任务领取状态 */
export enum ShowStatus {
  /** 可领取 */
  CanOnlyObtainBtnBg = 'canOnlyObtainBtnBg',
  /** 不可领取 */
  NotOnlyObtainBtnBg = 'notOnlyObtainBtnBg',
  /** 已领取 */
  CompleteOnlyObtainBtnBg = 'completeOnlyObtainBtnBg',
  /** 已领完 */
  NoneOnlyObtainBtnBg = 'noneOnlyObtainBtnBg',
}

/** 任务状态 */
export enum TaskStatus {
  /** 未知状态（占位使用）*/
  Unknown = 0,
  /** 未通过任务条件 */
  NotReachConditon = 1,
  /** 可领取状态 */
  CanReceive = 2,
  /** 已完成领取（账号维度）*/
  HasReceived = 3,
  /** 达到设备维度限量 */
  ReachDeviceLimit = 4,
  /** 达到账号维度限量 */
  ReachUserLimit = 5,
  /** 被风控打击 */
  HitRiskControl = 6,
}

/** 任务信息 */
export interface TaskInfo {
  /** 任务 id */
  id: string;
  /** 任务状态 */
  status: TaskStatus;
  /** 礼包集合 */
  gifts: Gift[];
}

/** 任务中心配置类型 */
export enum GrantType {
  /** 应用宝福利 */
  Welfare = 1,
  /** 应用宝积分(抽奖) */
  YYBPoints = 3,
  /** 游戏内测资格 */
  InternalTesting = 7,
}

/** 礼包信息 */
export interface Gift {
  /** 礼包标题 */
  title: string;
  /** 礼包 icon */
  icon: string;
  /** 任务中心配置类型 */
  grantType: GrantType;
  /** 礼包类型 */
  presentType: PresentType;
  /** 礼包限量 */
  giftLimit: string;
  /** 礼包使用 */
  giftUsed: string;
}

/** 礼包类型 */
export enum PresentType {
  /** 无类型 */
  None = 0,
  /** mp */
  MP = 1,
  /** ams 道具 */
  AMS = 2,
  /** cdkey */
  CDKEY = 3,
  /** Q 币 */
  QB = 4,
  /** 宝券 */
  Coupon = 5,
  /** 实物 */
  Physical = 6,
  /** CP 礼包 */
  CP = 7,
  /** QQ 红包 */
  QQWallet = 8,
  /** 微信红包 */
  WXWallet = 9,
}

/** 组件配置信息 */
export interface Config {
  /** 组件id */
  id: string;
  /** 是否单任务领取模式 */
  singleTask: boolean;
  /** 是否仅展示批量按钮 */
  onlyBatchBtn: boolean;

  /** 任务集 id */
  taskGroupId: string;
  /** 任务 id */
  taskId: string;

  /** 预览批量领取状态 */
  previewBatchStatus: ShowBatchStatus;
  /** 预览单任务领取状态 */
  previewOnlyObtainStatus: ShowStatus;

  /** 是否静默领取 */
  isSilentObtain: boolean;

  /** 是否需要查询条件 */
  isNeedCondition: boolean;
  /** 页面可见是否查询任务集 */
  isWatchPageVisibleAndLoad: boolean;
}
