import { ShowBatchStatus, ShowStatus } from './types';

export default {
  type: 'moka-ui-task-obtain',

  style: {
    left: '30',
    width: '320',
    height: '250',
  },

  singleTask: false,
  onlyBatchBtn: false,
  taskGroupId: '',
  taskId: '',
  isSilentObtain: false,
  isNeedCondition: true,
  isWatchPageVisibleAndLoad: true,

  // 批量领取按钮的配置
  previewBatchStatus: ShowBatchStatus.CanObtainBatch,
  obtainBatchBtnWidth: 139,
  obtainBatchBtnHeight: 38,
  canObtainBatchBtnBg: '',
  notObtainBatchBtnBg: '',
  // 仅展示批量领取按钮的配置
  canOnlyObtainBatchBtnBg: '',
  notOnlyObtainBatchBtnBg: '',

  // 领取按钮的配置
  canObtainBtnBg: '',
  notObtainBtnBg: '',
  completeObtainBtnBg: '',
  noneObtainBtnBg: '',
  // 仅展示领取按钮的配置
  previewOnlyObtainStatus: ShowStatus.CanOnlyObtainBtnBg,
  canOnlyObtainBtnBg: '',
  notOnlyObtainBtnBg: '',
  completeOnlyObtainBtnBg: '',
  noneOnlyObtainBtnBg: '',

  dtEid: 'task-obtain',
  renderType: 1,
};
