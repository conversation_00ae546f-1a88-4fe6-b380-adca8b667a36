import { Types } from '@tencent/moka-task-lib';

/** 任务领取完成事件 */
export const TASK_OBTAIN_COMPLETED = 'act:component:task-obtain:completed';

/** 组件内部方法，提供外部调用 */
export const COMPONENT_METHODS = {
  /** 查询任务集 */
  GetTaskGroup: 'getTaskGroup',
};

/** 任务列表的 mock 数据（用于编辑端展示） */
export const MOCK_TASKS = [{
  id: '1',
  status: Types.TaskStatus.NotReachConditon,
  gifts: [{
    giftId: '1',
    title: 'mock未通过条件',
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/7dtpO6l4.webp',
  }],
}, {
  id: '2',
  status: Types.TaskStatus.CanReceive,
  gifts: [{
    giftId: '2',
    title: 'mock可领取',
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/7dtpO6l4.webp',
  }],
}, {
  id: '3',
  status: Types.TaskStatus.HasReceived,
  gifts: [{
    giftId: '3',
    title: 'mock已领取',
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/7dtpO6l4.webp',
  }],
}, {
  id: '4',
  status: Types.TaskStatus.ReachDeviceLimit,
  gifts: [{
    giftId: '4',
    title: 'mock已领完',
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/7dtpO6l4.webp',
  }],
}];
