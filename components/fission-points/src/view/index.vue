<template>
  <div
    class="fission-points"
    dt-eid="button"
    :dt-params="`mod_id=welfare_record_card&mod_title=已领福利卡&btn_title=${description}`"
    :dt-cmd="`hold=${fissionStore.isReportHeld}`"
    @click="openDetailPopup"
  >
    <span>{{ description }}</span>
    <img
      :src="config.iconImage"
      class="icon"
      alt=""
    >
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { BoostEvent, DetailPopupType, hooks, store } from '@tencent/moka-ui-domain';

defineProps<{
  config: {
    iconImage: string;
  };
}>();

const { useFissionBoostStore } = store;
const fissionStore = useFissionBoostStore();

const totalPoints = computed(() => Number(fissionStore.activityInfo?.point_info?.total_points ?? 0));
const description = computed(() => {
  if (totalPoints.value > 0) return `活动已领${totalPoints.value}积分`;
  return '已领福利';
});

const { $bus, isLogin } = hooks.useMokaInject();
const openDetailPopup = async () => {
  if (!await fissionStore.openVipCommonPopup()) {
    return;
  }

  $bus?.$emit(BoostEvent.OpenDetailPopup, DetailPopupType.Points);
};
</script>

<style lang="scss" src="./index.scss" scoped />
