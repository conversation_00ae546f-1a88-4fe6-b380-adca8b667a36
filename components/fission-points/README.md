# MOKA
MOKA 自定义组件开发CLI。

# 文档

文档请移步 TODO

## 环境准备

node.js >= 16

pnpm >= 8

先安装 pnpm

```bash
$ npm install -g pnpm
```

然后安装依赖

```bash
$ pnpm bootstrap
```

## 运行项目

### 调试组件 - 本地调试
```bash
$ cd components/demo
$ pnpm dev
```

### 调试组件 - 在线调试
1.启动moka调试命令
```bash
$ moka dev --c componentName --p pluginName --d
```

|选项|描述|
|---|---|
|--c,--component [name]|指定调试的组件名，模糊搜索|
|--p,--plugin [name]|指定调试的插件名，模糊搜索|
|--d,--domain |是否调试domain|

2.打开MOKA编辑器，启动调试能力，配置调试地址

## 组件发布
```bash
$ moka push --c componentName -SSR
```
|选项|描述|默认值|
|---|---|---|
|--e,--env |发布环境 test/prod | prod |
|--c,--component [name]|指定发布的组件名||
|--p,--plugin [name]|指定发布的插件名||
|--d,--domain |发布domain||
|--iv,--ignoreVerify|忽略本地校验，npm白名单、git信息、git分支|false|
