import { actExecForwardRequester } from '@tencent/moka-data-core';

import { DyeType, GameDimension, GameStatus, UserGameStatusRsp } from '../types';

interface GameSrvInfo {
  svr_id: string;
  role_id?: string;
  role_name?: string;
}

interface GameStatusQuery {
  appid: number;
  pkg_name: string;
  game_srv_info?: GameSrvInfo;
}

/**
 * 获取用户染色状态
 * @param appid 游戏应用ID
 * @param pkgName 游戏包名
 * @param bizKey 业务场景号
 * @param gameSrvInfo 游戏区服信息（可选）
 * @returns 用户染色状态
 */
export async function getUserDyeStatus(
  appid: number,
  pkgName: string,
  bizKey = 'boc_pay',
  gameSrvInfo?: GameSrvInfo,
): Promise<boolean> {
  try {
    // 获取活动 ID
    const { activity_iid: backendId } = (window as any).mappingInfo || {};

    // 构建游戏状态查询参数
    const gameStatusQuery: GameStatusQuery = {
      appid,
      pkg_name: pkgName,
    };

    // 如果有区服角色信息，添加到查询参数中
    if (gameSrvInfo) {
      gameStatusQuery.game_srv_info = gameSrvInfo;
    }

    // 构建请求参数
    const requestData = {
      game_status_query_list: [gameStatusQuery],
      dims: [GameDimension.DYE_STATUS],
      biz_key: bizKey,
    };

    // 发送请求
    const resp = await actExecForwardRequester.request({
      activity_iid: backendId,
      invocation: {
        name: '/trpc.data_server.user_feature.UserFeature/GetGameStatusWithToken',
        data: requestData,
      },
    });

    console.log('[getUserDyeStatus] 获取用户染色状态成功', resp);

    // 处理响应
    if (resp.code !== 0 || !resp.body?.data) {
      console.error(`[getUserDyeStatus] 获取用户染色状态失败，code: ${resp.code}, tip: ${resp.tip}`);
      return false;
    }

    const data = resp.body.data as UserGameStatusRsp;

    // 检查返回状态
    if (data.ret !== 0) {
      console.error(`[getUserDyeStatus] 获取用户染色状态失败，ret: ${data.ret}, msg: ${data.msg}`);
      return false;
    }

    // 查找对应 appid 的游戏状态
    const gameStatus = data.gameStatuses.find((status: GameStatus) => status.appid === String(appid));
    if (!gameStatus?.dyeStatus) {
      console.warn(`[getUserDyeStatus] 未找到 appid ${appid} 的染色状态`);
      return false;
    }

    const isDyed = gameStatus.dyeStatus.dyeType === DyeType.YYB_CHANNEL_DYE;
    console.log(`[getUserDyeStatus] appid ${appid} 的染色状态: ${isDyed ? '已染色' : '未染色'}`);

    return isDyed;
  } catch (error) {
    console.error('[getUserDyeStatus] 获取用户染色状态出错:', error);
    return false;
  }
}
