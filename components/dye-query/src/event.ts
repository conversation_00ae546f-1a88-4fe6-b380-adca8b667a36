import { COMPONENT_EVENT } from './constant';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [
    {
      label: 'Android 应用宝染色',
      value: COMPONENT_EVENT.ANDROID_YYB_DYE,
      desc: '当检测到用户选择 Android 系统已染色时触发',
    },
    {
      label: 'Android 非应用宝染色',
      value: COMPONENT_EVENT.ANDROID_NOT_YYB_DYE,
      desc: '当检测到用户选择 Android 系统未染色时触发',
    },
    {
      label: 'IOS 用户',
      value: COMPONENT_EVENT.IOS_USER,
      desc: '当检测到到用户选择 IOS 系统时触发',
    },
  ],
  /** 组件内部方法，提供外部调用 */
  methods: [],
};
