import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

import { CALLER_METHOD } from './constant';
import events from './event';

/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        appid: {
          type: 'number',
          title: '应用ID',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-decorator-props': {
            labelWrap: true,
          },
          description: '游戏应用ID',
        },
        bizKey: {
          type: 'string',
          title: '业务标识',
          required: true,
          default: 'boc_pay',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入业务标识',
          },
        },
        packageName: {
          type: 'string',
          title: '应用包名',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入应用包名',
          },
        },
        zIndex: {
          type: 'number',
          title: '蒙层z-index值',
          required: true,
          default: 997,
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-decorator-props': {
            labelWrap: true,
          },
          description: '蒙层z-index值',
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: events.events,
    /** 组件内部方法，提供外部调用 */
    methods: [{
      label: '重新获取染色状态',
      value: CALLER_METHOD,
      desc: '重新获取用户的染色状态',
    }],
  },
};

export default meta;
