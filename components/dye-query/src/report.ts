import type { ComponentConfig, IPublicComponentReportSchema } from '@tencent/moka-schema/dist/component-meta-types';
import { BuiltInReportEvent } from '@tencent/mole-report';

export default function getReportConfig(componentConfig: ComponentConfig): IPublicComponentReportSchema {
  return {
    schema: {
      // 自定义事件
      publicRules: [],
      nodes: [{
        description: '染色查询组件曝光',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: `.${componentConfig.id}`,
          data: {
            action: BuiltInReportEvent.Exposure,
            pointName: '染色查询组件',
            appid: (componentConfig as any).appid,
            bizKey: (componentConfig as any).bizKey,
            zIndex: (componentConfig as any).zIndex,
          },
        },
      }],
    },
  };
}
