<template>
  <div>
    <!-- 未染色且为安卓区服时显示蒙层 -->
    <div
      v-if="!isDyed && isAndroidOS"
      class="dye-query-mask"
      @click="triggerEvent(COMPONENT_EVENT.ANDROID_NOT_YYB_DYE)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineExpose, getCurrentInstance, onMounted, ref } from 'vue';
import { hooks, store, utils } from '@tencent/moka-ui-domain';

import { getUserDyeStatus } from '../api';
import { CALLER_METHOD, COMPONENT_EVENT } from '../constant';
import { ComponentConfig } from '../types';
import { OSType } from '../utils/os';

const { usePartitionRoleStore } = store;

const props = defineProps<{
  config: ComponentConfig;
}>();

const { isLogin, $bus, loginReady } = hooks.useMokaInject();
const instance = getCurrentInstance();
const isDyed = ref<boolean>(true);
const isAndroidOS = ref<boolean>(false);

// 计算蒙层的z-index值
const maskZIndex = computed(() => props.config.zIndex || 997);

// 触发事件
const triggerEvent = (eventName: string) => {
  console.log(`[dye-query] 触发事件: ${eventName}`);
  $bus?.$emit(eventName, instance?.proxy);
};

// 处理染色状态逻辑
const handleDyeStatus = (isUserDyed: boolean) => {
  isDyed.value = isUserDyed;

  // 如果是 Android 设备
  if (isUserDyed) {
    // 已染色，触发 android_yyb_dye 事件
    triggerEvent(COMPONENT_EVENT.ANDROID_YYB_DYE);
  } else {
    // 未染色，触发 android_not_yyb_dye 事件
    triggerEvent(COMPONENT_EVENT.ANDROID_NOT_YYB_DYE);
  }
};

// 获取用户染色状态
const fetchUserDyeStatus = async () => {
  try {
    await loginReady;
    // 如果用户未登录，直接返回未染色
    if (!isLogin.value) {
      console.log('[dye-query] 用户未登录，染色状态默认为未染色');
      return;
    }

    // 获取区服角色信息
    const partitionStore = usePartitionRoleStore();
    const openID = utils.getLoginUserOpenID();
    const boundPartitionAndRole = partitionStore.getPartitionRole(props.config.packageName, openID);

    // 如果没有区服角色信息，直接返回未染色
    if (!boundPartitionAndRole?.partition?.id) {
      console.log('[dye-query] 未绑定区服角色，染色状态默认为未染色', boundPartitionAndRole);
      return;
    }

    console.log('[dye-query] 获取用户区服角色信息', boundPartitionAndRole);

    // 判断是否为安卓区服
    isAndroidOS.value = boundPartitionAndRole.osType?.id === OSType.ANDROID || false;
    console.log(`[dye-query] 用户区服类型: ${isAndroidOS.value ? '安卓' : '非安卓'}`);

    if (!isAndroidOS.value) {
      if (boundPartitionAndRole.osType?.id === OSType.IOS) {
        triggerEvent(COMPONENT_EVENT.IOS_USER);
      }

      console.log('[dye-query] 非安卓区服');
      return;
    }

    // 获取用户染色状态
    const { appid, packageName, bizKey } = props.config;
    console.log('[dye-query] 获取用户染色状态', appid, packageName, bizKey);
    const result = await getUserDyeStatus(appid, packageName, bizKey, {
      svr_id: boundPartitionAndRole.partition.id,
      role_id: boundPartitionAndRole.role?.id,
      role_name: boundPartitionAndRole.role?.name,
    });

    // 更新染色状态并触发相应事件
    handleDyeStatus(result);
    console.log(`[dye-query] 获取用户染色状态成功: ${result ? '已染色' : '未染色'}`);
  } catch (error) {
    console.error('[dye-query] 获取用户染色状态失败:', error);
    handleDyeStatus(false);
  }
};

// 组件挂载时获取用户染色状态
onMounted(() => {
  void fetchUserDyeStatus();
});

// 组件内部方法，提供外部调用
defineExpose({ [CALLER_METHOD]: fetchUserDyeStatus });
</script>

<style lang="scss" scoped>
.dye-query {
  &-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: v-bind(maskZIndex);
    background-color: transparent;
  }
}
</style>
