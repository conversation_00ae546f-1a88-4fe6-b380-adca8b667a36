/** 组件配置 */
export interface ComponentConfig {
  /** 应用ID */
  appid: number;
  /** 业务场景号，默认为 boc_pay */
  bizKey: string;
  /** 蒙层z-index值，默认为1100 */
  zIndex?: number;
  /** 应用包名 */
  packageName: string;
}

/** 游戏维度枚举 */
export enum GameDimension {
  /** 游戏染色状态 */
  DYE_STATUS = 0,
  /** 游戏活跃状态 */
  ACTIVE_STATUS = 1,
  /** 游戏粒度pltv值 */
  GAME_PLTV = 2,
  /** 应用宝渠道活跃用户流失概率 */
  YYB_CHANNEL_LOSS_PROB = 3,
  /** 流失用户应用宝渠道回流概率标签 */
  YYB_CHANNEL_REFLOW_PROB = 4,
  /** 付费标签 */
  PAY_STATUS = 5,
  /** 推荐排序标签 */
  RECOM_ORDER = 6,
  /** 用户在yyb侧的行为统计属性 */
  YYB_STAT_STATUS = 7,
}

// DyeType 染色状态
export enum DyeType {
  /** 当前染色状态未知 */
  DYE_UNKNOWN = 0,
  /** 未注册过游戏 */
  NEVER_DYE = 1,
  /** 注册过游戏，当前无染色 */
  NON_DYE = 2,
  /** yyb渠道染色 */
  YYB_CHANNEL_DYE = 3,
  /** 其他渠道染色 */
  OTHER_CHANNEL_DYE = 4,
}

/** 游戏状态查询列表 */
export interface GameStatusQueryList {
  /** 游戏open_appid列表 */
  appids: number[];
  /** 游戏粒度维度列表 */
  dims: GameDimension[];
}

/** 染色状态查询请求 */
export interface YybOpenidGameStatusReq {
  /** 游戏粒度状态请求参数 */
  game_status_qury_list: GameStatusQueryList;
  /** 业务场景id，默认为boc_pay */
  biz_key: string;
}

/** 染色状态 */
export interface DyeStatus {
  /** 历史是否有过yyb染色，true-yyb染色，false-非yyb染色 */
  isYybDye: boolean;
  /** 染色类型 */
  dyeType: DyeType;
}

/** 游戏状态 */
export interface GameStatus {
  /** 游戏open_appid */
  appid: string;
  /** 游戏染色状态 */
  dyeStatus: DyeStatus;
}

/** 用户游戏状态响应 */
export interface UserGameStatusRsp {
  /** 查询状态码；0-查询成功，1-查询失败 */
  ret: number;
  /** 请求失败，返回错误信息 */
  msg: string;
  /** 游戏粒度状态值列表 */
  gameStatuses: GameStatus[];
}
