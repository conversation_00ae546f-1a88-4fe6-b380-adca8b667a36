<template>
  <span 
    v-if="shouldDisplay"
    class="qq-binding-text" 
    :style="textStyle"
    @click="handleClick"
  >
    {{ btnText }}
  </span>
</template>

<script setup lang="ts">
import { defineProps, computed, ref, onMounted, defineExpose } from 'vue';
import { hooks, utils, store } from '@tencent/moka-ui-domain';
import { openPartitionRoleSelect } from '@tencent/yyb-partition-role-select';

import { ComponentConfig, ACT_QQ_BINDING_SCENE_ID } from '../types';
import { CALLER_METHOD } from '../constant';

const props = defineProps<{
  config: ComponentConfig;
}>();

const { isLogin, openLogin, app, inMagic } = hooks.useMokaInject();
const qqBindingStore = store.useQQBindingStore();
let defaultQQ = '';
let loginType = '';
const boundQQ = ref('');
const isBound = ref(false);
const shouldDisplay = ref(false);

// 计算按钮文本
const btnText = computed(() => {
  if (isBound.value && boundQQ.value) {
    return `当前账号：${boundQQ.value}`;
  }
  return props.config.notBindingBtnText || '绑定账号';
});

// 文字样式
const textStyle = computed(() => ({
  fontSize: `${props.config.fontSize || 14}px`,
  color: props.config.textColor || 'rgba(0, 0, 0, 1)',
}));

// 处理点击事件
const handleClick = () => {
  void checkLoginAndProcess();
};

// 更新组件显示状态
const updateDisplayStatus = () => {
  loginType = utils.getLoginType();
  console.log('[qq-binding] 当前登录类型:', loginType);
  // 在编辑器中(inMagic为true)或者微信登录时显示
  shouldDisplay.value = inMagic || loginType === 'wx';
};

/**
 * 打开区服角色选择弹窗
 */
const handleOpenPartitionRoleSelect = async (customDefaultQQ?: string) => {
  try {
    console.log('[qq-binding] 开始打开区服角色选择弹窗...');

    const pkgName = 'com.tencent.tmgp.dnf';
    const qqBindingScene = props.config.qqBindingScene || ACT_QQ_BINDING_SCENE_ID;

    const result = await openPartitionRoleSelect({
      pkgName,
      isShowOSType: false,
      popupTitle: '请确认领奖信息',
      inset: true,
      openVirtualList: false,
      isNeedQQ: true,
      needSaveAfterSuccess: true,
      activateMember: () => {},
      openLogin: () => {
        console.log('[qq-binding] 拉起登录');
        void app?.$openLogin();
      },
      onOk: async (partitionRole, onBusinessSuccess, onBusinessError) => {
        try {
          console.log('[qq-binding] 区服角色选择成功:', partitionRole);
          await onBusinessSuccess?.();
          // 更新绑定状态和QQ号
          await fetchAccountStatus();
          return true;
        } catch (error) {
          console.error('区服角色选择处理失败:', error);
          await onBusinessError?.();
          return false;
        }
      },
      partitionType: 0,
      defaultQQ: customDefaultQQ || defaultQQ,
      canChangeDefaultQQ: props.config.config.canChangeBindingQQ,
      qqBindingScene,
    });

    console.log('[qq-binding] 区服角色选择结果:', result);
  } catch (error) {
    console.error('[qq-binding] 打开区服角色选择弹窗出错:', error);
  }
};

/**
 * 获取账号绑定状态
 */
const fetchAccountStatus = async () => {
  console.log('[qq-binding] 获取账号绑定状态开始');
  try {
    const sceneId = props.config.qqBindingScene || ACT_QQ_BINDING_SCENE_ID;
    const result = await qqBindingStore.fetchAccountInfo(sceneId);
    
    if (result && result.qqNumber) {
      console.log('[qq-binding] 获取账号绑定状态成功:', result);
      boundQQ.value = result.qqNumber;
      defaultQQ = result.qqNumber;
      isBound.value = true;
    } else {
      console.log('[qq-binding] 获取账号绑定状态失败:', result);
      boundQQ.value = '';
      defaultQQ = '';
      isBound.value = false;
    }
  } catch (error) {
    console.error('[qq-binding] 获取账号绑定状态出错:', error);
    boundQQ.value = '';
    isBound.value = false;
  }
};

/**
 * 检查用户登录状态并处理
 */
const checkLoginAndProcess = async () => {
  try {
    // 未登录情况
    if (!isLogin.value) {
      openLogin?.();
      return;
    }

    // 已登录但不是微信登录
    if (loginType !== 'wx') {
      console.log('用户已登录，但不是微信登录，不触发区服角色选择');
      return;
    }

    // 微信登录情况，使用 store 获取账号信息
    console.log('[qq-binding] 用户已微信登录，获取账号信息');
    const sceneId = props.config.qqBindingScene || ACT_QQ_BINDING_SCENE_ID;
    const result = await qqBindingStore.fetchAccountInfo(sceneId);

    if (!result) {
      console.log('[qq-binding] 获取账号信息失败，使用空QQ触发区服角色选择');
      await handleOpenPartitionRoleSelect('');
      return;
    }

    // 设置默认QQ号并触发区服角色选择
    defaultQQ = result.qqNumber;
    await handleOpenPartitionRoleSelect(result.qqNumber);
  } catch (error) {
    console.error('[qq-binding] 检查登录状态并处理出错:', error);
  }
};

// 组件挂载时获取账号绑定状态
onMounted(() => {
  console.log('[qq-binding] 表单配置', props.config);
  console.log('[qq-binding] 是否可以更换绑定QQ', props.config.config.canChangeBindingQQ);
  console.log('[qq-binding] 组件挂载时获取账号绑定状态');
  updateDisplayStatus();
  if (shouldDisplay.value) {
    void fetchAccountStatus();
  }
});

// 组件内部方法，提供外部调用
defineExpose({ [CALLER_METHOD]: checkLoginAndProcess });
</script>
