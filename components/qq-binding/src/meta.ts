import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
import { ACT_QQ_BINDING_SCENE_ID } from './types';
import { CALLER_METHOD } from './constant';
/**
 * 该组件使用魔方协议
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        qqBindingScene: {
          type: 'string',
          title: 'QQ绑定场景',
          required: true,
          default: ACT_QQ_BINDING_SCENE_ID,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入QQ绑定场景',
          },
        },
        canChangeBindingQQ: {
          type: 'boolean',
          title: '允许修改绑定QQ',
          default: true,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
        },
        notBindingBtnText: {
          type: 'string',
          title: '绑定按钮文本',
          default: '绑定账号',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入绑定按钮文本',
          },
        },
        fontSize: {
          type: 'number',
          title: '文字大小',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入文字大小',
          },
          default: 14,
        },
        textColor: {
          type: 'string',
          title: '文字颜色',
          'x-decorator': 'FormItem',
          'x-component': 'ColorPocker',
          'x-decorator-props': {
            labelWrap: true,
          },
          default: 'rgba(0, 0, 0, 1)',
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [{
      label: '绑定QQ账号',
      value: CALLER_METHOD,
      desc: '',
    }],
  },
};

export default meta;
