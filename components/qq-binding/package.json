{"name": "@tencent/moka-ui-qq-binding", "version": "0.0.1", "description": "活动中台组件模板", "main": "./src/index.js", "scripts": {"dev": "vite"}, "author": "kyrieyan<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-qq-binding"], "repository": {"type": "git", "url": "", "directory": "components/qq-binding"}, "dependencies": {"@tencent/moka-data-core": "latest", "@tencent/moka-ui-domain": "moka-latest", "@tencent/mole-report": "^1.0.0", "@tencent/yyb-partition-role-select": "latest", "humps": "^2.0.1", "vue": "^3.3.4"}, "devDependencies": {"husky": "^8.0.3", "sass": "^1.43.4", "turbo": "^1.5.6"}}