import { camelize<PERSON><PERSON>s } from 'humps';
import { actExecRequester } from '@tencent/moka-data-core';
import { utils } from '@tencent/moka-ui-domain';

import { COMPONENT_TYPE, DEFAULT_ERROR_CODE, DEFAULT_ERROR_TEXT, SUCCESS_CODE } from '../constant/index';
import { type GetAllWishResultResp, type GetMyWishListResp } from './type';

/** 服务名 */
const wishServerName = '/trpc.component_plat.lotteryng.WishPool';

/** 接口 */
enum ApiPath {
  GetAllWishResult = 'GetAllWishResult',
  GetMyWishList = 'GetMyWishList',
}

/**
 * 获取往期心愿结果
 * @param data 接口入参
 * @param componentID 组件 id
 * @param modID 模块 ID
 * @returns 往期心愿结果
 */
export async function getAllWishResult(data: {
  wish_activity_id: string;
  wish_pool_time_list: number[];
}, componentID: string, modID: string): Promise<GetAllWishResultResp> {
  console.info('[moka-ui-wish-pool] getAllWishResult API params', data);
  const resp = await actExecRequester.request<typeof data, GetAllWishResultResp>({
    isTest: utils.isTestEnv(),
    componentType: COMPONENT_TYPE,
    componentID,
    componentInfo: {
      modID,
    },
    invocation: {
      name: `${wishServerName}/${ApiPath.GetAllWishResult}`,
      data,
    },
    qualifier_params: [],
  });

  const { code, tip, body } = resp;
  if (code !== SUCCESS_CODE) {
    console.error('[moka-ui-wish-pool] getAllWishResult API error', resp);
    return {
      code: code ?? DEFAULT_ERROR_CODE,
      msg: tip || DEFAULT_ERROR_TEXT,
    };
  }

  const { code: returnCode, msg } = body?.data?.data ?? {};
  if (returnCode !== SUCCESS_CODE) {
    console.error('[moka-ui-wish-pool] getAllWishResult API error', resp);
    return {
      code: returnCode ?? DEFAULT_ERROR_CODE,
      msg: msg || DEFAULT_ERROR_TEXT,
    };
  }

  console.info('[moka-ui-wish-pool] getAllWishResult API success', resp);
  return camelizeKeys(resp.body?.data?.data) as unknown as GetAllWishResultResp;
}

/**
 * 获取用户心愿列表
 * @param data 接口入参
 * @param componentID 组件 id
 * @param modID 模块 ID
 * @returns 往期心愿结果
 */
export async function getMyWishList(data: {
  wish_activity_id: string;
  page_no: number;
  page_num: number;
}, componentID: string, modID: string): Promise<GetMyWishListResp> {
  console.info('[moka-ui-wish-pool] getMyWishList API params', data);
  const resp = await actExecRequester.request<typeof data, GetMyWishListResp>({
    isTest: utils.isTestEnv(),
    componentType: COMPONENT_TYPE,
    componentID,
    componentInfo: {
      modID,
    },
    invocation: {
      name: `${wishServerName}/${ApiPath.GetMyWishList}`,
      data,
    },
    qualifier_params: [],
  });

  const { code, tip, body } = resp;
  if (code !== SUCCESS_CODE) {
    console.error('[moka-ui-wish-pool] getMyWishList API error', resp);
    return {
      code: code ?? DEFAULT_ERROR_CODE,
      msg: tip || DEFAULT_ERROR_TEXT,
    };
  }

  const { code: returnCode, msg } = body?.data?.data ?? {};
  if (returnCode !== SUCCESS_CODE) {
    console.error('[moka-ui-wish-pool] getMyWishList API error', resp);
    return {
      code: returnCode ?? DEFAULT_ERROR_CODE,
      msg: msg || DEFAULT_ERROR_TEXT,
    };
  }

  console.info('[moka-ui-wish-pool] getMyWishList API success', resp);
  return camelizeKeys(resp.body?.data?.data) as unknown as GetMyWishListResp;
}
