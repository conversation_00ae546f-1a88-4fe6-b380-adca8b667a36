import { WishPoolResult, WishRecord } from '../types/my-wish';

/** 获取所有的心愿抽奖结果 */
export interface GetAllWishResultResp {
  /** 状态码 */
  code: number;
  /** 返回消息 */
  msg: string;
  /** 所有的心愿池抽奖结果 */
  wishPoolResultList?: WishPoolResult[];
}

/** 获取用户心愿列表 */
export interface GetMyWishListResp {
  /** 状态码 */
  code: number;
  /** 返回消息 */
  msg: string;
  /** 是否有下一页 */
  hasNext?: boolean;
  /** 许愿记录列表 */
  recordList?: WishRecord[];
}
