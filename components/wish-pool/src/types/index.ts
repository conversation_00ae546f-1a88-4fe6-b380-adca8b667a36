/** 组件配置 */
export interface ComponentConfig {
  id: string;
}

/** 心愿物品 */
export interface WishProperty {
  /** 心愿物品 ID */
  id: string;
  /** 所属心愿池 ID */
  wishPoolID: string;
  /** 物品名称  */
  name: string;
  /** 物品描述 */
  desc: string;
  /** 物品图片 */
  cover: string;
  /** 物品总心愿点数 */
  wishPoints: number;
  /** 拓展信息 */
  ext?: string;
}

/** 我的心愿物品 */
export interface MyWishProperty extends WishProperty {
  /** 概率 */
  probability: string;
  /** 已投入心愿点 */
  currentWishPoints: number;
}

/** 我的心愿信息 */
export interface MyWishInfo {
  /** 我的心愿点 */
  myWishPoints: number;
  /** 本期心愿单 */
  currentWishProperties: MyWishProperty[];
}

/** 游戏信息 */
export interface GameInfo {
  appID: number;
  name: string;
  icon: string;
}

/** 心愿记录 */
export interface MyWishRecord {
  /** 心愿时间戳 */
  time: string;
  /** 心愿物品 */
  property: WishProperty;
  /** 投入心愿点 */
  wishPoints: number;
  /** 心愿结果 */
  wishResult: number;
}

/** 用户信息 */
export interface UserInfo {
  /** 用户名 */
  name: string;
  /** 用户头像 */
  icon: string;
}

/** 心愿记录 */
export interface WishResult {
  /** 心愿时间戳 */
  time: string;
  /** 心愿物品 */
  property: WishProperty;
  /** 中奖用户 */
  rewardUser: UserInfo;
}

/** 心愿池 */
export interface WishPool {
  /** 心愿池 ID */
  id: string;
  /** 时间戳 */
  dateTime: number;
  /** 心愿时间，例如 7.11 */
  time: string;
  /** 心愿池物品 */
  wishProperties: WishProperty[];
  /** 是否可以心愿 */
  canWish: boolean;
}

/** 心愿活动信息 */
export interface WishActInfo {
  /** 心愿活动 ID */
  id: string;
  /** 心愿池 */
  pools: WishPool[];
  /** 当前时间 */
  currentTime: number;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
  /** 每日心愿开始时间 */
  dailyWishStartTime: string;
  /** 每日心愿结束时间 */
  dailyWishEndTime: string;
  /** 每日心愿开奖时间 */
  dailyWishPublishTime: string;
}

/** 组件 Inject 配置 */
export interface ComponentInject {
  showLoadingToast: (text: string) => void;
  showSuccessToast: (text: string) => void;
  showFailToast: (text: string) => void;
}
