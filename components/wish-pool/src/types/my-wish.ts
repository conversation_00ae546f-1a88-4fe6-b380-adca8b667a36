/** 用户信息 */
interface User {
  /** 用户昵称 */
  name: string;
  /** 用户图标 */
  icon: string;
}

/** 许愿商品信息 */
interface WishPropertyInfo {
  /** 许愿商品 id */
  id: string;
  /** 许愿商品名称 */
  name: string;
  /** 许愿商品图标 */
  icon: string;
  /** 许愿商品心愿点 */
  allWishPoint: string;
  /** 许愿商品额外信息 */
  ext: string;
}

/** 单个心愿抽奖结果 */
export interface WishResult {
  /** 许愿商品信息 */
  property: WishPropertyInfo;
  /** 中奖用户信息 */
  user: User;
}

/** 许愿池信息 */
interface WishPoolInfo {
  /** 许愿池 id */
  id: string;
  /** 活动开始时间（秒级时间戳） */
  startTime: number;
  /** 活动结束时间（秒级时间戳） */
  endTime: number;
  /** 许愿商品列表 */
  propertyList: WishPropertyInfo[];
}

/** 心愿池的抽奖结果 */
export interface WishPoolResult {
  /** 许愿池信息 */
  wishPool: WishPoolInfo;
  /** 所有的心愿抽奖结果 */
  wishResultList: WishResult[];
}

export enum WishStatus {
  /** 默认值 */
  Default = 0,
  /** 未开奖 */
  NoResult = 1,
  /** 已经开奖，但是未中奖 */
  Miss = 2,
  /** 已经开奖，中奖了 */
  Winning = 3,
}

/** 许愿记录 */
export interface WishRecord {
  /** 许愿池 id */
  wishPoolId: string;
  /** 许愿商品 id */
  propertyId: string;
  /** 使用的心愿点 */
  wishPoint: number;
  /** 许愿时间（时间戳） */
  wishTime: number;
  /** 许愿状态 */
  wishStatus: WishStatus;
  /** 许愿商品信息 */
  property: WishPropertyInfo;
}
