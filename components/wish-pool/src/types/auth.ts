/** 单个游戏的授权状态 */
export interface GameAuthorize {
  app: SimpleApp;
  scenes: AuthScene[];
}

/** 简单 appInfo */
export interface SimpleApp {
  appid?: string;
  app_name: string;
  app_icon: string;
  /** 如果类型是 open 则取这个值 */
  open_appid?: string;
}

/** 授权场景 */
export interface AuthScene {
  content: AuthContent;
  visual: AuthVisual;
}

/** 授权内容 */
export enum AuthContent {
  /** 游戏角色数据 */
  GameCharacterData = 0,
  /** 游戏行为数据 */
  GameBehaviorData = 1,
  /** 游戏付费数据 */
  GamePaymentData = 2,
}

/** 可见范围 */
export enum AuthVisual {
  /** 所有人可见 */
  VisibleToAll = 0,
  /** 仅自己可见 */
  OnlyVisibleToYourself = 1,
  /** 取消授权 */
  Cancel = 2,
}
