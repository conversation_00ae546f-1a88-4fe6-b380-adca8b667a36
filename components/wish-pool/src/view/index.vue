<template>
  <div class="wish-pool-container">
    <!-- 心愿池头部 -->
    <wish-header />
    <!-- 心愿池部分 -->
    <wish-pool />

    <!-- 本期心愿单 + 往期心愿瓶结果公示 + 心愿注入按钮 + 心愿点 + 底部按钮 -->
    <!-- <my-wish /> -->
    <!-- 游戏列表 -->
    <!-- <game-info-list /> -->
  </div>
</template>

<script setup lang="ts">

import WishHeader from '../components/wish-header.vue';
import WishPool from '../components/wish-pool.vue';

const props = defineProps<{
  config: {
    userName: string;
    age: string;
  };
}>();
</script>

<style lang="scss" src="./index.scss" scoped />
