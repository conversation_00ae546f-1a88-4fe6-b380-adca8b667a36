/**
 * 获取某日中午 12 点的时间戳（本地时间）
 * @param dateStr 日期字符串（yyyy-MM-dd）
 * @returns 中午 12 点的时间戳（毫秒）
 */
export function getNoonTimestamp(dateStr: string) {
  const [year, month, day] = dateStr.split('-').map(Number);
  const noonDate = new Date(year, month - 1, day, 12, 0, 0); // 月份从 0 开始
  return noonDate.getTime();
}

/**
 * 返回日期字符串（yyyy-MM-dd）
 * @param timestamp 时间戳
 * @returns 日期字符串
 */
export function timestampToDate(timestamp: number) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

/**
 * 获取日期区间集合
 * @param startDate 开始时间
 * @param endDate 结束时间
 * @returns 日期区间集合
 */
export function getDateRange(startDate: Date, endDate: Date) {
  const list = [];
  startDate.setHours(0, 0, 0, 0);
  endDate.setHours(0, 0, 0, 0);

  // 暂不考虑跨年的情况，日期展示格式要求 MM 月 dd 日
  while (startDate < endDate) {
    const year = startDate.getFullYear();
    const month = String(startDate.getMonth() + 1).padStart(2, '0');
    const day = String(startDate.getDate()).padStart(2, '0');
    list.push({ dataText: `${month}月${day}日`, timestamp: getNoonTimestamp(`${year}-${month}-${day}`) });
    startDate.setDate(startDate.getDate() + 1); // 加一天
  }

  return list;
}
