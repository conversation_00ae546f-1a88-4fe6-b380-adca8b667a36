<template>
  <wish-property-popup
    :show="isShow"
    :button-text="buttonText"
    :wish-property="wishProperty"
    @close="handleClose"
    @confirm="handleConfirm"
  >
    <div>您的心愿是</div>
    <div>{{ wishProperty.name }}</div>
    <div>将消耗<span style="color: rgba(255, 0, 123, 1)">{{ isUseAllPoints ? wishPoints : 1 }}</span>心愿点</div>
  </wish-property-popup>
</template>

<script setup lang="ts">
import 'vant/es/toast/style';
import { inject } from 'vue';
import { sleep } from '@tencent/mole-utils-lib';

import { MOKA_INJECT_SYMBOL, WISH_INJECTING_SUCCESS, WISH_INJECTING_TEXT } from '../../constants';
import type { ComponentInject, WishProperty } from '../../types';
import WishPropertyPopup from '../wish-property-popup/index.vue';

defineProps<{
  wishProperty: WishProperty;
  isShow: boolean;
  wishPoints: number;
  isUseAllPoints: boolean;
}>();

const emits = defineEmits<{
  (e: 'update:isShow', value: boolean): void;
  (e: 'wishFinished'): void;
}>();

const buttonText = '确认';

const { showLoadingToast, showSuccessToast } = inject(MOKA_INJECT_SYMBOL) as ComponentInject;

/** 处理关闭 */
const handleClose = () => {
  emits('update:isShow', false);
};

let isHandlingConfirm = false;
const handleConfirm = async () => {
  if (isHandlingConfirm) return;
  isHandlingConfirm = true;
  showLoadingToast(WISH_INJECTING_TEXT);

  // TODO: 补充心愿接口调用逻辑
  await sleep(1000);

  // 心愿结束
  emits('wishFinished');
  // 关闭弹窗
  emits('update:isShow', false);
  showSuccessToast(WISH_INJECTING_SUCCESS);
  isHandlingConfirm = false;
};
</script>

<style lang="scss" scoped src="./index.scss"></style>
