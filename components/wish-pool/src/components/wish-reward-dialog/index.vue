<template>
  <wish-property-popup
    :show="isShow"
    :button-text="buttonText"
    :wish-property="wishProperty"
    @close="handleClose"
    @confirm="handleAddressSelect"
  >
    <div class="congratulation">
      恭喜您
    </div>
    <div>填写地址后 15 天内发货</div>
  </wish-property-popup>
</template>

<script setup lang="ts">
import type { MyWishProperty } from '../../types';
import WishPropertyPopup from '../wish-property-popup/index.vue';

defineProps<{
  wishProperty: MyWishProperty;
  isShow: boolean;
}>();

const emits = defineEmits<{
  (e: 'update:isShow', value: boolean): void;
  (e: 'addressSelect'): void;
}>();

const buttonText = '去填写地址';

/** 处理关闭 */
const handleClose = () => {
  emits('update:isShow', false);
};

/** 处理地址选择 */
const handleAddressSelect = () => {
  // TODO: 补充调用 YYB-SDK 进行地址绑定
  emits('update:isShow', false);
  emits('addressSelect');
};

</script>

<style lang="scss" scoped src="./index.scss"></style>
