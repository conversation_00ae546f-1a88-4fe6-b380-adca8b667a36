<template>
  <div>
    <div class="wish-notice">
      活动期间，在应用宝游戏商城每日没消费100元可许愿<span class="header-mark">1</span>次，
      心愿瓶开启许愿时间为每日：<span class="header-mark">00:00-22:00</span>
    </div>
    <div class="wish-time">
      <div>心愿揭晓公布时间：</div>
      <div class="header-mark">23:00</div>
    </div>
  </div>
</template>

<script setup lang="ts">


const props = defineProps<{
}>();
</script>

<style lang="scss" scoped >
.header-mark {
  color: #FF007B;
}
.wish-notice {
  font-size: 0.12rem;
  line-height: 0.16rem;
}

.wish-time {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 0.14rem;
  padding: 0.1rem 0;
}
</style>
