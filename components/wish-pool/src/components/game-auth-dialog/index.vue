<template>
  <van-overlay
    z-index="9999"
    class-name="game-auth-overlay"
  >
    <div class="game-auth-dialog">
      <div class="game-auth-dialog__title">
        开启游戏授权数据
      </div>
      <div class="game-auth-dialog__content">
        {{ gameText }}还未获得游戏数据信息授权。享受游戏内容精准推荐及对应游戏福利，建议你开启游戏数据信息授权。
      </div>
      <div class="game-auth-dialog__operation">
        <div
          class="button"
          @click="emits(EmitEvent.Close)"
        >
          取消
        </div>
        <div
          class="button confirm"
          @click="emits(EmitEvent.Auth)"
        >
          一键授权
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup lang="ts">
import 'vant/es/overlay/style';
import { Overlay as VanOverlay } from 'vant';
import { computed } from 'vue';

import type { GameInfo } from '../../types';

enum EmitEvent {
  Close = 'close',
  Auth = 'auth',
}

const props = defineProps<{
  unAuthAppInfo: GameInfo[];
}>();

const emits = defineEmits<(e: EmitEvent.Close | EmitEvent.Auth) => void>();

/** 游戏文本 */
const gameText = computed(() => {
  const { unAuthAppInfo } = props;

  if (unAuthAppInfo.length === 1) {
    return unAuthAppInfo[0].name;
  }

  // 保持与原来授权弹窗一样的逻辑
  const gameNames = unAuthAppInfo.map(info => info.name).join('、');
  const ellipseLength = 12;
  if (gameNames.length > ellipseLength) {
    return `${gameNames.slice(0, ellipseLength - 1)}...等${unAuthAppInfo.length}款游戏`;
  }

  return `${gameNames}等${unAuthAppInfo.length}款游戏`;
});

</script>

<style lang="scss" scoped src="./index.scss"></style>
