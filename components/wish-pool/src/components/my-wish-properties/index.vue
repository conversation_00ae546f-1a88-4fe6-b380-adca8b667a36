<template>
  <div class="my-wish-properties">
    <!-- 我的心愿单头部 -->
    <div class="my-wish-properties__header">
      <div class="title">
        本期心愿单：
      </div>
      <div
        class="previous-result right-arrow-icon"
        :class="{ 'pc-pointer': isInPC }"
        @click="isShowPreviousWishesDialog = true"
      >
        往期心愿公示
      </div>
    </div>
    <!-- 我的心愿列表 -->
    <div class="my-wish-properties__list">
      <div
        v-for="property in properties"
        :key="property.id"
        class="property-item"
      >
        <div class="property-item__container">
          <img
            class="property-item__img"
            :src="property.cover ? replaceURLProtocol(property.cover) : ''"
          >
          <div class="property-item__name">
            {{ property.name }}
          </div>
          <div class="property-item__probability">
            当前概率{{ property.probability }}
          </div>
        </div>
      </div>
      <div
        v-if="!properties?.length && !isLoading"
        class="empty"
      >
        {{ emptyText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { isPC, replaceURLProtocol } from '@tencent/mole-utils-lib';

import { MY_WISH_PROPERTIES_EMPTY_TEXT, MY_WISH_PROPERTIES_NOT_AUTH_TEXT, MY_WISH_PROPERTIES_NOT_LOGIN_TEXT } from '../../constants';
import type { MyWishProperty } from '../../types';

const props = defineProps<{
  /** 我的心愿物品 */
  properties: MyWishProperty[];
  /** 是否正在加载中 */
  isLoading: boolean;
  /** 是否已授权 */
  isAuth: boolean;
  /** 是否已登录 */
  isLogin: boolean | undefined;
}>();

/** 是否是 PC */
const isInPC = !isPC(navigator.userAgent);

/** 往期心愿弹窗 */
const isShowPreviousWishesDialog = ref(false);

/** 心愿为空提示 */
const emptyText = computed(() => {
  const { isLogin, isAuth } = props;
  if (!isLogin) return MY_WISH_PROPERTIES_NOT_LOGIN_TEXT;
  if (!isAuth) return MY_WISH_PROPERTIES_NOT_AUTH_TEXT;
  return MY_WISH_PROPERTIES_EMPTY_TEXT;
});
</script>

<style lang="scss" scoped src="./index.scss"></style>
