<template>
  <van-dialog
    v-model:show="showDialog"
    :show-confirm-button="false"
    :show-cancel-button="false"
    class="wish-history-dialog"
  >
    <div class="dialog-header">
      <div class="dialog-header__title" />
      <close-svg
        class="dialog-header__close"
        @click="showDialog = false"
      />
    </div>
    <div class="dialog-content">
      <!-- 日期切换 -->
      <div class="date-header">
        <left-svg
          :style="{ visibility: currentDateIndex === 0 ? 'hidden' : 'visible' }"
          @click="changeDate(-1)"
        />
        <span class="date-header__current">{{ currentDate?.dataText }}</span>
        <right-svg
          :style="{ visibility: currentDateIndex === dateRange.length - 1 ? 'hidden' : 'visible' }"
          @click="changeDate(1)"
        />
      </div>

      <!-- 列表标题 -->
      <div class="list-header">
        <div class="list-header__column">
          奖品
        </div>
        <div class="list-header__column user">
          中奖用户
        </div>
      </div>

      <!-- 滚动列表 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
      >
        <div
          v-for="(item, index) in wishResults"
          :key="index"
          class="prize-item"
        >
          <div class="prize-item__column">
            <van-image
              width="0.35rem"
              height="0.46rem"
              :src="item.property?.icon"
            />
          </div>
          <div class="prize-item__column user">
            <van-image
              round
              width="0.26rem"
              height="0.26rem"
              :src="item.user.icon"
            />
            <span class="name">{{ item.user.name }}</span>
          </div>
        </div>
      </van-list>
    </div>
  </van-dialog>
</template>

<script setup lang="ts">
import 'vant/es/dialog/style';
import 'vant/es/image/style';
import 'vant/es/list/style';
import { Dialog as VanDialog, Image as VanImage, List as VanList } from 'vant';
import { computed, ref, toRefs, watch } from 'vue';
import { parseInt, showToast } from '@tencent/mole-utils-lib/client';

import { getAllWishResult } from '../../api/my-wish';
import { SUCCESS_CODE } from '../../constant/index';
import { type WishResult } from '../../types/my-wish';
import { getDateRange } from '../../utils/index';
import CloseSvg from './close-svg.vue';
import LeftSvg from './left-svg.vue';
import RightSvg from './right-svg.vue';

const props = defineProps<{
  /** 是否展示弹窗 */
  show: boolean;
  /** 活动 id */
  activityId: string;
  /** 组件 id */
  componentId: string;
  /** 许愿模块 id */
  modId: string;
  /** 活动开始时间戳 */
  startTime: number;
  /** 当前时间戳（服务器时间） */
  currentTime: number;
}>();
const { show, activityId, componentId, modId, startTime, currentTime } = toRefs(props);

const emit = defineEmits(['update:show']);

const showDialog = computed({
  get: () => show.value,
  set: value => emit('update:show', value),
});

const currentDateIndex = ref(0);
const dateRange = ref<Array<{ dataText: string; timestamp: number }>>([]);
watch(() => show.value, () => {
  if (!show.value || !startTime.value || !currentTime.value) return;

  const startDate = new Date(parseInt(startTime.value, 10));
  const endDate = new Date(parseInt(currentTime.value, 10));
  dateRange.value = getDateRange(startDate, endDate);

  currentDateIndex.value = dateRange.value.length - 1;
  void loadWishResult();
});

const currentDate = computed(() => dateRange.value?.[currentDateIndex.value]);
const wishResults = ref<WishResult[]>([]);
const loading = ref(false);
const finished = ref(true);

const changeDate = (direction: number) => {
  const newDateIndex = currentDateIndex.value + direction;
  if (newDateIndex > dateRange.value.length - 1
   || currentDateIndex.value + direction < 0
  ) {
    // 索引不在往期日期集合的范围内
    return;
  }

  currentDateIndex.value = newDateIndex;
  void loadWishResult();
};

/**
 * 往期心愿数据不会变，缓存一层
 */
const cacheWishResults: Record<string, WishResult[]> = {};

/**
 * 加载往期心愿数据
 */
const loadWishResult = async () => {
  const currentTimestamp = dateRange.value[currentDateIndex.value]?.timestamp;
  if (!currentTimestamp) return;

  if (cacheWishResults[currentTimestamp]) {
    wishResults.value = cacheWishResults[currentTimestamp];
    return;
  }

  loading.value = true;
  finished.value = false;
  wishResults.value = [];
  const resp = await getAllWishResult({
    wish_activity_id: activityId.value,
    wish_pool_time_list: [currentTimestamp],
  }, componentId.value, modId.value);

  loading.value = false;
  finished.value = true;
  const { code, msg, wishPoolResultList } = resp;
  if (code !== SUCCESS_CODE) {
    showToast(msg);
    return;
  }

  cacheWishResults[currentTimestamp] = wishPoolResultList?.[0]?.wishResultList || [];
  wishResults.value = cacheWishResults[currentTimestamp];
};
</script>
<style lang="scss">
.wish-history-dialog {
  width: 2.82rem;
  height: 4rem;
  overflow: visible;
  background-color: transparent;
  top: 50%;

  .van-dialog__content {
    overflow: hidden;
    border-radius: 0.16rem;
  }

  .van-loading__text, .van-list__finished-text {
    font-size: 0.13rem;
  }
}
</style>
<style lang="scss" scoped>
.dialog-header {
  position: absolute;
  width: 2.82rem;
  height: 1rem;
  top: -0.6rem;
  z-index: -1;

  &__title {
    margin: 0 auto;
    width: 2.6rem;
    height: 0.6rem;
    background-size: cover;
    background-repeat: no-repeat;
    background-image: url('https://cdn.yyb.gtimg.com/wupload/xy/yybtech/7UDb64WW.png');
  }

  &__close {
    position: absolute;
    right: 0;
    top: -0.2rem;
  }
}

.dialog-content {
  overflow: hidden;
  padding: 0.16rem;
  height: 100%;
  background: linear-gradient(0deg, #F2FEFF 85.75%, #C3F8FE 101.25%);
}

.date-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.16rem;

  &__current {
    margin: 0 0.15rem;
    font-weight: 700;
    font-size: 0.15rem;
    text-align: center;
    color: rgba(32, 53, 100, 1);
  }
}

.list-header {
  display: flex;
  padding-bottom: 0.1rem;

  &__column, &__column {
    width: 35%;
    font-weight: 500;
    font-size: 0.13rem;
    text-align: center;
    color: rgba(32, 53, 100, 1);

    &.user {
      width: 65%;
    }
  }
}

.van-list {
  overflow-y: auto;
  height: 3.03rem;
}

.prize-item {
  display: flex;
  height: 0.75rem;
  border-top: 0.008rem solid rgba(44, 44, 44, 0.06);

  &__column {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35%;

    &.user {
      width: 65%;
      font-weight: 500;
      font-size: 0.0934rem;
      color: rgba(96, 109, 139, 1);

      .name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-left: 0.05rem;
        width: 0.8rem;
      }
    }
  }
}
</style>
