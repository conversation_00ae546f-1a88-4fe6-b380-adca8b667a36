<template>
  <van-dialog
    v-model:show="showDialog"
    :show-confirm-button="false"
    :show-cancel-button="false"
    class-name="my-wish-dialog"
  >
    <div class="dialog-header">
      <div class="dialog-header__title" />
      <close-svg
        class="dialog-header__close"
        @click="showDialog = false"
      />
    </div>
    <div class="dialog-content">
      <!-- 列表标题 -->
      <div class="list-header">
        <div
          v-for="(item, index) in ['时间', '心愿', '心愿点', '心愿结果']"
          :key="index"
          class="list-header__column"
        >
          {{ item }}
        </div>
      </div>

      <!-- 滚动列表 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div
          v-for="(item, index) in wishRecords"
          :key="index"
          class="wish-item"
        >
          <div class="wish-item__column">
            {{ timestampToDate(item.wishTime) }}
          </div>
          <div class="wish-item__column">
            <van-image
              width="0.32rem"
              height="0.42rem"
              :src="item.property?.icon"
            />
          </div>
          <div class="wish-item__column">
            {{ item.wishPoint }}
          </div>
          <div
            class="wish-item__column"
            :class="{
              'miss': item.wishStatus === WishStatus.Miss,
              'win': item.wishStatus === WishStatus.Winning
            }"
          >
            {{ wishResultText(item.wishStatus) }}
          </div>
        </div>
      </van-list>
    </div>
  </van-dialog>
</template>

<script setup lang="ts">
import 'vant/es/dialog/style';
import 'vant/es/image/style';
import 'vant/es/list/style';
import { Dialog as VanDialog, Image as VanImage, List as VanList } from 'vant';
import { computed, ref, toRefs, watch } from 'vue';
import { showToast } from '@tencent/mole-utils-lib/client';

import { getMyWishList } from '../../api/my-wish';
import { SUCCESS_CODE } from '../../constant/index';
import { type WishRecord, WishStatus } from '../../types/my-wish';
import { timestampToDate } from '../../utils/index';
import CloseSvg from './close-svg.vue';

const props = defineProps<{
  /** 是否展示弹窗 */
  show: boolean;
  /** 活动 id */
  activityId: string;
  /** 组件 id */
  componentId: string;
  /** 许愿模块 id */
  modId: string;
}>();
const { show, activityId, componentId, modId } = toRefs(props);

const emit = defineEmits(['update:show']);

const showDialog = computed({
  get: () => show.value,
  set: value => emit('update:show', value),
});
watch(() => show.value, () => {
  if (!show.value) return;

  // 我的心愿单，打开需要刷新数据
  currentPageNo.value = 0;
  wishRecords.value = [];
});

const wishRecords = ref<WishRecord[]>([]);
const loading = ref(false);
const finished = ref(false);
const currentPageNo = ref(0);

const loadMyWishRecord = async () => {
  loading.value = true;
  finished.value = false;
  const resp = await getMyWishList({
    wish_activity_id: activityId.value,
    page_no: currentPageNo.value,
    page_num: 10,
  }, componentId.value, modId.value);

  loading.value = false;
  const { code, msg, hasNext, recordList } = resp;
  if (code !== SUCCESS_CODE) {
    showToast(msg);
    return;
  }

  finished.value = !hasNext;
  wishRecords.value.push(...(recordList || []));
  currentPageNo.value = currentPageNo.value + 1;
};

const wishResultText = (wishResult: number) => {
  const textMap: Record<number, string> = {
    [WishStatus.NoResult]: '待开奖',
    [WishStatus.Miss]: '未中奖',
    [WishStatus.Winning]: '中奖',
  };
  return textMap[wishResult];
};

const onLoad = () => {
  void loadMyWishRecord();
};
</script>
<style lang="scss">
.my-wish-dialog {
  width: 2.82rem;
  height: 4rem;
  overflow: visible;
  background-color: transparent;
  top: 50%;

  .van-dialog__content {
    overflow: hidden;
    border-radius: 0.16rem;
  }

  .van-loading__text, .van-list__finished-text {
    font-size: 0.13rem;
  }
}
</style>
<style lang="scss" scoped>
.dialog-header {
  position: absolute;
  width: 2.82rem;
  height: 1rem;
  top: -0.6rem;
  z-index: -1;

  &__title {
    margin: 0 auto;
    width: 2.6rem;
    height: 0.6rem;
    background-size: cover;
    background-repeat: no-repeat;
    background-image: url('https://cdn.yyb.gtimg.com/wupload/xy/yybtech/P0TawZBT.png');
  }

  &__close {
    position: absolute;
    right: 0;
    top: -0.2rem;
  }
}

.dialog-content {
  overflow: hidden;
  padding: 0.16rem;
  padding-bottom: 0;
  height: 100%;
  background: linear-gradient(0deg, #F2FEFF 85.75%, #C3F8FE 101.25%);
}

.list-header {
  display: flex;
  padding-bottom: 0.1rem;

  &__column {
    width: 25%;
    font-weight: 500;
    font-size: 0.13rem;
    text-align: center;
    color: rgba(32, 53, 100, 1);
  }
}

.van-list {
  overflow-y: auto;
  height: 3.5rem;
}

.wish-item {
  display: flex;
  height: 0.65rem;
  border-top: 0.008rem solid rgba(44, 44, 44, 0.06);

  &__column {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 25%;
    font-weight: 500;
    font-size: 0.1rem;
    text-align: center;
    color: rgba(96, 109, 139, 0.65);

    &.win {
      color: rgba(255, 0, 123, 1);
      font-weight: 700;
      font-size: 0.12rem;
    }

    &.miss {
      color: rgba(96, 109, 139, 1);
    }
  }
}
</style>
