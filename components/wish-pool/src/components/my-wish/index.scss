
@import url('../../assets/scss/index.scss');

.my-wish {
  width: 100%;
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Noto Sans CJK SC;
    letter-spacing: 0px;
    vertical-align: middle;
    margin-bottom: 0.11rem;

   .title {
      font-family: Noto Sans CJK SC;
      font-weight: 700;
      font-size: 14px;
      color: rgba(36, 38, 107, 1);
    }

    .previous-result {
      font-weight: 400;
      font-size: 12px;
      color: rgba(10, 97, 143, 1);
    }
  }

  &__list {
    margin-bottom: 0.12rem;
  }

  &__operation {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.08rem;
    .wish_button {
      width: 1.47rem;
      height: 0.41rem;
      line-height: 0.41rem;
      text-align: center;
      border-radius: 0.205rem;
      margin-right: 0.08rem;
      font-family: MFFangHei;
      font-weight: 700;      
      font-size: 0.14rem;
      letter-spacing: 0px;
      vertical-align: middle;
    }

    .one {
      background: linear-gradient(180deg, #FFEC89 0%, #FFD417 100%);
      color: rgba(15, 15, 15, 0.95);
    }

    .all {
      background: linear-gradient(180deg, #35C6FF 0%, #0483FB 100%);
      color: rgba(255, 255, 255, 0.95);
    }

    .wish_button:last-of-type {
      margin-right: 0;
    }
  }

  &__info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: Noto Sans CJK SC;
    font-style: Medium;
    letter-spacing: 0px;
    vertical-align: middle;
    .points {
      font-weight: 500;
      font-size: 0.13rem;
      color: rgba(26, 27, 75, 1);
    }
    .point-text {
      font-weight: 700;
      color: rgba(255, 0, 123, 1);
    }
    .default-text {
      display: inline-flex !important;
      font-weight: 400;
      font-size: 0.12rem;
      color: rgba(10, 97, 143, 1);
    }
    .records {
      font-weight: 400;
      font-size: 0.12rem;
      color: rgba(55, 106, 134, 1);
    }
  }
}
