<template>
  <div class="my-wish">
    <!-- 我的心愿单头部 -->
    <div class="my-wish__header">
      <div class="title">
        本期心愿单：
      </div>
      <div
        class="previous-result right-arrow-icon"
        @click="isShowPreviousWishesDialog = true"
      >
        往期心愿公示
      </div>
    </div>
    <!-- 我的心愿列表 -->
    <div class="my-wish__list">
      <my-wish-properties
        :properties="wishProperties"
        :is-loading="isLoading"
        :is-login="isLogin"
        :is-auth="isAuth"
      />
    </div>
    <!-- 心愿按钮 -->
    <div class="my-wish__operation">
      <div class="wish_button one">
        注入1点心愿点
      </div>
      <div class="wish_button all">
        注入全部心愿点
      </div>
    </div>
    <!-- 我的心愿信息区域 -->
    <div class="my-wish__info">
      <div
        class="points"
        @click.stop="handlePointTextClick"
      >
        我的心愿点:
        <span :class="[isLogin && isAuth ? 'point-text' : 'default-text right-arrow-icon']">{{ wishPointsText }}</span>
      </div>
      <div
        class="records right-arrow-icon"
        @click="isShowMyWishesDialog = true"
      >
        我的心愿单
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';

import { WISH_POINTS_NOT_AUTH_TEXT, WISH_POINTS_NOT_LOGIN_TEXT } from '../../constants';
import { useMyWish } from '../../hooks/use-my-wish';
import MyWishProperties from '../my-wish-properties/index.vue';

const props = defineProps<{
  wishID: string;
  isLogin: boolean;
  isAuth: boolean;
}>();

const emits = defineEmits<(e: 'login' | 'auth') => void>();

const {
  isLoading,
  wishPoints,
  wishProperties,
  isShowMyWishesDialog,
  isShowPreviousWishesDialog,
  getMyWishInfo,
} = useMyWish();

const wishPointsText = computed(() => {
  const { isLogin, isAuth } = props;
  // 未登录
  if (!isLogin) return WISH_POINTS_NOT_LOGIN_TEXT;
  // 未授权
  if (!isAuth) return WISH_POINTS_NOT_AUTH_TEXT;
  // 已登录且已授权展示心愿点
  return wishPoints.value;
});

watch(() => [props.isLogin, props.isAuth], () => {
  // 未登录或者未授权不获取
  if (!props.isLogin || !props.isAuth) return;
  void getMyWishInfo(props.wishID);
});

const handlePointTextClick = () => {
  const { isLogin, isAuth } = props;
  // 未登录
  if (!isLogin) emits('login');
  // 未授权
  if (!isAuth) emits('auth');
  // 已登录已授权
  return;
};

</script>

<style lang="scss" scoped src="./index.scss"></style>
