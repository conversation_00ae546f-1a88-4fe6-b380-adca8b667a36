.wish-property-pop {
  background: linear-gradient(0deg, #F2FEFF 87.15%, #C3F8FE 101.13%);
  position: relative;
  border-radius: 0.16rem;
  width: 2.82rem;
  &__container {
    width: 100%;
    height: 100%;
    top: -0.25rem;
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  &__text {
    width: 100%;
    margin-top: 0.06rem;
    font-family: Noto Sans CJK SC;
    font-weight: 400;    
    font-size: 0.16rem;
    letter-spacing: 0px;
    text-align: center;
    vertical-align: middle;
    color: rgba(32, 53, 100, 1);
    line-height: 0.23rem;
  }

  &__button {
    width: 2.01rem;
    height: 0.42rem;
    border-radius: 0.21rem;
    background: linear-gradient(180deg, #35C6FF 0%, #0483FB 100%);
    margin-top: 0.11rem;
    text-align: center;
    line-height: 0.42rem;
    font-family: MFFangHei;
    font-weight: 400;
    font-size: 0.16rem;
    letter-spacing: 0px;
    vertical-align: middle;
    font-weight: bolder;
    color: rgba(210, 249, 253, 1);
  }

  &__close {
    width: 0.23rem;
    height: 0.23rem;
    position: absolute;
    right: 0;
    top: -0.23rem;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    background-image: url('https://cms.myapp.com/wupload/xy/yybtech/jEz7kroE.svg');
  }
}

.wish-property-pop-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}

