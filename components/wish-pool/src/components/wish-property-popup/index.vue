<template>
  <van-overlay
    z-index="9999"
    class-name="wish-property-pop-overlay"
  >
    <div class="wish-property-pop">
      <div class="wish-property-pop__container">
        <wish-property-container :wish-property="wishProperty" />
        <!-- 心愿物品弹窗内容区域 -->
        <div class="wish-property-pop__text">
          <slot />
        </div>
        <!-- 按钮 -->
        <div
          class="wish-property-pop__button"
          @click="handleButtonClick"
        >
          {{ buttonText }}
        </div>
        <!-- 关闭按钮 -->
        <div
          class="wish-property-pop__close"
          @click.stop="emits('close')"
        />
      </div>
    </div>
  </van-overlay>
</template>

<script setup lang="ts">
import 'vant/es/overlay/style';
import { Overlay as VanOverlay } from 'vant';
import { debounce } from '@tencent/mole-utils-lib';

import type { WishProperty } from '../../types';
import WishPropertyContainer from '../wish-property/index.vue';

defineProps<{
  /** 许愿物品 */
  wishProperty: WishProperty;
  /** 按钮文本 */
  buttonText: string;
}>();

const emits = defineEmits<(e: 'confirm' | 'close') => void>();

/** 处理按钮点击 */
const handleButtonClick = debounce(() => {
  emits('confirm');
}, 100);
</script>

<style lang="scss" scoped src="./index.scss"></style>
