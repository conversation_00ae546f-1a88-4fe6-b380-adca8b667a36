<template>
  <div class="games-info">
    <div class="games-info__title">
      参与活动的游戏
    </div>
    <div class="games-info__items">
      <div
        v-for="info in gamesInfo"
        :key="info.name"
        class="game-item"
        :style="{
          backgroundImage: `url(${info.icon})`
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { GameInfo } from '../../types';

defineProps<{
  gamesInfo: GameInfo[];
}>();

</script>

<style lang="scss" scoped src="./index.scss"></style>
