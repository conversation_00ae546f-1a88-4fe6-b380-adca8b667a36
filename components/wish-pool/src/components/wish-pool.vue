<template>
  <div>
    <!-- 选项卡 -->
    <wish-tab-nav :activeIndex="activeIndex" @activeChange="setActiveIndex"/>
    <!-- 选项卡对应页面 -->
    <wish-tab-pane :activeIndex="activeIndex"/>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import WishTabNav from './wish-pool/wish-tab-nav.vue' 
import WishTabPane from './wish-pool/wish-tab-pane.vue' 

const props = defineProps<{
}>();

const wishActInfo = [
  {
    id: '1',
    pools: [
      {
        id: 'p1',
        name: '<PERSON><PERSON>',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
      }
    ],
    dailyWishStartTime: '00:00',
    dailyWishEndTime: '22:00',
    dailyWishPublishTime: '23:00',
  }
]

const activeIndex = ref(0);

const setActiveIndex = (index: number) => {
  activeIndex.value = index;
}

</script>

<style lang="scss" scoped >
</style>

