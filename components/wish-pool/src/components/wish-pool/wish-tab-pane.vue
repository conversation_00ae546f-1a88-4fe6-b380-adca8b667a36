<template>
  <div class="wish-tab-pane">
    <div class="wish-pane-item" v-for="(item, index) in 6" :key="index" @click="selectProperty(index)">
      <wish-property :isSelected="index === selectedIndex"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import WishProperty from './wish-property.vue'

const props = defineProps<{
  activeIndex: number;
}>();

const selectedIndex = ref();

const selectProperty = (index: number) => {
  console.log('[selectProperty]', index)
  selectedIndex.value = index;
}

</script>

<style lang="scss" scoped >
.wish-tab-pane {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .wish-pane-item {
    width: 0.96rem;
    margin-bottom: 0.1rem;
  }
}
</style>
