<template>
  <div class="wish-tab-nav">
    <div v-for="(item, index) in 3" :key="index" :class="['nav-item', activeIndex === index ? 'nav-active' : '']"
      @click="navItemClick(index)">
      <div>{{ `7.1${index}` }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">

const props = defineProps<{
  activeIndex: number;
}>();
const emits = defineEmits(['activeChange']);


// 标签页导航点击事件
const navItemClick = (index: number) => {
  emits('activeChange', index);
}

</script>

<style lang="scss" scoped >
.wish-tab-nav {
  height: 0.26rem;
  display: flex;
  background-color: #F1F9FF;
  border-radius: 0.04rem;
  margin-bottom: 0.16rem;

  .nav-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.14rem;
    font-weight: 700;
    color: #88A1C0;
    overflow: hidden;
    border-radius: 0.0398rem;
  }

  .nav-active {
    background-color: #32DDFF;
    color: #FFFFFF;
  }
}
</style>
