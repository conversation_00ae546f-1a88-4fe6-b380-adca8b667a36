<template>
  <div class="wish-property">
    <div class="property-name-skin">
      <div class="name"><PERSON><PERSON></div>
    </div>
    <div class="property-content-skin">
      <div class="property-content">
        <div class="property-select">
          <PropertySelected v-if="isSelected" />
          <PropertyUnselected v-else />
        </div>
        <div class="property-content-img">
          <img src="https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png" alt="">
        </div>
        <div class="wish-desc">
          已注入<span class="points">32心愿点</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PropertyUnselected from './property-unselected.vue';
import PropertySelected from './property-selected.vue';

const props = defineProps<{
  isSelected: boolean;
}>();


</script>

<style lang="scss" scoped >
.wish-property {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .property-name-skin {
    width: 0.72rem;
    background-color: #fff;
    border-radius: 0.0145rem;
    padding: 0.02rem 0.02rem 0;

    .name {
      background: rgba(243, 244, 241, 1);
      text-align: center;
      padding: 0.01rem;
      border-radius: 0.0145rem;
      font-size: 0.14rem;
      font-weight: 700;
    }
  }

  .property-content-skin {
    width: 100%;
    border-radius: 0.0772rem;
    background-color: #fff;
    padding: 0.04rem;

    .property-content {
      height: 0.9rem;
      background: linear-gradient(to right bottom, rgba(73, 219, 255, 1), rgba(50, 154, 245, 1));
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 0.0772rem;
      position: relative;

      &-img {
        width: 0.54rem;
        height: 0.72rem;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .wish-desc {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        background-color: #fff;
        border-radius: 0 0 0.0772rem 0.0772rem;
        padding: 0.02rem 0.04rem;
        font-size: 0.08rem;
        box-shadow: -0.01rem 0.01rem 0.016rem rgba(194, 205, 227, 0.65);

        .points {
          font-size: 0.1rem;
          color: rgba(255, 0, 123, 1);
        }
      }

      .property-select {
        position: absolute;
        right: 0;
        top: 0;
        width: 0.18rem;
        height: 0.18rem;
        transform: translate(50%, -50%);
      }
    }
  }
}
</style>
