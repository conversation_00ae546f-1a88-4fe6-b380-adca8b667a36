import { computed, ref } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import { reusePromiseWrapper, safeJsonStringify } from '@tencent/mole-utils-lib';
import {
  authAllGames,
  getGameAuthorizeStatus,
} from '@tencent/yyb-game-info-auth-sdk';

import { AUTH_SUCCESS_TEXT, DEFAULT_ERROR_TEXT, SUCCESS_CODE } from '../constants';
import type { GameInfo } from '../types';
import { type GameAuthorize, AuthVisual } from '../types/auth';

/** 游戏授权相关逻辑 */
export function useGameAuth() {
  const { isLogin, toast, loginReady } = hooks.useMokaInject();

  /** 游戏授权信息 */
  const gameAuthorizeInfo = ref<GameAuthorize[]>([]);

  /** 参与游戏: TODO: 待从接口获取 */
  const supportGames = ref<GameInfo[]>([{
    name: '欢乐斗地主',
    icon: 'http://pp.myapp.com/ma_icon/0/icon_97949_1750838399/256',
    appID: 97949,
  }, {
    name: '三国杀',
    icon: 'http://pp.myapp.com/ma_icon/0/icon_12147939_1751511608/256',
    appID: 12147939,
  }, {
    name: '穿越火线-枪战王者',
    icon: 'http://pp.myapp.com/ma_icon/0/icon_12165022_1751590977/256',
    appID: 12165022,

  }, {
    name: '地下城与勇士：起源',
    icon: 'http://pp.myapp.com/ma_icon/0/icon_12250395_1750291377/256',
    appID: 12250395,
  },
  {
    name: '王者荣耀',
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/yybtech/dXt4lSTC.png',
    appID: 12127266,
  },
  ]);

  /** 未授权游戏 */
  const unAuthGames = computed(() => supportGames.value.filter(({ appID }) => {
    const isAuth = gameAuthorizeInfo.value.find(({ app }) => Number(app.appid) === Number(appID))
      ?.scenes?.every(authScene => authScene.visual === AuthVisual.VisibleToAll);
    return !isAuth;
  }));

  /** 是否所有参与的游戏均已授权已授权 */
  const isAuthed = computed(() => !(supportGames.value.length > 0 && unAuthGames.value.length > 0));

  /** 获取用户游戏授权信息 */
  const getUserGameAuthInfo = reusePromiseWrapper(async () => {
    // 获取所有参与的游戏 appID
    const appIDs = supportGames.value.map(info => `${info.appID}`);
    console.log(`[getUserGameAuthInfo] 获取游戏授权信息开始，appIDs: ${appIDs.join(',')}`);
    const resp = await getGameAuthorizeStatus(appIDs, '', utils.isTestEnv());
    console.log(`[getUserGameAuthInfo] 获取游戏授权信息成功，resp: ${safeJsonStringify(resp)}`);
    const { games } = resp;
    gameAuthorizeInfo.value = games;
  });

  /** 授权所有参与的游戏 */
  const authSupportGames = reusePromiseWrapper(async () => {
    await loginReady;
    // 未登录不获取
    if (!isLogin.value) return;

    console.log(`[authSupportGames] 游戏授权开始, appIDs: ${unAuthGames.value.map(info => info.appID).join(',')}`);
    const retCode = await authAllGames(gameAuthorizeInfo.value, '', utils.isTestEnv());
    console.log(`[authSupportGames] 游戏授权结束, retCode: ${retCode}`);

    // 判断是否授权成功
    if (retCode === SUCCESS_CODE) {
      toast?.(AUTH_SUCCESS_TEXT);
      await getUserGameAuthInfo();
    } else {
      toast?.(DEFAULT_ERROR_TEXT);
    }
  });

  /** 初始化授权游戏 */
  const initAuthSupportGames = async (games: GameInfo[]) => {
    supportGames.value = games;
    await getUserGameAuthInfo();
  };

  return {
    supportGames,
    unAuthGames,
    isAuthed,
    getUserGameAuthInfo,
    initAuthSupportGames,
    authSupportGames,
  };
}
