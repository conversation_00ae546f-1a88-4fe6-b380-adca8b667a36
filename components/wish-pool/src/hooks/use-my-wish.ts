import { ref } from 'vue';
import { reusePromiseWrapper, sleep } from '@tencent/mole-utils-lib';

import type { MyWishProperty } from '../types';

export function useMyWish() {
  // 我的心愿点
  const wishPoints = ref(0);
  // 我的心愿单物品
  const wishProperties = ref<MyWishProperty[]>([]);
  // 是否正在加载我的心愿信息
  const isLoading = ref(false);

  // 是否展示往期心愿弹窗
  const isShowPreviousWishesDialog = ref(false);
  // 是否展示我的心愿单弹窗
  const isShowMyWishesDialog = ref(false);

  // 获取我的心愿信息
  const getMyWishInfo = reusePromiseWrapper(async (wishID: string) => {
    console.log(`[getMyWishInfo] 获取我的心愿信息开始, wishID: ${wishID}`);
    isLoading.value = true;
    // TODO: 请求我的心愿信息
    await sleep(1000);
    wishPoints.value = 30;
    wishProperties.value.push(
      {
        id: 'p1',
        name: '<PERSON><PERSON>',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
        probability: '0.1%',
        currentWishPoints: 1,
      },
      {
        id: 'p2',
        name: 'Labubu',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
        probability: '0.1%',
        currentWishPoints: 1,
      },
      {
        id: 'p1',
        name: 'Ponda',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
        probability: '0.1%',
        currentWishPoints: 1,
      },
      {
        id: 'p2',
        name: 'Labubu',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
        probability: '0.1%',
        currentWishPoints: 1,
      },
      {
        id: 'p1',
        name: 'Ponda',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
        probability: '0.1%',
        currentWishPoints: 1,
      },
      {
        id: 'p2',
        name: 'Labubu',
        desc: '',
        cover: 'https://yyb.qpic.cn/moka-imgs/1752227579093-t1bp2rhd0d8.png',
        wishPoints: '132',
        probability: '0.1%',
        currentWishPoints: 1,
      },
    );

    isLoading.value = false;
    console.log(`[getMyWishInfo] 获取我的心愿信息成功, myWishPoints: ${wishPoints.value}, wishProperties: ${wishProperties.value.length}`);
  });

  return {
    wishPoints,
    wishProperties,
    isLoading,
    isShowPreviousWishesDialog,
    isShowMyWishesDialog,
    getMyWishInfo,
  };
}
