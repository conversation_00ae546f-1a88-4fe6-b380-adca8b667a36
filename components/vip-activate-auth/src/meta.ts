import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        appId: {
          type: 'number',
          title: '当前游戏appid',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'AppSelect',
          'x-decorator-props': {
            labelWidth: 'auto',
            labelWrap: true,
          },
          'x-component-props': {
            test: 10,
            bindType: 'appId',
          },
        },
        appids: {
          type: 'string',
          title: '授权游戏appid列表',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-decorator-props': {
            labelWidth: 'auto',
            labelWrap: true,
          },
          description: '使用英文逗号分隔',
        },
        authAllGame: {
          type: 'boolean',
          title: '是否授权全部游戏',
          default: false,
          description: '在装+已预约+下载未安装+有流水Appid（全量游戏）',
          'x-decorator-props': {
            labelWidth: 'auto',
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
        },
        scene: {
          type: 'string',
          title: '会员激活场景',
          'x-decorator': 'FormItem',
          required: true,
          'x-component': 'TextLink',
          'x-component-props': {
            linkTitle: '激活场景配置',
            linkStrOrFunc() {
              return 'https://wuji.woa.com/p/edit?appid=ovbu_yyb_ydc_soft_2&schemaid=dim_game_welfare_source_channel_df';
            },
          },
        },
        collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '按钮文案配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 3,
                    labelWrap: true,
                  },
                  properties: {
                    buttonConfig: {
                      type: 'object',
                      properties: {
                        ['3.text']: {
                          type: 'string',
                          title: '未激活未授权',
                          'x-decorator': 'FormItem',
                          'x-component': 'Input',
                          default: '激活',
                          required: true,
                        },
                        ['3.pic']: {
                          type: 'string',
                          title: '未激活未授权图片上传',
                          'x-decorator-props': {
                            labelWidth: 'auto',
                            labelWrap: true,
                          },
                          'x-decorator': 'FormItem',
                          'x-component': 'Upload',
                          'x-component-props': {
                            lockImgNorm: {
                              width: 0.5,
                              height: 0.5,
                            },
                          },
                        },
                        ['2.text']: {
                          type: 'string',
                          title: '未激活已授权',
                          'x-decorator': 'FormItem',
                          'x-component': 'Input',
                          default: '激活',
                          required: true,
                        },
                        ['2.pic']: {
                          type: 'string',
                          title: '未激活已授权图片上传',
                          'x-decorator-props': {
                            labelWidth: 'auto',
                            labelWrap: true,
                          },
                          'x-decorator': 'FormItem',
                          'x-component': 'Upload',
                          'x-component-props': {
                            lockImgNorm: {
                              width: 0.5,
                              height: 0.5,
                            },
                          },
                        },
                        ['1.text']: {
                          type: 'string',
                          title: '已激活未授权',
                          'x-decorator': 'FormItem',
                          'x-component': 'Input',
                          default: '授权',
                          required: true,
                        },
                        ['1.pic']: {
                          type: 'string',
                          title: '已激活未授权图片上传',
                          'x-decorator-props': {
                            labelWidth: 'auto',
                            labelWrap: true,
                          },
                          'x-decorator': 'FormItem',
                          'x-component': 'Upload',
                          'x-component-props': {
                            lockImgNorm: {
                              width: 0.5,
                              height: 0.5,
                            },
                          },
                        },
                        ['0.text']: {
                          type: 'string',
                          title: '已激活已授权',
                          'x-decorator': 'FormItem',
                          'x-component': 'Input',
                          default: '已激活',
                          required: true,
                        },
                        ['0.pic']: {
                          type: 'string',
                          title: '已激活已授权图片上传',
                          'x-decorator-props': {
                            labelWidth: 'auto',
                            labelWrap: true,
                          },
                          'x-decorator': 'FormItem',
                          'x-component': 'Upload',
                          'x-component-props': {
                            lockImgNorm: {
                              width: 0.5,
                              height: 0.5,
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
