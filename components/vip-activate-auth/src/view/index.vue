<template>
  <div
    class="vip-activate-auth"
    @click="activateAndAuthGame"
  >
    <img
      v-if="buttonConfig.pic"
      :src="buttonConfig.pic"
    >
    <span
      v-else
    >{{ buttonConfig.text }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, getCurrentInstance, ref } from 'vue';
import { ActivateAuthStatus, common, hooks } from '@tencent/moka-ui-domain';

import { CALLER_EVENT } from '../constant';
import { type ComponentConfig } from '../types';

const props = defineProps<{
  config: ComponentConfig;
}>();

const activateAuthStatus = ref(ActivateAuthStatus.InactivatedAndUnauthorized);

const buttonConfig = computed(() => props.config.buttonConfig[activateAuthStatus.value]);

const {
  inMagic,
  isLogin,
  $bus,
  loginReady,
  openLogin,
  toast,
} = hooks.useMokaInject();
const instance = getCurrentInstance();

const getActivatedAuthStatus = async (isOnlyQuery = true) => {
  const { scene, authAllGame, appId, appids = '' } = props.config;
  const result = await common.ActivateAndAuthGame({
    app_id: appId,
    appids: appids.split(',').map(appid => Number(appid)),
    scene_id: scene,
    is_all_app: authAllGame,
    is_only_query: isOnlyQuery,
    activityIID: props.config.id,
  });

  if (result?.ret === 0) {
    activateAuthStatus.value = result.status;
    return true;
  }

  if (!isOnlyQuery) {
    toast?.(result?.msg || '系统繁忙，请重试');
  }

  return false;
};

let isRequesting = false;
const activateAndAuthGame = async () => {
  if (inMagic) return;

  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  if (isRequesting || activateAuthStatus.value === ActivateAuthStatus.ActivatedAndAuthorized) return;

  isRequesting = true;
  try {
    if (await getActivatedAuthStatus(false)) {
      $bus?.$emit(CALLER_EVENT[activateAuthStatus.value], instance?.proxy, {});
    }
  } catch (error) {
    console.error('[method: getActivatedAuthStatus]', error);
  } finally {
    isRequesting = false;
  }
};

const initData = async () => {
  if (inMagic) return;
  await loginReady;
  if (!isLogin.value) return;
  void getActivatedAuthStatus();
};

// 初始化激活授权状态
void initData();

// 组件内部方法，提供外部调用
defineExpose({ activateAndAuthGame });
</script>

<style lang="scss" src="./index.scss" scoped />
