import { ActivateAuthStatus } from '@tencent/moka-ui-domain';

import { CALLER_EVENT, CALLER_METHOD } from './constant';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [
    {
      label: '激活会员&授权成功通知',
      value: CALLER_EVENT[ActivateAuthStatus.ActivatedAndAuthorized],
      desc: '',
    },
    {
      label: '激活会员失败通知',
      value: CALLER_EVENT[ActivateAuthStatus.InactivatedAndAuthorized],
      desc: '',
    },
    {
      label: '授权游戏失败通知',
      value: CALLER_EVENT[ActivateAuthStatus.ActivatedAndUnauthorized],
      desc: '',
    },
  ],
  /** 组件内部方法，提供外部调用 */
  methods: [{
    label: '激活会员&授权游戏',
    value: CALLER_METHOD,
    desc: '',
  }],
};
