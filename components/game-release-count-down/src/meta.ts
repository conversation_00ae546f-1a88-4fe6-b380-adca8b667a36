import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用 formily 协议
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        lotteryModID: {
          type: 'string',
          title: '抽奖模块 ID（用于展示抽取的 Q 币奖励）',
          'x-decorator': 'FormItem',
          'x-component': 'ModSelect',
          'x-component-props': {
            modType: 11,
          },
        },
        releaseTime: {
          type: 'string',
          title: '游戏首发时间',
          required: true,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            wrapperWidth: '95%',
          },
          'x-component': 'DatePicker',
          'x-component-props': {
            type: 'datetime',
          },
        },
        Collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            // 预约期配置
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '预约期展示配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'bookConfigs.text': {
                      type: 'string',
                      title: '文案展示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '距离$count天首发',
                      required: true,
                      'x-validator': [{ required: true, message: '请输入预约期展示文本' }],
                      'x-decorator-props': {
                        extra: '$count 会替换为距离设置的首发时间的天数，$reward 会替换为绑定的抽奖模块奖励描述',
                      },
                    },
                    'bookConfigs.urgentText': {
                      type: 'string',
                      title: '距离首发少于 24 小时文案展示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '距离$time小时首发',
                      'x-decorator-props': {
                        extra: '$time 会替换为距离设置的首发时间的小时，$reward 会替换为绑定的抽奖模块奖励描述',
                      },
                    },
                    'bookConfigs.jumpUrl': {
                      type: 'string',
                      title: '跳转链接（点击后）',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '',
                    },
                    'bookConfigs.textColor': {
                      type: 'string',
                      title: '文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      default: 'rgba(0, 0, 0, 1)',
                    },
                    'bookConfigs.backgroundColor': {
                      type: 'string',
                      title: '背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      default: 'rgba(255, 255, 255, 0)',
                    },
                    'bookConfigs.backgroundImage': {
                      type: 'string',
                      title: '背景图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
            // 首发期配置
            tab2: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '首发期展示配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'releaseConfigs.text': {
                      type: 'string',
                      title: '文案展示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '游戏已首发',
                      'x-decorator-props': {
                        extra: '$reward 会替换为绑定的抽奖模块奖励描述',
                      },
                    },
                    'releaseConfigs.jumpUrl': {
                      type: 'string',
                      title: '跳转链接（点击后）',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '',
                    },
                    'releaseConfigs.textColor': {
                      type: 'string',
                      title: '文本颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(0, 0, 0, 1)',
                    },
                    'releaseConfigs.backgroundColor': {
                      type: 'string',
                      title: '背景颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      default: 'rgba(255, 255, 255, 0)',
                    },
                    'releaseConfigs.backgroundImage': {
                      type: 'string',
                      title: '背景图',
                      'x-decorator-props': {
                        labelWrap: true,
                      },
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                      default: '',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
