import { Status } from '../type';
/** 一天 */
export const ONE_DAY = 1000 * 60 * 60 * 24;
/** 一小时 */
export const ONE_HOUR = 1000 * 60 * 60;
/** 组件内抛出事件 */
export const COMPONENT_EVENT = {
  [Status.Booking]: 'booking',
  [Status.Release]: 'release',
};
/** 默认错误提示文案 */
export const DEFAULT_ERROR_MESSAGE = '系统繁忙，请稍候再试';
/** 组件抛出事件 */
export const COMPONENT_METHODS = {
  UpdateLotteryReward: 'updateLotteryReward',
};
