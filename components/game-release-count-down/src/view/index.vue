<template>
  <div
    class="count-down"
    :style="componentStyle"
    @click="handleClick"
  >
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, getCurrentInstance, ref } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';

import { getLotteryReward, getServerTime } from '../api';
import { COMPONENT_EVENT, COMPONENT_METHODS } from '../constant';
import { ComponentConfig, Status } from '../type';
import { calculateDaysRemaining, getLotteryID } from '../utils';

const { $bus, inMagic } = hooks.useMokaInject();
const instance = getCurrentInstance();

const props = defineProps<{
  config: ComponentConfig;
}>();

const serverTime = ref<string>();
const rewardInfo = ref<string>('');

/** 距离首发还剩余多少天 */
const timesRemaining = computed(() => {
  const { releaseTime } = props.config;
  return calculateDaysRemaining(releaseTime, serverTime.value);
});

/** 是否已首发 */
const isRelease = computed(() => {
  const { daysRemaining, hoursRemaining } = timesRemaining.value;
  return daysRemaining <= 0 && hoursRemaining <= 0;
});

/** 组件展示文本 */
const text = computed(() => {
  const { releaseConfigs, bookConfigs } = props.config;
  const { daysRemaining, hoursRemaining } = timesRemaining.value;

  // 未获取到服务器时间，直接返回
  if (!serverTime.value && !inMagic) {
    return '游戏暂未首发';
  }

  // 已首发
  if (isRelease.value) {
    return releaseConfigs.text.replaceAll('$reward', rewardInfo.value);
  }

  // 小时
  if (hoursRemaining <= 24) {
    return bookConfigs.urgentText.replaceAll('$time', `${hoursRemaining}`).replaceAll('$reward', rewardInfo.value);
  }

  return bookConfigs.text.replaceAll('$count', `${daysRemaining}`).replaceAll('$reward', rewardInfo.value);
});

/** 组件样式 */
const componentStyle = computed(() => {
  const { releaseConfigs, bookConfigs } = props.config;
  if (isRelease.value) {
    const { textColor, backgroundColor, backgroundImage } = releaseConfigs;
    return {
      color: textColor,
      backgroundColor,
      backgroundImage: `url(${backgroundImage ?? ''})`,
    };
  }

  const { textColor, backgroundColor, backgroundImage } = bookConfigs;
  return {
    color: textColor,
    backgroundColor,
    backgroundImage: `url(${backgroundImage ?? ''})`,
  };
});

/** 处理点击事件 */
const handleClick = () => {
  const { releaseConfigs, bookConfigs } = props.config;
  // 首发期点击
  if (isRelease.value) {
    if (releaseConfigs.jumpUrl) {
      utils.jumpUrl(releaseConfigs.jumpUrl);
    }

    $bus?.$emit(COMPONENT_EVENT[Status.Release], instance?.proxy, {});
    return;
  }

  // 预约期点击
  if (bookConfigs.jumpUrl) {
    utils.jumpUrl(bookConfigs.jumpUrl);
  }

  $bus?.$emit(COMPONENT_EVENT[Status.Booking], instance?.proxy);
};

/** 获取抽奖信息 */
const getRewardInfo = async () => {
  const { lotteryModID, id } = props.config;
  if (!lotteryModID) {
    return;
  }

  const resp = await getLotteryReward(id, lotteryModID, getLotteryID(lotteryModID));
  console.log('[game-release-count-down] 获取抽奖奖励信息成功', resp);
  const { code, data } = resp;
  if (code !== 0 || !data) {
    rewardInfo.value = '';
    return;
  }

  rewardInfo.value = data?.property_results[0]?.instance_info?.desc ?? '';
};

const init = async () => {
  const time = await getServerTime();
  serverTime.value = time;
  await getRewardInfo();
};

void init();

// 组件抛出事件
defineExpose({
  /** 更新收集卡片 */
  [COMPONENT_METHODS.UpdateLotteryReward]: () => {
    void getRewardInfo();
  },
});
</script>

<style lang="scss" src="./index.scss" scoped />
