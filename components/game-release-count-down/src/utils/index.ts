import { actUtils } from '@tencent/moka-data-core';

import { ONE_DAY, ONE_HOUR } from '../constant';

/**
 * 计算当前时间和首发时间的时间间隔（天数）
 * @param releaseTime 首发时间
 * @param now 当前时间
 * @returns 当前时间和首发时间的时间间隔（天数）
 */
export function calculateDaysRemaining(releaseTime: string, now?: string) {
  const nowDate = now ? new Date(now) : new Date(Date.now());
  const releaseDate = new Date(releaseTime);

  // 计算时间差
  const timeDifference = releaseDate.getTime() - nowDate.getTime();
  // 将时间差转换为天数
  const daysRemaining = Math.ceil(timeDifference / (ONE_DAY));
  const hoursRemaining = Math.floor(timeDifference / (ONE_HOUR));

  return {
    daysRemaining,
    hoursRemaining,
  };
}

/**
 * 获取抽奖 ID
 * @param modID 后端模块 ID
 * @returns 抽奖 ID
 */
export function getLotteryID(modID: string | number) {
  if (!modID) return '';
  return actUtils.getCIIDByModID({ modID: String(modID) });
}
