import { actExecRequester, ActInterfaceName, actOtherRequester } from '@tencent/moka-data-core';
import { ActExecRespBody, StandardizedResp } from '@tencent/moka-data-core/dist/types/requester';
import { utils } from '@tencent/moka-ui-domain';

import { DEFAULT_ERROR_MESSAGE } from '../constant';

/** 抽奖结果 */
export interface LotteryResult {
  property_results: ({
    instance_info: {
      desc: string;
    };
  })[];
}

/** 获取服务器时间 */
export async function getServerTime() {
  const res = await actOtherRequester.request<{ activity_iid: string }, { server_ts: string }>({
    interfaceName: ActInterfaceName.GetActivityConfig,
    reuse: true,
    data: {
      activity_iid: (globalThis as any).mappingInfo.activity_iid,
    },
  });

  console.log(res.body?.data?.server_ts);
  return res.body?.data?.server_ts;
}

/**
 * 获取抽奖奖励列表
 * @param componentID 组件 ID
 * @param modID 抽奖模块 ID
 * @param lotteryIID 抽奖 ID
 * @returns 抽奖奖励列表
 */
export async function getLotteryReward(
  componentID: string,
  modID: string,
  lotteryIID: string,
) {
  const result = await actExecRequester.request<{ lottery_iid: string }, LotteryResult>({
    isTest: utils.isTestEnv(),
    // 抽奖模块 type
    componentType: '11',
    componentID,
    componentInfo: {
      // 抽奖模块 ID
      modID,
    },
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/GetUserLotteryResult',
      data: {
        lottery_iid: lotteryIID,
      },
    },
    qualifier_params: [],
  });

  return handleResp(result);
}

/**
 * 处理响应
 * @param resp 响应数据
 * @returns 处理后的响应结果
 */
function handleResp<T>(resp: StandardizedResp<ActExecRespBody<T>>) {
  const { code, tip, body, statusCode } = resp;

  // 状态码非 200，返回默认错误
  if (statusCode !== 200) {
    return {
      code: -99999,
      msg: DEFAULT_ERROR_MESSAGE,
      data: null,
    };
  }

  return {
    code,
    msg: tip,
    data: body?.data?.data,
  };
}
