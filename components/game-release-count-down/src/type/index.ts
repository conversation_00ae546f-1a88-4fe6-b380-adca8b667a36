/** 组件配置 */
export interface ComponentConfig {
  /** 组件 ID */
  id: string;
  /** 抽奖模块 ID */
  lotteryModID: string;
  /** 活动时间 */
  releaseTime: string;
  /** 预约期配置 */
  bookConfigs: BookConfigs;
  /** 首发期配置 */
  releaseConfigs: DisplayConfigs;
}

/** 预约期展示配置 */
export interface BookConfigs extends DisplayConfigs {
  /** 距离首发少于 24 小时的文案展示 */
  urgentText: string;
}

/** 展示配置 */
export interface DisplayConfigs {
  /** 文案展示 */
  text: string;
  /** 跳转链接（点击后） */
  jumpUrl: string;
  /** 文本颜色 */
  textColor: string;
  /** 背景颜色 */
  backgroundColor: string;
  /** 背景图 */
  backgroundImage: string;
}

/** 组件状态 */
export enum Status {
  /** 预约期点击 */
  Booking = 'booking',
  /** 首发期点击 */
  Release = 'release',
}
