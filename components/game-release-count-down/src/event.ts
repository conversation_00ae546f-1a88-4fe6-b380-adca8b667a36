
import { COMPONENT_EVENT, COMPONENT_METHODS } from './constant';
import { Status } from './type';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [
    {
      label: '预约期点击',
      value: COMPONENT_EVENT[Status.Booking],
      desc: '',
    },
    {
      label: '首发期点击',
      value: COMPONENT_EVENT[Status.Release],
      desc: '',
    },
  ],
  /** 组件内部方法，提供外部调用 */
  methods: [
    {
      label: '更新抽奖记录',
      value: COMPONENT_METHODS.UpdateLotteryReward,
      desc: '',
    },
  ],
};
