<template>
  <div
    v-if="!hideNav"
    :class="isShowPCStyle ? 'pc-nav-comp' : 'top-nav-comp'"
    dt-clck-ignore="true"
    dt-imp-ignore="true"
  >
    <pc-login
      v-if="isShowPCStyle"
      :login-type="loginType"
      :is-login="isLogin ?? false"
      :user-avatar="userAvatar"
      :nick-name="userInfo?.nickname"
      @login="changeLogin"
      @switch-login="changeLogin"
      @logout="logout"
    />
    <mobile-login
      v-else
      :login-type="loginType"
      :is-login="isLogin ?? false"
      :user-avatar="userAvatar"
      @switch-login="changeLogin"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';
import { getURLQueryParams, isPC } from '@tencent/mole-utils-lib';
import { LoginInfo, LoginType } from '@tencent/yyb-login-core';

import MobileLogin from '../components/mobile-login.vue';
import PcLogin from '../components/pc-login.vue';
import { NavStyle } from '../constant';
import type { ComponentConfig } from '../types';
const instance = getCurrentInstance();

const props = defineProps<{
  config: ComponentConfig;
}>();

const { isLogin, inMagic, getUserInfo, openLogin, switchLogin, loginReady } = hooks.useMokaInject();
const userInfo = ref<LoginInfo & { nickname: string; headImgUrl: string } | null>(null);

const hideNav = ref(false);
// 是否展示 PC 样式：当且仅当用户设备访问是 PC 且勾选 PC 宽屏样式
const isShowPCStyle = computed(() => Number(props.config.navStyle) === NavStyle.PC && isPC(navigator.userAgent));
onMounted(async () => {
  const params = getURLQueryParams(location.href);
  hideNav.value = !!params.hideUserNav;
  if (hideNav.value || inMagic) {
    return;
  }

  await loginReady;
  const loginInstance = instance?.appContext.config.globalProperties?.$magicLogin;
  const result = await loginInstance.checkLogin();
  if (!isLogin.value && !result) return;

  const resp = await getUserInfo?.();
  if (resp) userInfo.value = resp;
});

const loginType = computed(() => userInfo.value?.loginType || LoginType.None);
const userAvatar = computed(() => {
  const defaultIcon = isShowPCStyle.value ? 'https://cms.myapp.com/xy/yybtech/oqI5unoJ.svg' : 'https://inews.gtimg.com/newsapp_ls/0/6688639827/0';
  return userInfo.value?.headImgUrl || defaultIcon;
});

const changeLogin = () => {
  // 未登录，则拉起登录
  if (!isLogin.value) {
    console.log('[yyb-login-header] changeLogin 执行登录');
    openLogin?.();
    return;
  }

  // 切换登录
  switchLogin?.();
};

/** 退出登录 */
const logout = async () => {
  const loginInstance = instance?.appContext.config.globalProperties?.$magicLogin;
  loginInstance.logout?.();
};
</script>

<style lang="scss">
.top-nav-comp {
  width: 100%;
  position: fixed;
  // 固定在顶部
  top: 0 !important;
  left: 0 !important;
  right: auto !important;
  z-index: 998;
}

// PC 导航栏
.pc-nav-comp {
  position: fixed;
  // 吸顶右侧
  top: 0 !important;
  right: 0 !important;
  left: auto !important;
  width: fit-content !important;
  z-index: 998;
}
</style>
