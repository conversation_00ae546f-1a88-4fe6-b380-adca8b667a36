import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      navStyle: {
        type: 'number',
        title: '顶部导航栏样式',
        default: 1,
        enum: [
          {
            label: '移动端',
            value: 1,
          },
          {
            label: 'PC 宽屏',
            value: 2,
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
