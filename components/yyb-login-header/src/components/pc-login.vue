<template>
  <div
    ref="loginHeaderRef"
    class="pc-login-header"
    @click.stop="handleLoginClick"
  >
    <div
      class="user-icon"
      :style="userIconStyle"
    />
    <div class="user-info">
      {{ userInfoText }}
    </div>
    <div
      class="select-icon"
      :style="iconStyle"
    />
    <transition name="fade">
      <div
        v-show="isExpand"
        class="login-panel"
      >
        <div
          class="login-panel__item"
          @click.stop="handleSwitchLoginClick(LoginAction.Switch)"
        >
          切换微信QQ
        </div>
        <div
          class="login-panel__item"
          @click.stop="handleSwitchLoginClick(LoginAction.Logout)"
        >
          退出
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import 'vant/es/dialog/style';
import { showConfirmDialog } from 'vant';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { replaceURLProtocol } from '@tencent/mole-utils-lib';
import { LoginType } from '@tencent/yyb-login-core';

/** 登录操作 */
enum LoginAction {
  /** 切换登录 */
  Switch = 'switch',
  /** 登出 */
  Logout = 'logout',
}

const props = defineProps<{
  /** 头像信息 */
  userAvatar: string;
  /** 是否已登录 */
  isLogin: boolean;
  /** 登录类型 */
  loginType: LoginType;
  /** 用户名称 */
  nickName?: string;
}>();

const emits = defineEmits(['login', 'switchLogin', 'logout']);

/** 登录状态文本 */
const textByLoginType = {
  [LoginType.MOBILEQ]: '(QQ)',
  [LoginType.WX]: '(微信)',
  // 下面两个暂时不会用上
  [LoginType.PHONE]: '(手机号)',
  [LoginType.None]: '',
};

/** 是否已展开 */
const isExpand = ref(false);
/** 登录导航栏元素 */
const loginHeaderRef = ref<HTMLElement | null>();

/** ICON 样式 */
const iconStyle = computed(() => ({
  transform: `rotate(${isExpand.value ? 180 : 0}deg)`,
}));

/** 用户头像样式信息 */
const userIconStyle = computed(() => ({
  backgroundImage: `url(${replaceURLProtocol(props.userAvatar)})`,
}));

/** 用户信息 */
const userInfoText = computed(() => {
  if (props.isLogin) {
    return `${props.nickName ?? ''}${textByLoginType[props.loginType]}`;
  }

  return '登录';
});

/** 处理登录点击 */
const handleLoginClick = async () => {
  // 未登录点击拉起登录
  if (!props.isLogin) {
    emits('login');
    return;
  }

  isExpand.value = !isExpand.value;
};

/** 处理切换登录 */
const handleSwitchLoginClick = async (action: LoginAction) => {
  isExpand.value = !isExpand.value;
  // 切换登录
  if (action === LoginAction.Switch) {
    emits('switchLogin');
    return;
  }

  try {
    // 是否退出当前账号
    await showConfirmDialog({
      title: '',
      message: '是否退出当前账号',
    });

    emits('logout');
  } catch (err) {
    console.log('取消退出当前账号');
  }
};

/** 点击外部 */
const clickOutSide = (e: MouseEvent) => {
  const element = (e.target ?? e.srcElement) as HTMLElement;
  if (!loginHeaderRef.value) return;
  if (element !== loginHeaderRef.value && !element?.contains(loginHeaderRef.value)) {
    isExpand.value = false;
    return;
  }
};

onMounted(() => {
  document.body.addEventListener('click', clickOutSide);
});

onUnmounted(() => {
  document.body.removeEventListener('click', clickOutSide);
});

</script>

<style lang="scss" scoped>
.pc-login-header {
  --pc-header-height: 36px;
  --pc-login-panel: 92px;

  display: flex;
  align-items: center;
  height: var(--pc-header-height);
  border-radius: 0 0 0 12px;
  padding: 0 12px;
  background: rgba(0, 0, 0, 0.6);
  color: rgba(255, 255, 255, 1);
}

.user-icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  margin-right: 4px;
}

.user-info {
  margin-right: 4px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0px;
  vertical-align: middle;
}

.select-icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url('https://cms.myapp.com/xy/yybtech/FygmXv0X.svg');
  transition: transform 0.5s;
}

.login-panel {
  position: absolute;
  top: calc(var(--pc-header-height));
  right: 8px;
  box-sizing: border-box;
  width: 264px;
  height: var(--pc-login-panel);
  padding: 8px 0;
  border-radius: 16px;
  border-width: 0.5px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 1);
  flex-direction: column;
  box-shadow: 0px 12px 26px 0px rgba(0, 0, 0, 0.06);
  border: 0.5px solid rgba(0, 0, 0, 0.2);

  font-family: Noto Sans SC;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0%;
  vertical-align: middle;
  color:rgba(0, 0, 0, 0.85);
  transition: transform ease 0.3s;

  &__item {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height: 38px;
    width: 100%;
    padding-left: 16px;
  }
}

/* 进入前/离开后的状态 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 进入/离开过程中的动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

/* 进入后/离开前的状态 */
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>
