<template>
  <div class="top-nav-box">
    <div
      class="nav-user"
    >
      <div
        v-report="{eid: isLogin ? 'switch_account' : 'login'}"
        class="user-avatar-wrapper"
        @click="changeLogin"
      >
        <div class="user-avatar">
          <img :src="replaceURLProtocol(userAvatar)">
          <i
            class="user-type"
            :class="{'qq-icon': loginType === LoginType.MOBILEQ, 'wx-icon': loginType === LoginType.WX}"
          />
        </div>
        <div v-if="noChangeLogin">
          {{ isLogin ? '' : '点击登录' }}
        </div>
        <div
          v-else
          class="user-text-name"
        >
          {{ isLogin ? '切换账号' : '点击登录' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { isYSDK, replaceURLProtocol } from '@tencent/mole-utils-lib';
import { LoginType } from '@tencent/yyb-login-core';

defineProps<{
  /** 头像信息 */
  userAvatar: string;
  /** 是否已登录 */
  isLogin: boolean;
  /** 登录类型 */
  loginType: LoginType;
}>();

const emits = defineEmits(['switchLogin']);

// 不支持切换登录
const noChangeLogin = ref(isYSDK(navigator.userAgent));
// 切换登录
const changeLogin = () => {
  if (noChangeLogin.value) return;
  emits('switchLogin');
};

</script>

<style lang="scss">
.top-nav-box {
  display: flex;
  align-items: center;
  width: 3.59rem;
  height: 0.38rem;
  margin: 0 auto;
  background: rgba(0, 0, 0, .4);
  border-radius: 0 0 0.28rem 0.28rem;
}
.top-nav-box-simple {
  background: none;
  border-radius: initial;
  width: 100%;
}

.nav-user {
  display: flex;
  align-items: center;
  margin-left: 0.16rem;
  font-size: 0.14rem;
  color: #fff;

  .user-avatar-wrapper {
    display: flex;
    align-items: center;
  }

  .user-text-name {
    max-width: 0.6rem;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
  }

  .user-avatar {
    margin-right: 0.05rem;
    position: relative;

    img {
      display: block;
      width: 0.26rem;
      height: 0.26rem;
      border-radius: 50%;
    }

    .user-type {
      width: 0.14rem;
      height: 0.1rem;
      display: block;
      position: absolute;
      right: -0.06rem;
      bottom: -0.03rem;

      &.qq-icon {
        background: url("data:image/png;base64,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") 0 0 no-repeat;
        background-size: contain;
      }

      &.wx-icon {
        background: url("data:image/png;base64,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") 0 0 no-repeat;
        background-size: contain;
      }
    }
  }
}

.nav-right {
  > a {
    display: flex;
    align-items: center;
    position: relative;

    &.parting-line::after {
      content: '';
      width: 1px;
      height: 0.12rem;
      display: block;
      background-color: #FFF;
      opacity: 0.3;
      margin: 0 0.09rem;
    }
  }
}

.head-icon {
  width: 0.24rem;
  height: 0.24rem;
  display: block;
}
</style>
