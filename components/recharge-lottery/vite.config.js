import vue from '@vitejs/plugin-vue';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    extensions: ['.vue', '.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
  },
  server: {
    open: '/example/index.html',
    host: true,
    cors: true,
    port: 3000,
  },
  build: {
    rollupOptions: {
      input: 'example/index.html',
    },
  },
});
