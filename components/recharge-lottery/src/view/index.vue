<template>
  <div
    :dt-cmd="`hold=${rewards.length === 0 || isLoginAndSelectOs}`"
    class="recharge-lottery"
  >
    <lottery-pool
      ref="lotteryPoolRef"
      :rewards="rewards"
    />
    <lottery-button
      v-if="rewards.length >= 0"
      :user-lottery-status="userLotteryStatus"
      :next-level-recharge="nextLevelRecharge"
      :lottery-btn-bg="config.lotteryBtnBg"
      dt-eid="recharge-lottery-button"
      :dt-params="utils.getReportParamsStr({
        style_type: userLotteryStatus,
        mod_id: 'recharge-lottery',
        remaining_rewards: lotteryNum,
        awards_num: winningRewardsNum.length,
        group_id: config.taskGroupID,
      }, inMagic)"
      @start-lottery="handleLottery"
    />
    <lottery-success-dialog
      v-if="isLotterySuccessDialogVisible && winningPrize"
      :winning-prize="winningPrize"
      :dialog-bg="config.lotteryResultBg"
      @close="() => isLotterySuccessDialogVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref, toRefs } from 'vue';
import { hooks, store, utils } from '@tencent/moka-ui-domain';
import { debounce, sleep } from '@tencent/mole-utils-lib';

import { executeLotteryTask, getLotteryTaskGroup, getYybPoint } from '../api';
import { COMPONENT_EVENT, COMPONENT_METHODS } from '../constant';
import { DEFAULT_ERROR_MSG, LOTTERY_FAIL_MSG, LotteryAnimateStatus, NOT_BINDING_QQ_ERROR_MSG, RewardStatus, TaskStatus, UserLotteryStatus } from '../constant/lottery';
import { DEFAULT_BG, DEFAULT_LOTTERY_BG, DEFAULT_WINNING_BG, REWARDS } from '../constant/mock-data';
import { Config } from '../types';
import type { RewardInfo } from '../types/lottery';
import LotteryButton from './components/lottery-button.vue';
import LotteryPool from './components/lottery-pool.vue';
import LotterySuccessDialog from './components/lottery-success-dialog.vue';

const { toast, inMagic, $bus, isLogin } = hooks.useMokaInject();
const instance = getCurrentInstance();

/** 奖池组件引用 */
const lotteryPoolRef = ref<HTMLElement>();

const props = defineProps<{
  config: Config;
}>();

const { config } = toRefs(props);

// 是否中奖成功弹窗可见
const isLotterySuccessDialogVisible = ref(false);
// 中奖的奖品
const winningPrize = ref<RewardInfo | null>(null);
// 抽奖次数
const lotteryNum = ref(0);
// 达到下一档位还需要的充值
const nextLevelRecharge = ref(0);
// 任务状态
const taskStatus = ref(TaskStatus.Unknown);
// 任务 ID
const lotteryTaskID = ref('');
// 奖品列表
const rewards = ref<RewardInfo[]>([]);

const preloadLotteryImage = () => {
  [config.value.lotteryBg, config.value.winningBg].forEach((imgSrc) => {
    const img = new Image();
    img.src = imgSrc;
  });
};

const mockRewards = computed<RewardInfo[]>(() => {
  const {
    notWinningBg,
    lotteryBg,
    winningBg,
    lotteryRewardNum,
  } = config.value;

  return REWARDS.map(item => ({
    ...item,
    bg: notWinningBg || DEFAULT_BG,
    lotteryBg: lotteryBg || DEFAULT_LOTTERY_BG,
    winningBg: winningBg || DEFAULT_WINNING_BG,
  })).slice(0, lotteryRewardNum || 9);
});

onMounted(async () => {
  if (inMagic) {
    rewards.value = mockRewards.value;
    return;
  }

  preloadLotteryImage();

  void getLotteryTaskGroupAndLotteryNum();
});

const winningRewardsNum = computed(() => rewards.value.filter(reward => reward.status === RewardStatus.Winning));

// 是否正在抽奖中
let isDoingLottery = false;
// 是否正在播放抽奖动画
const isLotteryAnimatePlaying = computed(() => lotteryPoolRef.value?.animateStatus !== LotteryAnimateStatus.End);

/** 用户抽奖状态 */
const userLotteryStatus = computed(() => {
  if (taskStatus.value === TaskStatus.HasReceived) return UserLotteryStatus.HasLotteryOut;
  if (lotteryNum.value === 0) return UserLotteryStatus.DisableLottery;

  return UserLotteryStatus.CanLottery;
});

const isLoginAndSelectOs = computed(() => isLogin.value && !config.value.hasSelectOS);

/** 获取抽奖任务集和抽奖次数 */
const getLotteryTaskGroupAndLotteryNum = async () => {
  const {
    taskGroupID,
    id,
    pointGroupID,
    notWinningBg,
    lotteryBg,
    winningBg,
  } = config.value;

  try {
    const [taskGroupData, yybPoint] = await Promise.all([
      getLotteryTaskGroup(taskGroupID),
      getYybPoint(pointGroupID, id),
    ]);
    const { taskID, gifts, status, nextLevelRecharge: nextRecharge } = taskGroupData;
    lotteryNum.value = yybPoint;
    rewards.value = gifts.map(gift => ({ ...gift, bg: notWinningBg, lotteryBg, winningBg }));
    lotteryTaskID.value = taskID;
    taskStatus.value = status;
    nextLevelRecharge.value = nextRecharge;
  } catch (err) {
    console.error('[recharge-lottery] getLotteryTaskGroupAndLotteryNum, err:', err);
  }
};

/** 抽奖的前置处理，检查是否需要拉起 Q 号绑定 */
let qq = '';
const qqBindingStore = store.useQQBindingStore();
const handleLotteryBefore = async () => {
  console.log('[recharge-lottery] 抽奖前检查开始');
  const { getLoginType, ELoginType: loginType } = utils;
  const isWxLogin = getLoginType() === loginType.Wx;
  if (config.value.isNeedQQ && isWxLogin) {
    qq = qqBindingStore.getQQNumberByScene(config.value.qqBindingScene);
    console.log('[recharge-lottery] 正在检查微信账号是否绑定 Q 号', config.value.qqBindingScene, qq);
    if (!qq) {
      toast?.(NOT_BINDING_QQ_ERROR_MSG);
      $bus?.$emit(COMPONENT_EVENT.NeedQQBinding, instance?.proxy);
      return false;
    }
  }

  return true;
};

// 获取区服角色信息
const partitionStore = store.usePartitionRoleStore();
const openID = utils.getLoginUserOpenID();
/** 调用后台执行抽奖 */
const lottery = async (): Promise<RewardInfo | undefined > => {
  const boundPartitionAndRole = partitionStore.getPartitionRole(config.value.packageName, openID);
  const result = await executeLotteryTask(config.value.taskGroupID, lotteryTaskID.value, {
    qq: Number(qq),
    pkgName: props.config.packageName || '',
    serverId: boundPartitionAndRole.partition?.id || '',
    roleId: boundPartitionAndRole.role?.id || '',
    roleName: boundPartitionAndRole.role?.name || '',
  });
  return result;
};

/** 抽奖失败 */
const handleLotteryFailed = () => {
  lotteryPoolRef.value?.endLottery();
  isDoingLottery = false;
  void getLotteryTaskGroupAndLotteryNum();
};

/**
 * 处理抽奖成功, 展示抽奖成功弹窗
 * @param rewardInfo 奖品信息
 */
const handleLotterySuccess = async (rewardInfo?: RewardInfo) => {
  // 若不存在抽中的奖励，终止抽奖动画
  if (!rewardInfo) {
    handleLotteryFailed();
    toast?.(LOTTERY_FAIL_MSG);
    $bus?.$emit(COMPONENT_EVENT.CompleteLottery, instance?.proxy);
    return;
  }

  // 暂停抽奖动画
  lotteryPoolRef.value?.stopLottery(
    Number(rewardInfo.id),
    // 抽奖动画暂停回调函数
    () => {
      // 重新拉取任务集和抽奖数
      void getLotteryTaskGroupAndLotteryNum();

      console.log('[recharge-lottery] 打开抽奖成功弹窗', rewardInfo);
      winningPrize.value = rewardInfo;
      isLotterySuccessDialogVisible.value = true;
      lotteryPoolRef.value?.endLottery();
      $bus?.$emit(COMPONENT_EVENT.CompleteLottery, instance?.proxy);
    },
  );
};

/** 处理抽奖 */
const handleLottery = debounce(async () => {
  // 当奖池 dom 未加载完毕时，不进行操作
  if (!lotteryPoolRef.value) {
    toast?.(DEFAULT_ERROR_MSG);
    return;
  }

  // 正在抽奖或正在播放抽奖动画，则不进行处理
  if (isDoingLottery || isLotteryAnimatePlaying.value) return;
  isDoingLottery = true;

  try {
    // 1. 抽奖前置处理
    const isValidLottery = await handleLotteryBefore();
    if (!isValidLottery) {
      isDoingLottery = false;
      return;
    }

    // 2. 抽奖
    lotteryPoolRef.value?.startLottery();
    await sleep(1000); // 至少播放 1s 的抽奖动效
    const rewardInfo = await lottery();

    // 3. 处理抽奖成功动画播放
    await handleLotterySuccess(rewardInfo);
  } catch (err) {
    console.error('[handleLottery] 抽奖失败', err);
    toast?.(DEFAULT_ERROR_MSG);
  } finally {
    isDoingLottery = false;
  }
}, 100);

const getLotteryNum = async () => {
  const yybPoint = await getYybPoint(config.value.pointGroupID, config.value.id);
  lotteryNum.value = yybPoint;
};

defineExpose({
  [COMPONENT_METHODS.DoLottery]: handleLottery,
  [COMPONENT_METHODS.GetLotteryNum]: getLotteryNum,
});
</script>

<style lang="scss" scoped>
.recharge-lottery {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 3.27rem;
}
</style>
