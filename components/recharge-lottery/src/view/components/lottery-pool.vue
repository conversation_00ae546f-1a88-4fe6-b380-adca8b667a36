<template>
  <div
    class="lottery-pool"
  >
    <div class="lottery-pool-content">
      <reward
        v-for="reward in rewards"
        :key="reward.id"
        :reward-info="reward"
        :is-selecting="reward.id === currentReward"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

import { useLotteryAnimateHook } from '../../composables/use-lottery-animate';
import { LotteryAnimateStatus } from '../../constant/lottery';
import type { RewardInfo } from '../../types/lottery';
import Reward from './lottery-reward.vue';

const props = defineProps<{
  /** 奖池信息 */
  rewards: RewardInfo[];
}>();

/** 奖品抽奖顺序 */
const rewardsInLotteryOrder = computed(() => props.rewards.map(reward => reward.id));

const { startLottery, stopLottery, endLottery, currentReward, animateStatus } = useLotteryAnimateHook({
  rewardIDsInOrder: rewardsInLotteryOrder,
  needManualChangeEndStatus: true,
});

defineExpose({
  animateStatus,
  startLottery,
  stopLottery,
  endLottery,
  setEndAnimateStatus: () => {
    animateStatus.value = LotteryAnimateStatus.End;
  },
});
</script>

<style lang="scss" scoped>
.lottery-pool {
  width: 100%;

  &-content {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
  }
}
</style>
