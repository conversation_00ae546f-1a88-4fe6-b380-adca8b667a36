<template>
  <div
    class="reward"
    :style="{
      backgroundImage: `url(${rewardBg})`
    }"
  >
    <div class="gift-info">
      <img
        class="gift-info__icon"
        :src="rewardInfo.icon"
      >
      <div class="gift-info__name">
        {{ rewardInfo.title }}
      </div>
    </div>
    <div
      v-if="isLotteryWining"
      :style="{
        backgroundImage: `url(${rewardInfo.winningBg})`
      }"
      class="winning"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';

import { LotteryAnimateStatus, RewardStatus } from '../../constant/lottery';
import type { RewardInfo } from '../../types/lottery';

const props = defineProps<{
  /** 奖品信息 */
  rewardInfo: RewardInfo;
  /** 抽奖动画状态 */
  lotteryAnimateStatus: LotteryAnimateStatus;
  /** 是否正在被选中 */
  isSelecting: boolean;
}>();

/** 是否正在抽奖中 */
const isLotterySelecting = computed(() => props.lotteryAnimateStatus !== LotteryAnimateStatus.End && props.isSelecting);
/** 是否已抽中 */
const isLotteryWining = computed(() => props
  .rewardInfo.status === RewardStatus.Winning);

const rewardBg = computed(() => {
  if (isLotterySelecting.value) return props.rewardInfo.lotteryBg;
  return props.rewardInfo.bg;
});
</script>

<style lang="scss" scoped>
.reward {
  margin-bottom: 0.11rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 1.03rem;
  height: 1.03rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .gift-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &__icon {
      height: 0.42rem;
      width: 0.42rem;
      object-fit: contain;
    }

    &__name {
      color: #bdccdf;
      font-weight: bold;
      font-size: 0.12rem;
      margin-top: 0.04rem;
    }
  }

  .winning {
     position: absolute;
     background-size: 100% 100%;
     background-repeat: no-repeat;
     width: 100%;
     height: 100%;
     top: 0;
     left: 0;
  }
}
</style>
