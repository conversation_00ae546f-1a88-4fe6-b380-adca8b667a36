<template>
  <div
    class="lottery-success-dialog-mask"
    @click="() => emits('close')"
  >
    <div
      class="lottery-success-dialog"
      :style="{
        backgroundImage: `url(${dialogBg})`
      }"
    >
      <close-svg />
      <i
        class="svg_icon icon_cancel"
        @click="() => emits('close')"
      >
        <svg class="svg_icon_cancel"><use xlink:href="#svg_icon_cancel" /></svg>
      </i>
      <div class="title">
        恭喜获得<span>{{ winningPrize.title }}</span>
      </div>
      <img
        class="icon"
        :src="winningPrize.icon"
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

import { RewardInfo } from '../../types/lottery';
import CloseSvg from './close-svg.vue';

const emits = defineEmits(['close']);

defineProps<{
  winningPrize: RewardInfo;
  dialogBg: string;
}>();
</script>

<style lang="scss">
.lottery-success-dialog-mask {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.8);
  z-index: 999;

  .lottery-success-dialog {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 3.07rem;
    height: 1.50rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;

     .title {
       font-weight: bold;
       font-size: 0.22rem;
       color: #ecca9c;
       margin-bottom: 0.1rem;
     }

     .icon {
       height: 0.6rem;
       width: 0.6rem;
     }

     .icon_cancel {
       position: absolute;
       top: 0.05rem;
       right: 0.05rem;
       width: 0.24rem;
       height: 0.24rem;
       color: #4c5d76;
         &.svg_icon svg {
           width: 100%;
           height: 100%;
        }
     }
  }
}
</style>
