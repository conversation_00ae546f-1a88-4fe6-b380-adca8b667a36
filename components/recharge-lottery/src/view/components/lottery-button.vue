<template>
  <div
    class="lottery-button"
    :style="btnStyle"
    @click="handleLotteryClick"
  >
    <div
      v-if="!canLottery"
      class="lottery-button__text"
    >
      {{ btnText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';
import {
  debounce,
} from '@tencent/mole-utils-lib';

import { DISABLE_LOTTERY_TIP_TEXT, HAS_LOTTERY_OUT_TIP_TEXT, UserLotteryStatus } from '../../constant/lottery';

const props = defineProps<{
  /** 用户抽奖状态 */
  userLotteryStatus: UserLotteryStatus;
  /** 用户距离下一档位价值 */
  nextLevelRecharge: number;
  /** 抽奖按钮背景图 */
  lotteryBtnBg: string;
}>();

const { toast, isLogin, openLogin, loginReady } = hooks.useMokaInject();

const emits = defineEmits<(e: 'startLottery' | 'lotteryFailed') => void>();

const lotteryStatus = computed(() => props.userLotteryStatus);

/** 按钮文案 */
const btnText = computed(() => {
  if (lotteryStatus.value === UserLotteryStatus.DisableLottery && props.nextLevelRecharge > 0) return `充${props.nextLevelRecharge}元再抽一次`;
  if (lotteryStatus.value === UserLotteryStatus.HasLotteryOut) return '您已获得所有奖励';
  return '立即抽奖';
});

const btnStyle = computed(() => ({ backgroundImage: `url(${props.lotteryBtnBg})`, fontSize: lotteryStatus.value === UserLotteryStatus.CanLottery ? '0.16rem' : '0.12rem' }));

/** 点击抽奖按钮 */
const handleLotteryClick = debounce(async () => {
  await loginReady;
  if (!isLogin.value) {
    openLogin?.();
    return;
  }

  // 不可抽奖
  if (lotteryStatus.value === UserLotteryStatus.DisableLottery) {
    toast?.(DISABLE_LOTTERY_TIP_TEXT);
    return;
  }

  // 抽完所有奖品
  if (lotteryStatus.value === UserLotteryStatus.HasLotteryOut) {
    toast?.(HAS_LOTTERY_OUT_TIP_TEXT);
    return;
  }

  // 开始抽奖
  emits('startLottery');
}, 200);
</script>

<style lang="scss" scoped>
.lottery-button {
  width: 1.98rem;
  height: 0.47rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &__text {
    color: #9e5601;
    font-weight: bold;
  }
}
</style>
