import type { RewardInfo } from '../types/lottery';
import { RewardStatus } from './lottery';

export const REWARDS: RewardInfo[] = [
  {
    id: 1,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '5Q币',
    status: RewardStatus.Winning,
    giftShowIndex: 1,
  },
  {
    id: 2,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '50Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 2,
  },
  {
    id: 3,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '20Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 3,
  },
  {
    id: 4,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '100Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 4,
  },
  {
    id: 5,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '500Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 5,
  },
  {
    id: 6,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '50Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 6,
  },
  {
    id: 7,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '5Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 7,
  },
  {
    id: 8,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '100Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 8,
  },
  {
    id: 9,
    icon: 'https://cdn.yyb.gtimg.com/wupload/xy/task_center/XZnujVrp.png',
    title: '20Q币',
    status: RewardStatus.NotWinning,
    giftShowIndex: 9,
  },
];

/** 默认奖品背景 */
export const DEFAULT_BG = 'https://yyb.qpic.cn/moka-imgs/1741957245792-ckm12bfhc8n.png';
/** 默认抽奖中背景 */
export const DEFAULT_LOTTERY_BG = 'https://yyb.qpic.cn/moka-imgs/1741957092497-93vkuhbrtt.png';
/** 默认中奖背景 */
export const DEFAULT_WINNING_BG = 'https://yyb.qpic.cn/moka-imgs/1742280785217-w9suly8ll1b.png';
