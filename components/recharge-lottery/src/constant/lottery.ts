/** 不可抽奖 */
export const DISABLE_LOTTERY_TIP_TEXT = '抽奖机会不足哦~';

/** 已抽完所有奖品 */
export const HAS_LOTTERY_OUT_TIP_TEXT = '您已获得所有奖励~';

/** 默认错误提示文案 */
export const DEFAULT_ERROR_MSG = '系统繁忙，请稍候再试';

/** 没有绑定QQ提示文案 */
export const NOT_BINDING_QQ_ERROR_MSG = '请先绑定QQ账号，再参与抽奖噢~';

/** 抽奖失败提示文案 */
export const LOTTERY_FAIL_MSG = '抽奖失败，请联系客服处理';

/** 请求成功返回码 */
export const REQUEST_SUCCESS_CODE = 0;

/** 组件类型 */
export const COMPONENT_TYPE = 'recharge-lottery';

/** 抽奖动画状态 */
export enum LotteryAnimateStatus {
  /** 开始中，逐渐加速状态 */
  Starting = 1,
  /** 暂停中，逐渐减速状态 */
  Stopping = 2,
  /** 停止状态 */
  End = 3,
}

/** 用户抽奖状态 */
export enum UserLotteryStatus {
  /** 可抽奖 */
  CanLottery = 1,
  /** 不可抽奖 */
  DisableLottery = 2,
  /** 已抽完所有奖品 */
  HasLotteryOut = 3,
}

/** 奖品状态 */
export enum RewardStatus {
  /** 未中奖 */
  NotWinning = 1,
  /** 已中奖 */
  Winning = 2,
}

/** 任务状态 */
export enum TaskGiftStatus {
  /** 未发放 */
  NotSend = 0,
  /** 发放失败 */
  SendFail = 1,
  /** 发放成功 */
  SendSuccess = 2,
}

/** 任务状态 */
export enum TaskStatus {
  /** 未知状态（占位使用）*/
  Unknown = 0,
  /** 未通过任务条件 */
  NotReachConditon = 1,
  /** 可领取状态 */
  CanReceive = 2,
  /** 已完成领取（账号维度）*/
  HasReceived = 3,
  /** 达到设备维度限量 */
  ReachDeviceLimit = 4,
  /** 达到账号维度限量 */
  ReachUserLimit = 5,
  /** 被风控打击 */
  HitRiskControl = 6,
}
