import {
  computed,
  ComputedRef,
  ref,
  watch,
} from 'vue';

import { LotteryAnimateStatus } from '../constant/lottery';

/** 开始抽奖动画速度时间 */
const startAnimateSpeedTime = 200;
/** 抽奖动画最大速度时间 */
const maxAnimateSpeedTime = 70;
/** 抽奖结束动画播放最大时间 */
const finishAnimateMaxTime = 2000;

/**
 * 抽奖动画 hook
 * @param options 抽奖动画选项
 * @param options.rewardIDsInOrder 奖品 ID 数组，需要按抽奖轮播顺序排序
 * @param options.finishedCallback 抽奖动画结束回调
 * @param options.endCallback 抽奖动画终止回调
 */
export function useLotteryAnimateHook(options: {
  /** 奖品 ID 数组，按抽奖轮播顺序排序 */
  rewardIDsInOrder: ComputedRef<(number | string)[]>;
  /** 抽奖终止回调 */
  endCallback?: () => Promise<void> | void;
  /** 是否需要手动变更结束状态 */
  needManualChangeEndStatus?: boolean;
}) {
  const { rewardIDsInOrder, endCallback, needManualChangeEndStatus } = options;
  /** 当前选中的奖品索引下标 */
  const currentIndex = ref(-1);
  /** 抽奖状态 */
  const animateStatus = ref(LotteryAnimateStatus.End);
  /** 中奖索引 */
  let winningIndex = -1;
  /** 当前抽奖动画速度时间，下一帧动画 */
  let currentSpeedTime = startAnimateSpeedTime;
  /** 开始停止动画的时间 */
  let stopTime: number;
  /** 动画播放定时器 */
  let timeout: NodeJS.Timeout;
  /** 抽奖动画结束回调 */
  let finishedCallback: () => Promise<void> | void;
  /** 当前选中的奖品 */
  const currentReward = computed(() => rewardIDsInOrder.value[currentIndex.value]);

  /** 开始抽奖 */
  function startLottery() {
    animateStatus.value = LotteryAnimateStatus.Starting;
    playLotteryAnimate();
  }

  /**
   * 停止抽奖
   * @param rewardID 中奖 ID
   */
  function stopLottery(rewardID: number | string, callback?: () => Promise<void> | void) {
    finishedCallback = callback ?? (() => {});
    winningIndex = rewardIDsInOrder.value.findIndex(reward => String(reward) === String(rewardID));
    // 未找到对应中奖信息
    if (winningIndex === -1) {
      endLottery();
      return;
    }

    animateStatus.value = LotteryAnimateStatus.Stopping;
    stopTime = new Date().getTime();
  }

  /** 终止抽奖 */
  function endLottery() {
    animateStatus.value = LotteryAnimateStatus.End;
    if (timeout) {
      clearTimeout(timeout);
    }

    currentSpeedTime = startAnimateSpeedTime;
    currentIndex.value = -1;
    void endCallback?.();
  }

  /** 播放抽奖动画 */
  function playLotteryAnimate() {
    timeout = setTimeout(() => {
      // 自增当前选中索引
      currentIndex.value = (currentIndex.value + 1) % rewardIDsInOrder.value.length;
      // 当前状态处于停止抽奖，且到达停止抽奖时间和滚动到选中的奖品索引
      if (
        animateStatus.value === LotteryAnimateStatus.Stopping
        && winningIndex === currentIndex.value
        && Date.now() - stopTime > finishAnimateMaxTime
      ) {
        // 清除定时器
        if (timeout) {
          clearTimeout(timeout);
        }

        // 延时 300 ms 后结束抽奖
        setTimeout(() => {
          if (!needManualChangeEndStatus) {
            animateStatus.value = LotteryAnimateStatus.End;
          }

          void finishedCallback?.();
        }, 300);

        return;
      }

      changeLotteryAnimateSpeed();
      playLotteryAnimate();
    }, currentSpeedTime);
  }

  /** 改变抽奖速度 */
  function changeLotteryAnimateSpeed() {
    // 暂停中状态，抽奖动画减速
    if (animateStatus.value === LotteryAnimateStatus.Stopping) {
      currentSpeedTime += maxAnimateSpeedTime / 5;
      return;
    }

    if (currentSpeedTime <= maxAnimateSpeedTime) {
      // 到达最大速度后保持最大速度
      currentSpeedTime = maxAnimateSpeedTime;
    } else {
      // 加速，每次加速最大速度的 10%
      currentSpeedTime -= maxAnimateSpeedTime / 10;
    }
  }

  /** 重置 */
  function reset() {
    animateStatus.value = LotteryAnimateStatus.End;
    currentIndex.value = -1;
    currentSpeedTime = startAnimateSpeedTime;
    winningIndex = -1;
  }

  watch(rewardIDsInOrder.value, () => {
    reset();
  });

  return {
    startLottery,
    stopLottery,
    endLottery,
    reset,
    animateStatus,
    currentIndex,
    currentReward,
  };
}
