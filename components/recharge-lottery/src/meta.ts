import type { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/** 活动积分组的数据表 */
const pointGroupWujiUrlByEnv = {
  test: 'https://wujiapi.woa.com/x/api/wuji_cache/object?appid=activityPageEdit_test&schemaid=t_point_group&schemakey=023e91e2d5c34910935cefab93db4713&size=total',
  prod: 'https://wujiapi.woa.com/x/api/wuji_cache/object?appid=activityPageEdit&schemaid=t_point_group&schemakey=8fe268d6e8f94b82aa236e79589d656e&size=total',
};

const env = location.host === 'moka.woa.com' ? 'prod' : 'test';
/**
 * 组件表单配置
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      lotteryRewardNum: {
        type: 'string',
        title: '奖品数量（用于编辑器展示）',
        'x-decorator': 'FormItem',
        'x-component': 'InputNumber',
        default: 9,
      },
      taskGroupID: {
        // TODO: 无疆表的读接口不支持跨域调用，暂使用 TextLink
        type: 'string',
        title: '任务集',
        'x-decorator': 'FormItem',
        'x-component': 'TextLink',
        'x-component-props': {
          placeholder: '请输入任务集ID',
          linkTitle: '任务中心',
          linkStrOrFunc() {
            return `https://wujiang.woa.com/xy/app/${env}/task_center/task_group`;
          },
        },
        'x-validator': [
          { required: true, message: '请输入任务集ID' },
        ],
      },
      pointGroupID: {
        type: 'string',
        title: '活动积分组ID',
        'x-decorator': 'FormItem',
        'x-component': 'Select',
        'x-component-props': {
          filterable: true,
          placeholder: '请选择',
        },
        'x-decorator-props': {
          labelWrap: true,
          extra: '必须和抽奖次数绑定的同一个活动积分组ID，用于展示是否可抽奖的状态判断',
        },
        'x-reactions': [
          async (field) => {
            const curField = field;
            const url = `${pointGroupWujiUrlByEnv[env]}&${encodeURIComponent(`id="${curField.value || ''}"`)}`;
            const resp = await fetch(url);
            const { data } = await resp.json();

            curField.dataSource = data.map(item => ({
              label: `${item.id}-${item.name}`,
              value: item.id,
            }));
          },
        ],
        'x-validator': [
          { required: true, message: '请输入活动积分组ID' },
        ],
      },
      isNeedQQ: {
        type: 'boolean',
        title: '是否需要前置校验QQ绑定',
        default: true,
        'x-decorator-props': {
          labelWidth: 'auto',
          labelWrap: true,
        },
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
      },
      qqBindingScene: {
        type: 'string',
        title: 'QQ绑定场景',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        default: 25,
        'x-reactions': [
          {
            dependencies: ['isNeedQQ'],
            when: '{{$deps[0]}}',
            fulfill: {
              state: {
                visible: true,
              },
            },
            otherwise: {
              state: {
                visible: false,
              },
            },
          },
        ],
      },
      isNeedPartitionAndRole: {
        type: 'boolean',
        title: '是否需要区服',
        default: true,
        'x-decorator-props': {
          labelWidth: 'auto',
          labelWrap: true,
        },
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
      },
      packageName: {
        type: 'string',
        title: '应用包名',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-reactions': [
          {
            dependencies: ['isNeedPartitionAndRole'],
            when: '{{$deps[0]}}',
            fulfill: {
              state: {
                visible: true,
              },
            },
            otherwise: {
              state: {
                visible: false,
              },
            },
          },
        ],
      },
      hasSelectOS: {
        type: 'boolean',
        title: '是否已选择系统',
        default: false,
        'x-decorator-props': {
          labelWidth: 'auto',
          labelWrap: true,
        },
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
      },
      notWinningBg: {
        type: 'string',
        title: '未中奖背景图',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-component-props': {
          drag: true,
          lockImgNorm: {
            width: 0.5,
            height: 0.5,
          },
        },
        'x-validator': [
          { required: true, message: '请上传背景图' },
        ],
      },
      lotteryBg: {
        type: 'string',
        title: '抽奖中背景图',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-component-props': {
          drag: true,
          lockImgNorm: {
            width: 0.5,
            height: 0.5,
          },
        },
        'x-validator': [
          { required: true, message: '请上传背景图' },
        ],
      },
      winningBg: {
        type: 'string',
        title: '抽中奖背景图',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-component-props': {
          drag: true,
          lockImgNorm: {
            width: 0.5,
            height: 0.5,
          },
        },
        'x-validator': [
          { required: true, message: '请上传背景图' },
        ],
      },
      lotteryResultBg: {
        type: 'string',
        title: '中奖弹窗背景图',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-component-props': {
          drag: true,
          lockImgNorm: {
            width: 0.5,
            height: 0.5,
          },
        },
        'x-validator': [
          { required: true, message: '请上传背景图' },
        ],
      },
      lotteryBtnBg: {
        type: 'string',
        title: '抽奖按钮背景图',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-component-props': {
          drag: true,
          lockImgNorm: {
            width: 0.5,
            height: 0.5,
          },
        },
        'x-validator': [
          { required: true, message: '请上传背景图' },
        ],
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [{
      label: '抽奖完成',
      value: 'rechargeLottery:completeLottery',
      desc: '抽奖完成了触发其他组件变更',
    }, {
      label: '需要拉起QQ绑定',
      value: 'rechargeLottery:needQQBinding',
      desc: '需要拉起QQ绑定触发其他组件变更',
    }],
    /** 组件内部方法，提供外部调用 */
    methods: [
      {
        label: '抽奖',
        value: 'doLottery',
        desc: '发起抽奖事件',
      },
      {
        label: '更新抽奖次数',
        value: 'getLotteryNum',
        desc: '重新查询抽奖次数事件',
      },
    ],
  },
};

export default meta;
