import { TaskGiftStatus } from '../constant/lottery';

/**
 * 任务执行返回的礼包结果
 */
export interface TaskGiftResult {
  /** 礼包 id */
  giftId: number;
  /** 礼包发放状态 */
  sendStatus: TaskGiftStatus;
  /** 礼包展示顺序 */
  giftShowIndex: number;
  /** 是否本次领取 */
  isLastOrder: boolean;
  /** 礼包发放返回码 */
  retCode: number;
  /** 礼包发放返回错误信息 */
  errMsg: string;
  /** 礼包标题 */
  title: string;
  /** 礼包 icon */
  icon: string;
}

/**
 * 任务收集的用户参数（区服、qq号、地址等）
 */
export interface TaskUserParams {
  /** qq 号 */
  qq: number;
  /** 区服 id */
  serverId: string;
  /** 角色 id */
  roleId: string;
  /** 角色名 */
  roleName: string;
  /** 平台 id 0-ios 1-安卓 */
  platId: string;
  /** 包名 */
  pkgName: string;
  /** 渠道号 */
  channelId: string;
  /** 地址 id */
  addressId: string;
}
