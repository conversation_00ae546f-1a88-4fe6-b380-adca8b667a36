import { actExecForwardRequester } from '@tencent/moka-data-core';
import { executeTask, getTaskGroupInfo } from '@tencent/moka-task-lib';
import { safeJsonStringify } from '@tencent/mole-utils-lib';

import { COMPONENT_TYPE, REQUEST_SUCCESS_CODE, RewardStatus, TaskGiftStatus } from '../constant/lottery';
import type { RewardInfo } from '../types/lottery';
import { TaskGiftResult, TaskUserParams } from './type';

/**
 * 获取应用宝积分
 * @param pointGroupID 积分组 id
 * @param componentID 组件 id
 */
export async function getYybPoint(pointGroupID: string, componentID: string) {
  try {
    console.log(`[recharge-lottery] getYybPoint start, pointGroupID: ${pointGroupID}`);
    const { code, tip, body } = await actExecForwardRequester.request<unknown, { user_point: string }>({
      activity_iid: '',
      componentID,
      componentType: COMPONENT_TYPE,
      invocation: {
        name: '/trpc.component_plat.yybpoint.YybPoint/Query',
        data: {
          group_id: pointGroupID,
        },
      },
    });

    if (code !== REQUEST_SUCCESS_CODE) {
      console.error(`[recharge-lottery] getYybPoint tip: ${tip}[${code}]`);
      return 0;
    }

    console.log(`[recharge-lottery] getYybPoint success, yybPoint: ${body?.data?.user_point}`);

    return parseInt(body?.data?.user_point || '0', 10);
  } catch (e) {
    console.error('[recharge-lottery] getYybPoint error', e);
    return 0;
  }
}

/**
 * 获取抽奖任务集
 * @param taskGroupId 任务集 id
 */
export async function getLotteryTaskGroup(taskGroupId: string) {
  console.log(`[recharge-lottery] getLotteryTaskGroup start, taskGroupId: ${taskGroupId}`);
  const tasks = await getTaskGroupInfo(taskGroupId);
  const lotteryTask = tasks[0];
  if (!lotteryTask) {
    console.error('[recharge-lottery] getLotteryTaskGroup 不存在抽奖任务');
    return {
      gifts: [],
      nextLevelRecharge: 0,
    };
  }

  const { id, status, singleAttrConfig } = lotteryTask;
  const { actualValue, expectedValue } = singleAttrConfig;

  // 距离下一档位的充值流水，actualValue 为实际充值流水，处理后台解析失败兜底为 0
  const nextLevelRecharge = expectedValue - (isFinite(Number(actualValue)) ? actualValue : 0);

  const lotteryTaskGroupData = {
    taskID: id,
    status,
    gifts: lotteryTask.gifts.map(formatGift).sort((a, b) => a.giftShowIndex - b.giftShowIndex),
    nextLevelRecharge: nextLevelRecharge > 0 ? Math.floor((nextLevelRecharge / 100) * 100) / 100 : 0,
  };

  console.log(`[recharge-lottery] getLotteryTaskGroup success, lotteryTaskGroupData: ${safeJsonStringify(lotteryTaskGroupData)}`);

  return lotteryTaskGroupData;
}

/**
 * 执行抽奖任务
 * @param taskGroupId 任务集 id
 * @param taskId 单任务 id
 * @param taskUserParams 任务收集的用户参数（区服、qq号、地址等）
 */
export async function executeLotteryTask(taskGroupId: string, taskId: string, taskUserParams: Partial<TaskUserParams>) {
  console.log(`[recharge-lottery] executeLotteryTask start, taskGroupId: ${taskGroupId}, taskId: ${taskId}, taskUserParams: ${safeJsonStringify(taskUserParams)}`);
  const resp = await executeTask(taskGroupId, taskId, taskUserParams);
  const { code, msg, data } = resp;
  if (code !== REQUEST_SUCCESS_CODE || !data?.gifts[0]) {
    console.error(`[recharge-lottery] executeLotteryTask error, code: ${code}, msg: ${msg}`);
    return;
  }

  console.log(`[recharge-lottery] executeLotteryTask success, resp: ${safeJsonStringify(data.gifts[0])}`);

  return formatGift(data.gifts[0]);
}

/**
 * 格式化礼包信息
 * @param gift 礼包信息
 */
function formatGift(gift: TaskGiftResult): RewardInfo {
  const { title, icon, sendStatus, giftId, giftShowIndex } = gift;
  return {
    id: giftId || giftShowIndex, // 由于积分没有 giftID, 使用 giftShowIndex 作为 id
    title,
    icon,
    status: sendStatus === TaskGiftStatus.SendSuccess ? RewardStatus.Winning : RewardStatus.NotWinning,
    giftShowIndex,
  };
}
