<script setup lang="ts">
import Example from '../src/view/index.vue';

const config = {
  /** 组件 id */
  id: 'recharge-lottery_1234',

  /** 任务集 id */
  taskGroupID: 'g3gyd9kvv7',
  /** 积分 id */
  pointGroupID: 'z1ohf00vtl',
  /** 未中奖背景图 */
  notWinningBg: 'https://yyb.qpic.cn/moka-imgs/1741957245792-ckm12bfhc8n.png',
  /** 抽奖中背景图背景图 */
  lotteryBg: 'https://yyb.qpic.cn/moka-imgs/1741957092497-93vkuhbrtt.png',
  /** 抽中奖的背景图 */
  winningBg: 'https://yyb.qpic.cn/moka-imgs/1742280785217-w9suly8ll1b.png',
  /** 中奖弹窗背景图 */
  lotteryResultBg: 'https://yyb.qpic.cn/moka-imgs/1742267575955-q60ng9zhlx.png',
  /** 抽奖按钮背景图 */
  lotteryBtnBg: 'https://yyb.qpic.cn/moka-imgs/1741943247607-9aiprp5jzno.png',
  /** 奖品数量（仅限于编辑器使用） */
  lotteryRewardNum: 9,
}
</script>

<template>
  <Example :config="config" />
</template>
