<template>
  <div
    v-if="isDebug"
    class="layout"
  >
    <div
      @click="() => openLogin()"
    >
      登录测试
    </div>
    <div
      @click="() => openVipActivate()"
    >
      激活测试
    </div>
    <div
      @click="() => openAuthGame()"
    >
      游戏授权
    </div>
  </div>
</template>

<script setup lang="ts">
import '@tencent/yyb-mall-components/dist/style.css';
import { inject } from 'vue';
import { utils } from '@tencent/moka-ui-domain';
import { isYSDK } from '@tencent/mole-utils-lib';
import { initVipLoginStore, LoginUseCase, ReqVersion } from '@tencent/yyb-mall-components';

import { COMPONENT_METHODS } from '../constant';

/** 组件配置 */
interface ComponentConfig {
  id: string;
  appId: string;
  sourceId: string;
  otherAppIdListStr?: string;
}

const props = defineProps<{
  config: ComponentConfig;
}>();

initVipLoginStore({
  sourceId: props.config.sourceId,
  otherAppIdListStr: props.config.otherAppIdListStr,
  reqVersion: ReqVersion.Fission,
});

const loginUseCase = new LoginUseCase(isYSDK(navigator.userAgent) ? '' : props.config.appId);

// 调试DEBUG
const DEBUG_KEY = 'debug';
const isDebug = utils.fetchQueryParam(DEBUG_KEY) || '';
const {
  checkLogin,
  openLogin,
  getUserInfo,
  openVipActivate,
  openAuthGame,
  sync,
  tryActivate,
  tryAuthGame,
  tryLoginAndAuthGameAndActivate,
} = loginUseCase;
// 初始化插入全局变量
window.vipCommonLogic = {
  checkLogin: checkLogin.bind(loginUseCase),
  getUserInfo: getUserInfo.bind(loginUseCase),
  openLogin: openLogin.bind(loginUseCase),
  openVipActivate: openVipActivate.bind(loginUseCase),
  openAuthGame: openAuthGame.bind(loginUseCase),
  tryActivate: tryActivate.bind(loginUseCase),
  tryAuthGame: tryAuthGame.bind(loginUseCase),
  sync: sync.bind(loginUseCase),
  tryLoginAndAuthGameAndActivate: tryLoginAndAuthGameAndActivate.bind(loginUseCase),
};

// 组件抛出事件
defineExpose({
  [COMPONENT_METHODS.OpenAuthGame]: openAuthGame.bind(loginUseCase),
  [COMPONENT_METHODS.OpenVipActivate]: openVipActivate.bind(loginUseCase),
  [COMPONENT_METHODS.TryActivate]: tryActivate.bind(loginUseCase),
  [COMPONENT_METHODS.TryAuthGame]: tryAuthGame.bind(loginUseCase),
});

const isLogin = await loginUseCase.checkLogin();
// FIX兼容插件逻辑
const commonStore = inject<{isLogin: boolean; $patch: (op: any) => void}>('store');
commonStore?.$patch({
  isLogin,
});

void loginUseCase.sync();
</script>
