import { COMPONENT_METHODS } from './constant';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [],
  /** 组件内部方法，提供外部调用 */
  methods: [
    {
      label: '激活',
      value: COMPONENT_METHODS.TryActivate,
      desc: '',
    },
    {
      label: '授权',
      value: COMPONENT_METHODS.TryAuthGame,
      desc: '',
    },
    {
      label: '拉起激活弹窗',
      value: COMPONENT_METHODS.OpenVipActivate,
      desc: '',
    },
    {
      label: '拉起授权弹窗',
      value: COMPONENT_METHODS.OpenAuthGame,
      desc: '',
    },
  ],
};
