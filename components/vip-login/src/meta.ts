import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        appId: {
          type: 'string',
          title: '游戏配置',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'AppSelect',
          'x-component-props': {
            test: 10,
            bindType: 'appId',
          },
        },
        otherAppIdListStr: {
          type: 'string',
          title: '授权游戏列表',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-decorator-props': {
            labelWrap: true,
            extra: '多游戏用英文逗号分隔',
            tooltip: '和 [游戏配置] 配的游戏取并集',
          },
          'x-validator': [(value: string, rule: any, ctx: any) => {
            // 只允许出现数字和英文符号的校验
            const regex = /^[0-9,]+$/;

            if (regex.test(value) || !value) {
              ctx.field.setSelfErrors('');
            } else {
              ctx.field.setSelfErrors('只允许输入数字和英文逗号');
            }
          }],
        },
        sourceId: {
          type: 'string',
          title: '场景ID(sourceId)',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
