import { utils } from '@tencent/moka-ui-domain';

import { DEFAULT_YYB_AVATAR } from '../constant';
import type { BoostRecord } from '../types';

const prefix = 'fission_boost_new_records_popup';
export function getBoostNewRecordsShowConditions(boostRecords: BoostRecord[]) {
  const storageKey = `${prefix}${utils.getLoginUserOpenID()}`;
  const showedLastOrderID = localStorage.getItem(storageKey);
  // 根据缓存判断积分数量
  let points = 0;
  const boosterRecords: string[] = [];
  if (!showedLastOrderID || showedLastOrderID !== boostRecords[0]?.order_id) {
    for (const record of boostRecords) {
      if (record.order_id === showedLastOrderID) break;
      points += Number(record.value);
      boosterRecords.push(record.avatar ?? DEFAULT_YYB_AVATAR);
    }

    localStorage.setItem(storageKey, boostRecords[0]?.order_id);

    return {
      points,
      showBoostNewRecords: true,
      boosterRecords,
    };
  }

  return {
    points,
    showBoostNewRecords: false,
    boosterRecords,
  };
}
