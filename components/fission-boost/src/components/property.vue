<template>
  <div
    class="property"
  >
    <div class="left">
      <div class="icon-container">
        <img
          :src="propertyIcon"
          alt=""
        >
      </div>
      <div class="property-detail">
        <div class="threshold">
          活动获{{ property.threshold }}积分免费领
        </div>
        <div class="name">
          {{ property.obtainInfo?.property.instanceInfo.name }}
        </div>
      </div>
    </div>

    <div
      class="obtain-btn"
      :class="{
        disabled,
        received: property.obtainInfo?.isPersonalOutOfStock,
        cdk: showCDKClass,
      }"
      dt-eid="button"
      :dt-params="utils.getReportParamsStr({
        btn_title: buttonText || '',
        gift_id: property.obtainID,
        small_position: String(position),
      }, inMagic)"
      @click="emits('doObtain', property.obtainID)"
    >
      {{ buttonText }}
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { hooks, type ObtainProperty, store, utils } from '@tencent/moka-ui-domain';

const props = defineProps<{
  property: ObtainProperty;
  position: number;
}>();
const emits = defineEmits(['doObtain']);

const { inMagic } = hooks.useMokaInject();

const defaultPropertyIcon = 'https://yyb.qpic.cn/moka-imgs/1730295398540-n02eo3crut.png';
// 物品图片
const propertyIcon = computed(() => props.property.obtainInfo?.property.instanceInfo.pic_url
  || defaultPropertyIcon);

const fissionStore = store.useFissionBoostStore();
const reachedThreshold = computed(() => fissionStore.ladderPoints >= Number(props.property.threshold));
const buttonText = computed(() => {
  if (!reachedThreshold.value) {
    return '待解锁';
  }

  return props.property.obtainInfo?.buttonText;
});
const showCDKClass = computed(() => {
  const {
    isCDK,
    hasReceiveRecord,
  } = props.property.obtainInfo || {};
  return isCDK && hasReceiveRecord;
});
const disabled = computed(() => {
  const { isGlobalOutOfStock } = props.property.obtainInfo || {};
  return !reachedThreshold.value || isGlobalOutOfStock;
});
</script>
<style lang="scss" scoped>
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.property {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.08rem;

  .left {
    display: flex;
    .icon-container {
      width: 0.44rem;
      height: 0.44rem;
      border-radius: 0.1rem;
      background-color: rgba(255, 242, 226, 1);
      margin-right: 0.08rem;
      @include flex-center;

      img {
        width: 0.2871rem;
        height: 0.2871rem;
      }
    }

    .property-detail {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-family: Noto Sans CJK SC;
      text-align: left;

      .threshold {
        font-size: 0.14rem;
        font-weight: 500;
        line-height: 0.21rem;
        letter-spacing: -0.0034rem;
        color: #000;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 2.1rem;
      }

      .name {
        font-weight: 400;
        font-size: 0.12rem;
        line-height: 0.1776rem;
        letter-spacing: -0.0024rem;
        color: rgba(0, 0, 0, 0.45);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 2.1rem;
      }
    }
  }

  .obtain-btn {
    width: 0.52rem;
    height: 0.24rem;
    font-family: Noto Sans CJK SC;
    font-size: 0.12rem;
    font-weight: 500;
    line-height: 0.18rem;
    border-radius: 0.16rem;
    color: #fff;
    background-color: rgba(238, 60, 56, 1);
    @include flex-center;

    &.disabled {
      background-color: rgba(238, 60, 56, 0.08);
      color: rgba(238, 60, 56, 0.45);
    }

    &.received {
      background-color: transparent;
      color: rgba(0, 0, 0, 0.35);
    }
    &.cdk {
      color: #fff;
      background-color: rgba(238, 60, 56, 1);
    }
  }
}
</style>
