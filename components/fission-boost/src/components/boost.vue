<template>
  <div class="boost">
    <div
      v-for="(detail,idx) in boostDetails"
      :key="idx"
      class="box"
      dt-eid="head"
      :dt-params="utils.getReportParamsStr({
        btn_id: 'invite_btn',
      }, inMagic)"
      @click="onClickAvatar(detail.value)"
    >
      <img
        class="avatar"
        :src="detail.avatar || DEFAULT_AVATAR"
        alt=""
      >
      <div
        v-if="detail.value"
        class="points"
      >
        {{ formatPoints(Number(detail.value)) }}
      </div>
    </div>
    <div
      v-for="(detail,idx) in filledDefaultDetails"
      :key="idx"
      class="box"
      dt-eid="button"
      :dt-params="utils.getReportParamsStr({
        btn_title: '加号',
      }, inMagic)"
      @click="onClickAvatar()"
    >
      <img
        class="avatar"
        :src="detail.avatar"
        alt=""
      >
    </div>
    <van-icon
      dt-eid="button"
      :dt-params="utils.getReportParamsStr({
        btn_title: '查看更多',
      }, inMagic)"
      class="more"
      name="arrow"
      color="rgba(15, 15, 15, 0.45)"
      @click="showPopup"
    />
  </div>
</template>
<script lang="ts" setup>
import 'vant/es/icon/style';
import { Icon as VanIcon } from 'vant';
import { computed } from 'vue';
import { BoostEvent, DetailPopupType, hooks, utils } from '@tencent/moka-ui-domain';

import { DEFAULT_AVATAR } from '../constant';
import type { BoostRecord, ComponentConfig } from '../types';

const props = defineProps<{
  boostDetails: BoostRecord[];
  config: ComponentConfig;
}>();

const { inMagic, $bus } = hooks.useMokaInject();

const filledDefaultDetails = computed(() => {
  const originalArray = props.boostDetails;
  const targetLength = 6;

  if (originalArray.length < targetLength) {
    const diff = targetLength - originalArray.length;
    const filler = {
      avatar: props.config.defaultAvatar,
    };

    return Array(diff).fill(filler);
  }

  return [];
});

const formatPoints = (points: number) => {
  if (points >= 10000) {
    return `${points / 10000}万`;
  }

  return points;
};

const showPopup = () => {
  $bus?.$emit(BoostEvent.OpenDetailPopup, DetailPopupType.Points);
};

const onClickAvatar = (value?: string) => {
  if (value) {
    showPopup();
    return;
  }

  // 触发裂变分享按钮分享能力
  $bus?.$emit(BoostEvent.OpenFissionShare);
};
</script>
<style lang="scss" scoped>
.boost {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.box {
  position: relative;
}
.avatar {
  width: 0.38rem;
  height: 0.38rem;
  margin-right: 0.12rem;
  border-radius: 50%;
}
.points {
  position: absolute;
  bottom: -0.06rem;
  left: 0;
  min-width: 0.38rem;
  min-height: 0.14rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 231, 186, 1);
  border-radius: 1rem;
  font-family: YYB;
  font-size: 0.1rem;
  font-weight: 400;
  color: rgba(244, 131, 25, 1);
}
.more {
  margin-left: -0.12rem;
  font-size: 0.14rem;
}
</style>
