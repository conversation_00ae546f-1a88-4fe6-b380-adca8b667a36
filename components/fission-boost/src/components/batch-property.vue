<template>
  <div
    class="property-container"
    dt-eid="tab_page"
    dt-params="tab_title=免费礼包"
  >
    <property
      v-for="(property, idx) in properties"
      :key="property.obtainID"
      :property="property"
      :position="idx + 1"
      @do-obtain="batchObtainMap?.doSingleObtain"
    />
  </div>
  <div
    class="batch-obtain-btn"
    :class="{ disable: !canObtainProperties.length }"
    dt-eid="button"
    dt-params="btn_title=一键领"
    @click="batchObtainMap?.doBatchObtain"
  >
    一键领
  </div>
</template>
<script lang="ts" setup>
import { computed, Ref } from 'vue';
import { BoostEvent, hooks, type ObtainProperty } from '@tencent/moka-ui-domain';

import Property from './property.vue';

const { $bus } = hooks.useMokaInject();
let batchObtainMap: {
  properties: Ref<ObtainProperty[]>;
  canObtainProperties: Ref<ObtainProperty[]>;
  doBatchObtain: () => void;
  doSingleObtain: (id: string) => void;
};
$bus?.$emit(BoostEvent.GetFissionBatchObtainInfo, (options: typeof batchObtainMap) => {
  batchObtainMap = options;
});

const properties = computed(() => batchObtainMap?.properties?.value ?? []);
const canObtainProperties = computed(() => batchObtainMap?.canObtainProperties?.value ?? []);
</script>

<style lang="scss" scoped>
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.property-container {
  height: 2.4rem;
  overflow-y: scroll;
}
.batch-obtain-btn {
  margin-top: 24px;
  width: 3.27rem;
  height: 0.48rem;
  font-family: Noto Sans CJK SC;
  font-size: 0.16rem;
  font-weight: 500;
  line-height: 0.24rem;
  border-radius: 0.24rem;
  color: #fff;
  background-color: rgba(238, 60, 56, 1);
  @include flex-center;

  &.disable {
    background-color: rgba(238, 60, 56, 0.08);
    color: rgba(238, 60, 56, 0.45);
  }
}
</style>
