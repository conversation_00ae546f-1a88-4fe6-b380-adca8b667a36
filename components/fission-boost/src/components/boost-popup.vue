<template>
  <van-popup
    position="center"
    style="background: transparent;overflow: visible;"
    :close-on-click-overlay="false"
    dt-eid="pop"
    :dt-params="utils.getReportParamsStr({
      ...config.reportParam || {},
    }, inMagic)"
    :dt-cmd="`hold=${fissionStore.isReportHeld}`"
  >
    <div class="boost-popup">
      <div
        class="close"
        dt-eid="button"
        dt-params="btn_title=关闭"
        @click="close"
      />
      <div
        class="avatar"
        :class="{ 'multi-avatar': config.avatars.length > 1 }"
      >
        <img
          v-for="(avatar,idx) in showedAvatars"
          :key="idx"
          :src="avatar || DEFAULT_YYB_AVATAR"
          alt=""
        >
        <div
          v-if="config.avatars.length > maxShowAvatarNum"
          class="more-avatar"
        >
          <img
            :src="config.avatars[maxShowAvatarNum - 1]"
            alt=""
          >
          <div class="count">
            +{{ config.avatars.length - maxShowAvatarNum }}
          </div>
        </div>
      </div>
      <div class="title">
        {{ config.title }}
      </div>
      <div class="description">
        {{ config.description }}
      </div>
      <div class="points">
        {{ formatPoints(points) }}<span>积分</span>
      </div>
      <div
        class="confirm"
        dt-eid="button"
        :dt-params="`btn_title=${config.btnTitle}`"
        @click="confirm"
      >
        {{ config.btnTitle }}
      </div>
    </div>
  </van-popup>
</template>
<script lang="ts" setup>
import 'vant/es/popup/style';
import { Popup as VanPopup } from 'vant';
import { computed } from 'vue';
import { BoostPopupType, constants, hooks, store, utils } from '@tencent/moka-ui-domain';

import { doBoost } from '../api';
import { DEFAULT_YYB_AVATAR } from '../constant';
import { ComponentConfig } from '../types';

const props = defineProps<{
  config: ComponentConfig;
  boostPopupType: BoostPopupType;
  points: number;
  // 助力记录头像列表
  boosterRecords?: string[];
}>();

const { inMagic, $bus } = hooks.useMokaInject();
const { UPDATE_YYB_EVENT } = constants;

const yybIcon = 'https://yyb.qpic.cn/moka-imgs/1730360336658-2z6eikv4wm5.png';
// 最多展示头像数量
const maxShowAvatarNum = 3;
const config = computed(() => {
  const baseConfig = {
    btnTitle: '确认',
    title: '领取成功',
    description: '已到账',
    avatars: [yybIcon],
    reportParam: {},
  };
  switch (props.boostPopupType) {
    case BoostPopupType.Receive:
      return {
        ...baseConfig,
        title: '领取成功',
        description: '已到账',
        avatars: [yybIcon],
        reportParam: {
          mod_id: 'point_receive_succ_pop',
          mod_title: '积分领取成功弹窗',
        },
      };
    case BoostPopupType.Boost:
      return {
        ...baseConfig,
        title: '为你助力',
        description: '送你',
        avatars: props.boosterRecords ?? [],
        reportParam: {
          mod_id: 'assist_point_achieve_pop',
          mod_title: '助力积分到账弹窗',
        },
      };
    case BoostPopupType.Bind:
      return {
        ...baseConfig,
        title: `为${getShortName()}助力`,
        description: '达成活动条件后送你最高',
        btnTitle: '一键助力',
        avatars: [fissionStore.launcherInfo?.avatar || DEFAULT_YYB_AVATAR],
        reportParam: {
          mod_id: 'activity_assist_pop',
          mod_title: '助力弹窗',
        },
      };
    case BoostPopupType.Lottery:
      return {
        ...baseConfig,
        title: '恭喜获得',
        description: '免单',
        btnTitle: '立即收下',
        reportParam: {
          mod_id: 'raffle_free_order_succ_pop',
          mod_title: '抽免单成功结果弹窗',
        },
      };
    default:
      return baseConfig;
  }
});

const showedAvatars = computed(() => {
  const { avatars } = config.value;
  if (avatars.length <= maxShowAvatarNum) return avatars;

  return avatars.slice(0, maxShowAvatarNum - 1);
});

// 名字过长需要截断处理
const getShortName = () => {
  const { nickname = 'TA' } = fissionStore.launcherInfo || {};
  return nickname.length > 5 ? `${nickname.slice(0, 5)}...` : nickname;
};

// 格式化积分，超过一万展示中文
const formatPoints = (points: number) => {
  if (points >= 10000) {
    return `${points / 10000}万`;
  }

  return points;
};

const { toast } = hooks.useMokaInject();
const { useFissionBoostStore, useYybUpdateStore } = store;
const fissionStore = useFissionBoostStore();
const yybUpdateStore = useYybUpdateStore();
let isInBoost = false;
const boost = async () => {
  if (isInBoost) return;
  isInBoost = true;

  if (!await fissionStore.openVipCommonPopup()) {
    isInBoost = false;
    return;
  }

  if (!fissionStore.shareKey) {
    toast?.('系统繁忙，请稍候重试');
    isInBoost = false;
    return;
  }

  if (fissionStore.hasBind) {
    toast?.('本次活动内只能助力一次');
    isInBoost = false;
    return true;
  }

  if (!yybUpdateStore.isVersionValid) {
    $bus?.$emit(UPDATE_YYB_EVENT, { config: props.config });
    isInBoost = false;
    return;
  }

  try {
    // 发起助力
    const { id, modId, appInfo } = props.config;
    const isSuccess = await doBoost(id, Number(modId), fissionStore.shareKey);
    if (isSuccess) {
      toast?.(`助力成功，首次下载注册后领${props.config.bindPoints}积分`);
      fissionStore.activityInfo = {
        ...fissionStore.activityInfo,
        bind_info: {
          share_key: fissionStore.shareKey,
          share_key_avatar: fissionStore.launcherInfo?.avatar ?? DEFAULT_YYB_AVATAR,
          share_key_nick_name: fissionStore.launcherInfo?.nickname ?? '',
          order_id: fissionStore.activityInfo?.bind_info?.order_id ?? '',
        },
      };
    }

    // 助力成功或者失败均刷新 user detail 以获取最新助力信息
    void fissionStore.getUserDetail(id, Number(modId), String(appInfo.appId), utils.getBoostId(String(modId)));
  } catch (error) {
    console.log('[boost]', error);
  } finally {
    isInBoost = false;
    return true;
  }
};

const confirm = async () => {
  if (props.boostPopupType === BoostPopupType.Bind) {
    // 绑定逻辑
    const isBoosted = await boost();
    if (isBoosted) close();
    return;
  }

  close();
};

const emits = defineEmits(['update:show']);
const close = () => {
  emits('update:show', false);
};
</script>
<style lang="scss" scoped>
.boost-popup {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 3.12rem;
  height: 3.7rem;
  background: url('https://yyb.qpic.cn/moka-imgs/1730355759592-huz2dbbuolw.png');
  background-size: 100% 100%;
}

.close {
  position: absolute;
  top: 0.22rem;
  right: 0.24rem;
  width: 0.14rem;
  height: 0.14rem;
}

.avatar {
  position: absolute;
  top: -0.38rem;

  img {
    width: 0.76rem;
    height: 0.76rem;
    border-radius: 50%;
    border: 1.5px solid rgba(255, 208, 151, 1);
  }

  &.multi-avatar {
    display: flex;
    top: -0.29rem;

    img {
      width: 0.58rem;
      height: 0.58rem;
      border-radius: 50%;
      border: 1.5px solid rgba(255, 208, 151, 1);
      margin-left: -0.13rem;
    }
  }

  .more-avatar {
    position: relative;
    width: 0.58rem;
    height: 0.58rem;
    border-radius: 50%;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -0.13rem;
      width: 0.58rem;
      height: 0.58rem;
      background-color: rgba(0, 0, 0, 0.4);
      border-radius: 50%;
    }
    .count {
      position: absolute;
      top: 0.13rem;
      font-family: YYB;
      font-size: 0.24rem;
      font-weight: 400;
      line-height: 0.3343rem;
      color: #fff;
      z-index: 2;
    }
  }
}

.title {
  position: absolute;
  top: 0.43rem;
  font-family: MFFangHei;
  font-size: 0.24rem;
  font-weight: 400;
  line-height: 0.36rem;
  color: rgba(76, 9, 4, 1);
}

.description {
  position: absolute;
  top: 0.78rem;
  font-family: PingFang SC;
  font-size: 0.16rem;
  font-weight: 600;
  line-height: 0.224rem;
  color: rgba(76, 9, 4, 1);
}

.points {
  position: absolute;
  top: 1.16rem;
  font-family: YYB;
  font-size: 0.76rem;
  font-weight: 400;
  line-height: 1.0587rem;
  color: rgba(255, 71, 42, 1);

  span {
    font-size: 0.16rem;
    font-weight: 700;
  }
}

.confirm {
  position: absolute;
  bottom: 0.36rem;
  width: 2.04rem;
  height: 0.58rem;
  border-radius: 0.3rem;
  background: linear-gradient(93.78deg, #FFC985 4%, #FFDCB1 42.17%, #FFC985 98.99%);
  display: flex;
  align-items: center;
  justify-content: center;

  font-family: MFFangHei;
  font-size: 0.2rem;
  font-weight: 400;
  line-height: 0.3rem;
  color: rgba(163, 71, 34, 1);
}
</style>
