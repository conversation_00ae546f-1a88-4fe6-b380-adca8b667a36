<template>
  <van-popup
    position="bottom"
    round
    :style="{
      height: '4.43rem',
      padding: '0.24rem 0.24rem 0.16rem',
    }"
    dt-eid="pop"
    :dt-params="utils.getReportParamsStr({
      mod_id: 'welfare_record_pop',
      mod_title: '福利记录弹窗',
    }, inMagic)"
  >
    <div class="title">
      {{ config.popTitle }}
      <span class="total">
        总计获得<span class="point">{{ totalPoints }}积分</span>
      </span>
    </div>
    <div class="tab-bar">
      <div
        v-for="tab in tabs"
        :key="tab.name"
        class="tab-title"
        :class="{active: detailPopupType === tab.name}"
        dt-eid="button"
        :dt-params="utils.getReportParamsStr({
          btn_title: tab.title,
        }, inMagic)"
        @click="emits('update:detailPopupType', tab.name)"
      >
        {{ tab.title }}
      </div>
    </div>
    <batch-property
      v-if="detailPopupType === DetailPopupType.Property"
    />
    <div
      v-else
      class="details-box"
      dt-eid="tab_page"
      dt-params="tab_title=积分"
    >
      <van-list
        v-model:error="error"
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        error-text="加载出错了~点击重试"
        @load="handleOnLoad"
      >
        <div
          v-for="(detail,index) in details"
          :key="detail.order_id || index"
          class="details"
        >
          <img
            class="avatar"
            :src="detail.avatar || DEFAULT_AVATAR"
            alt="头像"
          >
          <div class="user-info">
            <div class="name">
              {{ detail.nickname }}
            </div>
            <div class="receive-time">
              {{ detail.boost_timestamp }}
            </div>
          </div>
          <div class="point">
            +{{ detail.value }}积分
          </div>
        </div>
      </van-list>
    </div>
  </van-popup>
</template>
<script lang="ts" setup>
import 'vant/es/list/style';
import 'vant/es/popup/style';
import { List as VanList, Popup as VanPopup } from 'vant';
import { computed, Ref, ref } from 'vue';
import { DetailPopupType, hooks, utils } from '@tencent/moka-ui-domain';

import BatchProperty from '../components/batch-property.vue';
import { DEFAULT_AVATAR } from '../constant';
import type { BoostRecord, ComponentConfig } from '../types';

const props = defineProps<{
  boostDetails: BoostRecord[];
  config: ComponentConfig;
  totalPoints: number;
  finished: boolean;
  loading: boolean;
  downloadRegisterDetail?: BoostRecord;
  detailPopupType: DetailPopupType;
  onLoad: (error: Ref<boolean>) => Promise<void>;
}>();

const { inMagic } = hooks.useMokaInject();

const emits = defineEmits(['update:detailPopupType']);
const tabs = [
  {
    name: DetailPopupType.Points,
    title: '积分',
  },
  {
    name: DetailPopupType.Property,
    title: '免费礼包',
  },
];

const error = ref(false);
const details = computed(() => {
  if (props.downloadRegisterDetail) {
    return [props.downloadRegisterDetail, ...props.boostDetails];
  }

  return props.boostDetails;
});

const handleOnLoad = async () => {
  await props.onLoad(error);
};
</script>
<style lang="scss" scoped>
.title {
  font-family: Noto Sans CJK SC;
  font-size: 0.16rem;
  font-weight: 700;
  line-height: 0.2368rem;
  text-align: left;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 0.16rem;
}
.total {
  font-weight: 400;
}
.point {
  color: rgba(238, 60, 56, 1);
}
.details-box {
  height: calc(100% - 0.48rem);
  overflow-y: scroll;
}
.details {
  margin-top: 0.08rem;
  display: flex;
  align-items: center;
}
.avatar {
  width: 0.38rem;
  height: 0.38rem;
  margin-right: 0.08rem;
  border-radius: 50%;
}
.user-info {
  flex: 1;
}
.name {
  font-family: Noto Sans SC;
  font-size: 0.12rem;
  font-weight: 500;
  line-height: 0.1738rem;
  color: rgba(15, 15, 15, 0.8);
}
.tab-bar {
  display: flex;
  margin-bottom: 0.19rem;
  .tab-title {
    font-family: PingFang SC;
    font-size: 0.14rem;
    font-weight: 600;
    height: 0.2rem;
    line-height: 0.196rem;
    margin-right: 0.54rem;
    color: rgba(172, 172, 174, 1);
  }
  .active {
    color: rgba(15, 15, 15, 1);
    position: relative;
    &::after {
      position: absolute;
      bottom: -0.03rem;
      left: calc(50% - 0.22rem / 2);
      content: '';
      width: 0.22rem;
      height: 0.03rem;
      border-radius: 0.008rem;
      background-color: rgba(15, 15, 15, 1);
    }
  }
}
</style>
