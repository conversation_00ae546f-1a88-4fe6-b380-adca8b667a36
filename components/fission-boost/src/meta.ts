import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      modId: {
        type: 'string',
        title: '模块选择',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'ModSelect',
        'x-component-props': {
          modType: 21,
        },
      },
      appInfo: {
        type: 'object',
        title: '游戏配置',
        required: true,
        default: '',
        'x-decorator': 'FormItem',
        'x-component': 'AppSelect',
        'x-component-props': {
          test: 10,
          bindType: 'appInfo',
        },
      },
      bindPoints: {
        required: true,
        type: 'string',
        title: '助力弹窗积分数量',
        'x-decorator': 'FormItem',
        'x-component': 'InputNumber',
      },
      maxBoostNum: {
        type: 'string',
        title: '最多可邀请好友数量 TODO',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'InputNumber',
        default: 20,
      },
      modTitle: {
        type: 'string',
        title: '助力模块标题',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        default: '成功邀请好友',
      },
      popTitle: {
        type: 'string',
        title: '弹窗标题',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        default: '福利记录',
      },
      defaultAvatar: {
        type: 'string',
        title: '默认+号头像',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        default: 'https://yyb.qpic.cn/moka-imgs/1730354913618-4hp0z9pg9hm.png',
      },
      isHideComponent: {
        type: 'boolean',
        title: '是否隐藏组件',
        default: false,
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
