import type { BoostPopupType, VisualAngle } from '@tencent/moka-ui-domain';

/** 助力记录 */
export interface BoostRecord {
  nickname: string;
  avatar: string;
  boost_timestamp: string;
  value: string;
  sub_id: string;
  order_id: string;
}

/** 助力信息接口返回值 */
export interface GetBoostDetailResp {
  launcher_info: {
    nickname: string;
    avatar: string;
  };
  /** 获得总积分 */
  total_points: number;
  boost_records: BoostRecord[];
  visual_angle: VisualAngle;
  has_next: boolean;
  my_share_key: string;
  register_boost_record: BoostRecord;
  last_order_id: string;
}

export interface ComponentConfig {
  id: string;
  modId: string;
  defaultAvatar: string;
  modTitle: string;
  popTitle: string;
  maxBoostNum: number;
  bindPoints: number;
  appInfo: {
    appId: number;
    pkgName: string;
    appName: string;
  };
  isHideComponent: boolean;
}

/** 助力弹窗 props */
export interface BoostPopupProps {
  config: ComponentConfig;
  boostPopupType: BoostPopupType;
  points: number;
  // 助力记录头像列表
  boosterRecords?: string[];
}
