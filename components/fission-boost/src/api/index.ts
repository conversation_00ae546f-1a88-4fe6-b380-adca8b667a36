import { common, utils } from '@tencent/moka-ui-domain';

import type { GetBoostDetailResp } from '../types';

const { exec } = common;

/** 助力列表分页大小 */
const pageNumber = 25;
const queryKeyName = 'share_key';

/** 获取助力详情 */
export async function getBoostDetail(
  componentID: string,
  modID: string,
  lastOrderID: string,
) {
  const result = await exec<unknown, GetBoostDetailResp|undefined>({
    componentID,
    modID: Number(modID),
    methodName: '/trpc.activity.fission_point.FissionPoint/GetUserPoints',
    data: {
      boost_id: utils.getBoostId(modID),
      boosted_share_key: utils.fetchQueryParam(queryKeyName) || '',
      last_order_id: lastOrderID,
      num: pageNumber,
    },
  });

  return result?.data;
}

/** 发起数值助力生成 shareKey */
export async function launchValueBoost(
  componentID: string,
  modID: string,
) {
  const result = await exec<unknown, {share_key: string}|undefined>({
    componentID,
    modID: Number(modID),
    methodName: '/trpc.activity.boost_server.BoostServer/LaunchValueBoost',
    data: {
      boost_id: utils.getBoostId(modID),
    },
  });

  return result?.data;
}

/** 数值普通助力 */
export async function doBoost(
  componentID: string,
  modID: number,
  shareKey: string,
) {
  const result = await exec({
    componentID,
    modID,
    methodName: '/trpc.activity.fission_point.FissionPoint/RelationBoost',
    data: {
      share_key: shareKey,
      sub_id: 'fission_relation_boost', // 普通助力id 固定为 fission_relation_boost
      boost_id: utils.getBoostId(String(modID)),
    },
  });

  return result?.data;
}
