<template>
  <div class="fission-boost">
    <div
      dt-eid="card"
      :dt-params="utils.getReportParamsStr({
        mod_id: 'invite_record_card',
        mod_title: '邀请记录卡'
      }, inMagic)"
      :dt-cmd="`hold=${fissionStore.isReportHeld}`"
      class="boost-container"
    >
      <boost
        v-if="!config.isHideComponent"
        :config="config"
        :boost-details="boostRecords.slice(0, 6)"
      />
      <boost-detail
        v-model:show="show"
        v-model:detail-popup-type="detailPopupType"
        :config="config"
        :boost-details="boostRecords"
        :finished="finished"
        :loading="loading"
        :total-points="Number(totalPoints)"
        :on-load="loadBoostDetails"
      />
      <boost-popup
        v-model:show="showBoostPopup"
        v-bind="boostPopupProps"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, Ref, ref } from 'vue';
import { BoostEvent, BoostPopupType, DetailPopupType, hooks, store, utils } from '@tencent/moka-ui-domain';

import { getBoostDetail, launchValueBoost } from '../api';
import Boost from '../components/boost.vue';
import BoostDetail from '../components/boost-detail.vue';
import boostPopup from '../components/boost-popup.vue';
import type { BoostPopupProps, BoostRecord, ComponentConfig } from '../types';
import { getBoostNewRecordsShowConditions } from '../utils';

const props = defineProps<{
  config: ComponentConfig;
}>();

const show = ref(false);
const { useFissionBoostStore } = store;
const fissionStore = useFissionBoostStore();
const { isLogin, inMagic, $bus, toast, loginReady } = hooks.useMokaInject();
const { setPublicParams } = hooks.useReport();

const getShareKey = async () => {
  if (!isLogin.value) return;
  const { id, modId } = props.config;
  const { share_key: shareKey } = await launchValueBoost(id, modId) || {};
  if (!shareKey) {
    toast?.('生成分享 id 失败');
    return;
  }

  fissionStore.myShareKey = shareKey;
  console.log('[launchValueBoost] shareKey', shareKey);
};

const boostRecords = ref<BoostRecord[]>([]);
const totalPoints = ref(0);
const lastOrderID = ref('');
const loading = ref(false);
const finished = ref(false);
const updateBoostDetails = async (justGetDownloadRegisterRecord = false) => {
  const { id, modId } = props.config;
  const result = await getBoostDetail(id, modId, lastOrderID.value);

  if (!result) {
    throw new Error('获取助力记录失败');
  }

  const {
    my_share_key: myShareKey,
    launcher_info: launcherInfo,
    total_points: points,
    boost_records: flows,
    has_next: hasNext,
    visual_angle: visualAngle,
    last_order_id: oLastOrderID,
  } = result;

  if (!justGetDownloadRegisterRecord) {
    lastOrderID.value = oLastOrderID;
    loading.value = false;
    finished.value = hasNext === false;
    boostRecords.value.push(...(flows ?? []));
  }

  totalPoints.value = points ?? 0;
  fissionStore.launcherInfo = launcherInfo;
  fissionStore.visualAngle = visualAngle;

  if (myShareKey) {
    fissionStore.myShareKey = myShareKey;
    return;
  }

  // 没有 shareKey 发起助力生成 shareKey
  if (!fissionStore.myShareKey) {
    await getShareKey();
  }
};

const queryKeyName = 'share_key';
const loadBoostDetails = async (error?: Ref<boolean>) => {
  if (inMagic) return;
  if (loading.value) return;
  loading.value = true;
  try {
    await updateBoostDetails();
  } catch (e) {
    console.log('[loadBoostDetails] error:', e);
    // eslint-disable-next-line no-param-reassign
    if (error) error.value = true;
  } finally {
    const hostID = fissionStore.isMaster ? 1 : 0;
    setPublicParams({
      inviter_id: fissionStore.shareKey || '',
      is_host: isLogin.value ? hostID : -1,
    });

    loading.value = false;
    fissionStore.isReportHeld = false;
  }
};

// 福利记录明细
const detailPopupType = ref(DetailPopupType.Points);
const openDetailPopup = async (type: DetailPopupType) => {
  if (!await fissionStore.openVipCommonPopup()) {
    return;
  }

  detailPopupType.value = type;
  show.value = true;
};

$bus?.$on(BoostEvent.OpenDetailPopup, (type: DetailPopupType) => {
  void openDetailPopup(type);
});

// 助力弹窗
const boostPopupProps = ref<BoostPopupProps>({
  points: 0,
  config: props.config,
  boostPopupType: BoostPopupType.Boost,
});
const showBoostPopup = ref(false);
const openBoostPopup = async (options: BoostPopupProps) => {
  if (!await fissionStore.openVipCommonPopup()) {
    return;
  }

  boostPopupProps.value = {
    ...options,
    config: props.config,
  };

  showBoostPopup.value = true;
};

$bus?.$on(BoostEvent.OpenBoostPopup, (options: BoostPopupProps) => {
  void openBoostPopup(options);
});

// 获取用户详情（包括激活授权状态）
const getUserDetail = async () => {
  const { id, modId, appInfo: { appId } } = props.config;
  const boostId = utils.getBoostId(String(modId));
  fissionStore.getUserDetailOptions = {
    id,
    modId,
    appId,
    boostId,
  };

  if (!isLogin.value) return;

  await fissionStore.getUserDetail(id, Number(modId), String(appId), boostId);
};

const init = async () => {
  await loginReady;
  const shareKey = utils.fetchQueryParam(queryKeyName) || '';
  fissionStore.shareKey = shareKey;

  await Promise.all([loadBoostDetails(), getUserDetail()]);

  if (shareKey && !fissionStore.isMaster) {
    showBoostPopup.value = true;
    boostPopupProps.value = {
      points: props.config.bindPoints,
      config: props.config,
      boostPopupType: BoostPopupType.Bind,
    };

    return;
  }

  // 无记录未激活未授权不展示助力弹窗
  if (!boostRecords.value.length || !fissionStore.hasAuthorized || !fissionStore.hasActivatedVip) return;

  const {
    showBoostNewRecords = false,
    points = 0,
    boosterRecords = [],
  } = getBoostNewRecordsShowConditions(boostRecords.value) || {};
  if (!showBoostNewRecords) return;
  void openBoostPopup({
    points,
    config: props.config,
    boosterRecords,
    boostPopupType: BoostPopupType.Boost,
  });
};

void init();
</script>

<style lang="scss" src="./index.scss" scoped />
