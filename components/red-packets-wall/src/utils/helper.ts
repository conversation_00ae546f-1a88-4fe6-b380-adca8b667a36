import { Consts } from '@tencent/moka-athena-next-v2';
import { actUtils } from '@tencent/moka-data-core';
import type { LotteryOnline, YYBGiftInfo } from '@tencent/moka-ui-domain';
import { camelizeKeys, safeJsonParse, toCamelCase } from '@tencent/mole-utils-lib';

import type { ComponentConfig, GetRedPacketWallInfoResponse, LotteryBackMods, RedPacketsWallConfig, RedPacketsWallData } from '../types';
import { RedPacketsWallType } from '../types';

/**
 * 格式化红包墙数据
 * @param type 红包墙类型
 * @param config 组件配置
 * @param backMods 后端模块数据
 * @param redPacketWallInfo 红包墙信息
 * @returns 格式化后的红包墙数据
 */
export function formatRedPacketsWallData(
  type: RedPacketsWallType,
  config: ComponentConfig,
  backMods: LotteryBackMods[],
  redPacketWallInfo?: GetRedPacketWallInfoResponse,
): RedPacketsWallData {
  const {
    tencentConfig,
    cooperationConfig,
    lotteryIidPos,
    lotteryTimes,
    lotteryOnlines,
  } = redPacketWallInfo || {};
  const isJp = type === RedPacketsWallType.Jp;
  const wallConfig: RedPacketsWallConfig[] = camelizeKeys(safeJsonParse((isJp ? tencentConfig : cooperationConfig) || '')) ;
  const {
    id,
    gearHead,
    canOpenPics,
    obtainedPic,
    openWordPic,
    luckPointPic,
    luckPointLyPic,
    deliveryPic,
    redPacketBgs,
    lockPic,
  } = config;

  return backMods.map((mod, index) => {
    const { modID, noOpenableFirstPic, noOpenableSecondPic, noOpenableThirdPic, tag } = mod;
    const noOpenablePics = [noOpenableFirstPic, noOpenableSecondPic, noOpenableThirdPic];
    // 前端统一把后台返回的 modID 转换成了驼峰命名 需要匹配
    const backendLotteryId = toCamelCase(actUtils.getCIIDByModID({ modID }));

    return {
      id,
      modID,
      type,
      backendLotteryId,
      noOpenablePics,
      obtainedPic,
      openWordPic,
      deliveryPic,
      luckPointPic: isJp ? luckPointPic : luckPointLyPic,
      gearHeadPic: gearHead,
      canOpenPic: canOpenPics[index],
      redPacketBg: redPacketBgs[index],
      lockPic,
      lotteryIIDPos: lotteryIidPos?.[backendLotteryId] ?? '',
      lotteryTimes: lotteryTimes?.[backendLotteryId] ?? 0,
      gearLotteryPoints: wallConfig[index]?.lotteryPoints ?? 0,
      gearRechargeThresholds: wallConfig[index]?.rechargeGear ?? [],
      gearRechargeMaxThreshold: wallConfig[index]?.rechargeGear?.[2] ?? 0,
      lotteryOnline: lotteryOnlines?.[backendLotteryId],
      tag,
    };
  });
}

/**
 * 获取物品类型
 * @param propertyResult 物品结果信息
 * @returns 物品类型
 */
export function getPropertyType(propertyResult: LotteryOnline['properties'][number]) {
  const { baseType, deliveryPlat } = propertyResult.property.detail.desc;
  const isYYBGift = baseType === Consts.PROPERTY_BASE_TYPE.VIRTUAL
    && deliveryPlat.platId === Consts.PLAT_ID_TYPE.YYB_GIFT;

  if (!isYYBGift) return Consts.PRESENT_TYPE.NONE;
  // 返回应用宝礼包信息
  const presentInfo = camelizeKeys<YYBGiftInfo>(safeJsonParse(deliveryPlat.extends ?? '{}')) ;

  return presentInfo.giftDetail.type;
}
