import { onMounted, onUnmounted, ref } from 'vue';
import { isMobileQQ } from '@tencent/mole-utils-lib';

/**
 * tips: 微信和QQ跳二级页（非单页场景）时，表现不同。
 * 微信是在原webview打开新页面，原页面会关闭；
 * QQ是新开webview，返回到原页面时还是旧的页面，这时如果需要监听返回行为就可以监听qbrowserVisibilityChange
 */
export function useDocumentVisibility() {
  const isMQQ = isMobileQQ(navigator.userAgent);
  const visibility = ref('');

  visibility.value = document.visibilityState;

  const onQBrowserVisibilityChange = (e: any) => {
    if (e.hidden === true) {
      visibility.value = 'hidden';
    } else {
      visibility.value = 'visible';
    }
  };

  const onVisibilitychange = () => {
    visibility.value = document.visibilityState;
  };

  onMounted(() => {
    if (isMQQ) {
      /**
       * 手q抛出的可见性事件和浏览器api不同
       * http://mqq.oa.com/api.html#js-mqq-ui-pageVisibility
       */
      document.addEventListener('qbrowserVisibilityChange', onQBrowserVisibilityChange);
    } else {
      document.addEventListener('visibilitychange', onVisibilitychange);
    }
  });

  onUnmounted(() => {
    if (isMQQ) {
      document.removeEventListener('qbrowserVisibilityChange', onQBrowserVisibilityChange);
    } else {
      document.removeEventListener('visibilitychange', onVisibilitychange);
    }
  });

  return visibility;
}
