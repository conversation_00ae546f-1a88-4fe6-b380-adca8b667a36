.redPacketsWallContainer {
  width: 3.28rem !important;
  min-height: 3.8rem;
  height: auto !important;
  padding: 0.19rem 0.08rem 0.15rem;
  background: linear-gradient(180deg, #FFAC66 -13.64%, #FFD7B6 -3.28%, #FFF2E8 11.29%, #FFEDDD 56.33%, #FFDDC1 100%);
  border-radius: 0.13rem;
}

.tab {
  display: flex;
  margin-bottom: .1rem;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  font-family: Noto Sans CJK SC;
  text-align: center;
  font-weight: 700;
  line-height: 0.12rem;

  .title {
    display: flex;
    align-items: center;
    font-size: 0.14rem;
    color: rgba(112, 45, 0, 0.5);

    img {
      width: 0.38rem;
      height: 0.13rem;
      opacity: .5;
    }
  }

  .sub-title {
    font-size: 0.1rem;
    margin-top: 0.04rem;
    color: rgba(112, 45, 0, 0.35);
  }
}

.active {
  position: relative;
  color: rgba(112, 45, 0, 1);

  &::after {
    position: absolute;
    content: '';
    width: 0.24rem;
    height: 0.02rem;
    left: calc(50% - 0.12rem);
    bottom: -0.027rem;
    background-color: rgba(112, 45, 0, 1);
    border-radius: 0.005rem;
  }

  .title {
    color: rgba(112, 45, 0, 1);
    img {
      opacity: 1;
    }
  }

  .sub-title {
    color: rgba(112, 45, 0, .85);
  }
}

.tips {
  margin-top: 0.1rem;
  display: flex;
  align-items: center;
  font-family: Noto Sans SC;
  font-size: 0.1rem;
  font-weight: 400;
  line-height: 0.1448rem;
  color: rgba(112, 45, 0, 1);
}