<template>
  <div class="redPacketsWallContainer">
    <div class="tab">
      <div
        v-for="{ title, type } in tabData"
        :key="type"
        :class="['title-container', { active: activeTab === type }]"
        dt-eid="tab_button"
        dt-reuse-identifier="tab_title"
        :dt-params="`mod_id=tab_page_button&tab_title=${type}`"
        @click="switchTab(type)"
      >
        <div class="title">
          {{ title.title }}
          <img
            v-if="type === RedPacketsWallType.Jp"
            src="https://yyb.qpic.cn/moka-imgs/1737555325573-v8x7lv8m9ib.png"
            alt=""
          >
        </div>
        <div class="sub-title">
          {{ title.subTitle }}
        </div>
        <div
          v-if="activeTab === type"
          dt-eid="tab_page"
          dt-clck-ignore
        />
      </div>
    </div>
    <div
      v-for="tab in tabData"
      v-show="activeTab === tab.type"
      :key="tab.type"
      dt-eid="card"
      :dt-params="`mod_id=red_packet_wall_card&tab_title=${tab.type}`"
      dt-reuse-identifier="tab_title"
    >
      <lottery-wall
        v-show="tab.redPacketsWallData"
        :red-packets-wall-data="tab.redPacketsWallData"
        :progress="progress"
      />
      <recharge-progress
        v-bind="progress"
      />
      <support-games
        v-if="!inYSDK"
        :type="tab.type"
        :support-game-info="supportGameInfo"
      />
      <div
        v-if="!inYSDK"
        class="tips"
        @click="openRule"
      >
        <div> {{ tab.type === RedPacketsWallType.Jp ? config.tipConfig.jpTip : config.tipConfig.lyTip }}</div>
        <van-icon
          style="margin-left: 0.02rem;"
          name="question-o"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'vant/es/icon/style';
import 'vant/es/dialog/style';
import emitter from 'tiny-emitter/instance';
import { Icon as VanIcon, showDialog } from 'vant';
import { computed, getCurrentInstance, h, onUnmounted, ref, watch } from 'vue';
import { Consts } from '@tencent/moka-athena-next-v2';
import { hooks } from '@tencent/moka-ui-domain';
import { flatten, getURLParams, getURLQueryParams, isIOS, isUndefined, isYSDK } from '@tencent/mole-utils-lib';

import { getRedPacketsWallInfo, getSupportGames } from '../api';
import { showLotteryPacketDialog, type ShowLotteryPacketDialogParams } from '../components/lottery-packet-dialog';
import LotteryWall from '../components/lottery-wall.vue';
import RechargeProgress from '../components/recharge-progress.vue';
import SupportGames from '../components/support-games.vue';
import { COMPONENT_EMIT_EVENT, COMPONENT_EVENT, COMPONENT_METHODS } from '../constant';
import { AnchorTabType, type ComponentConfig, type GetRedPacketWallInfoResponse, GetSupportGamesResp, OnlyShowTabType, Progress, RedPacketsWallType } from '../types';
import { formatRedPacketsWallData, getPropertyType } from '../utils/helper';
import { useDocumentVisibility } from '../utils/use-document-visibility';

const props = defineProps<{
  config: ComponentConfig;
}>();

const inYSDK = isYSDK(navigator.userAgent);
const activeTab = ref<RedPacketsWallType>();
const redPacketWallInfo = ref<GetRedPacketWallInfoResponse>();
const supportGameInfo = ref<GetSupportGamesResp>();
const { onlyShowTab, anchorTab } = getURLQueryParams(location.href);
const isOnlyShowJpTab = onlyShowTab === OnlyShowTabType.JpTab;
const tabData = computed(() => {
  const { tabTitles, jpLotteryBackMods, lyLotteryBackMods } = props.config;
  const [jpTabTitle, lyTabTitle] = tabTitles;

  // TODO 方法参数较长，待优化
  const lyRedPacketsWallData = formatRedPacketsWallData(
    RedPacketsWallType.Ly,
    props.config,
    lyLotteryBackMods,
    redPacketWallInfo.value,
  );
  const jpRedPacketsWallData = formatRedPacketsWallData(
    RedPacketsWallType.Jp,
    props.config,
    jpLotteryBackMods,
    redPacketWallInfo.value,
  );

  const lyTab = {
    type: RedPacketsWallType.Ly,
    title: lyTabTitle,
    redPacketsWallData: lyRedPacketsWallData,
  };
  const jpTab = {
    type: RedPacketsWallType.Jp,
    title: jpTabTitle,
    redPacketsWallData: jpRedPacketsWallData,
  };

  // YSDK
  if (inYSDK) {
    return [lyTab];
  }

  // 链接干预
  if (onlyShowTab) {
    return [isOnlyShowJpTab ? jpTab : lyTab];
  }

  // 兜底默认
  return [jpTab, lyTab];
});
// 气泡组件进度配置
const progress = computed<Progress>(() => {
  const { userCooperationRecharge, userTencentRecharge } = supportGameInfo.value || {};
  const isJpTabActive = activeTab.value === RedPacketsWallType.Jp;

  // 计算累充金额 单位：分转为元
  const rechargeCountByTab = isJpTabActive ? userTencentRecharge : userCooperationRecharge;
  const userRechargeCount = Math.floor(Number(rechargeCountByTab || 0) / 100);

  const curTabData = tabData.value.find(item => item.type === activeTab.value);
  const { redPacketsWallData = [] } = curTabData || {};
  // 获得当前红包墙每个红包的获取档位
  const thresholds = flatten(redPacketsWallData.map(item => item.gearRechargeThresholds)) as unknown as number[];

  if (!thresholds?.length) {
    return {
      userRechargeCount,
      tips: '数据拉取失败',
      rewardInfo: [],
      pointDesc: '',
    };
  }

  // 过滤出比当前用户累充金额大的所有档位
  const largerNumbers = thresholds.filter(threshold => threshold > userRechargeCount);
  // 取出距离当前累充最小的一个档位
  const nextLargerNumber = Math.min(...largerNumbers);
  // 计算解锁下一个红包的累充差值
  const distance = largerNumbers.length > 0 ? (nextLargerNumber - userRechargeCount) : 0;
  // 计算下一个解锁红包所在的档位
  const nextRedPacketGear = redPacketsWallData.find(item => item.gearRechargeThresholds.includes(nextLargerNumber));
  // 进度配置
  const progressConfig = [
    ...redPacketsWallData.map((item, idx) => ({
      milestone: item.gearRechargeMaxThreshold,
      width: idx === 0 ? 42 : 80,
      thresholds: item.gearRechargeThresholds.slice(0, 2).map((threshold) => {
        const preMaxThreshold = idx === 0 ? 0 : redPacketsWallData[idx - 1].gearRechargeMaxThreshold;
        return ({
          left: (threshold - preMaxThreshold) / (item.gearRechargeMaxThreshold - preMaxThreshold) * 100,
          value: threshold,
        });
      }),
    })),
    {
      milestone: Math.max(...thresholds),
      width: 30,
    },
  ];

  if (!nextRedPacketGear) {
    return {
      currentProgress: userRechargeCount,
      tips: '您已解锁全部红包。去应用宝，用积分兑更多豪礼',
      progressConfig,
      pointDesc: '',
    };
  }

  // 计算下一个红包所在档位的物品信息
  const { properties } = nextRedPacketGear.lotteryOnline || {};
  // 过滤掉新春大吉（谢谢参与）商品
  const formattedProperties = properties?.filter(item => Consts.PRESENT_TYPE.NONE !== getPropertyType(item))
    ?.map(({ property, realTimeConfig }) => ({
      name: property.instanceInfo.name,
      icon: property.instanceInfo.picUrl,
      price: property.instanceInfo.price,
      rate: realTimeConfig.winRate,
    }));

  // 价值最高奖品
  const maxPriceProperty = formattedProperties?.sort((a, b) => b.price - a.price)?.[0];
  // 最大概率奖品
  const maxRateProperty = formattedProperties?.sort((a, b) => b.rate - a.rate)?.[0];
  // 奖品概率之和
  const rate = formattedProperties?.reduce((pre, cur) => ((pre * 100 + (cur.rate * 100)) / 100), 0);
  return {
    currentProgress: userRechargeCount,
    tips: `再攒${distance}个${isJpTabActive ? '福气值' : '好运值'},可开下1个红包,最高得${maxPriceProperty?.name}`,
    pointDesc: isJpTabActive ? `*需使用${nextRedPacketGear.gearLotteryPoints}积分` : '',
    maxRatePropertyDesc: `本轮${rate}%概率获得${maxRateProperty?.name}或以上奖品!`,
    progressConfig,
  };
});

const { $bus, isLogin, loginReady } = hooks.useMokaInject();
const instance = getCurrentInstance();

void init();

// 监听组件所有内部事件以触发外部事件
emitter.on(COMPONENT_EMIT_EVENT.InnerEvent, innerEventCallback);

// 取消监听事件
onUnmounted(() => {
  emitter.off(COMPONENT_EMIT_EVENT.InnerEvent, innerEventCallback);
});

async function innerEventCallback(eventName: string, cb?: () => void | Promise<void>) {
  // 刷新红包墙信息事件
  if (eventName === COMPONENT_EMIT_EVENT.RefreshRedPacketsWall) {
    await cb?.();
    void refreshRedPacketsWall();
    return;
  }

  // 积分兑换锚定事件: 根据当前 tab 触发不同锚定事件
  if (eventName === COMPONENT_EVENT.NeedScrollToPointExchange) {
    scrollToPointExchange();
    return;
  }

  // 不满足抽奖事件：根据当前 tab 触发不同事件
  if (eventName === COMPONENT_EVENT.NeedShowRule) {
    openRule();
    return;
  }

  $bus?.$emit(eventName, instance?.proxy);
}

function scrollToPointExchange() {
  const realEventName = activeTab.value === RedPacketsWallType.Jp
    ? COMPONENT_EVENT.NeedScrollToPointExchangeByJp
    : COMPONENT_EVENT.NeedScrollToPointExchangeByLy;
  $bus?.$emit(realEventName, instance?.proxy);
}

function openRule() {
  const realEventName = activeTab.value === RedPacketsWallType.Jp
    ? COMPONENT_EVENT.NeedShowJpRule
    : COMPONENT_EVENT.NeedShowLyRule;
  $bus?.$emit(realEventName, instance?.proxy);
}

// 初始化组件数据
async function init() {
  void refreshRedPacketsWall();
  supportGameInfo.value = await getSupportGames();
  // 按照约定红包助力分享会助力信息和拓展字段，优先级高于接口在玩游戏
  const { shareKey, boostID, extendBoostShareInfo } = getURLParams(location.href);
  if (shareKey && boostID && !isUndefined(extendBoostShareInfo)) {
    const activateType = extendBoostShareInfo === 'jp' ? RedPacketsWallType.Jp : RedPacketsWallType.Ly;
    activeTab.value = activateType;

    const event = activateType === RedPacketsWallType.Jp ? COMPONENT_EVENT.ChangeJpTab : COMPONENT_EVENT.ChangeLyTab;
    $bus?.$emit(event, instance?.proxy);
    return;
  }

  switchTab(getInitActiveTab());
}

// 切换 tab
function switchTab(type: RedPacketsWallType) {
  activeTab.value = type;
  // 触发事件更新
  const event = type === RedPacketsWallType.Jp ? COMPONENT_EVENT.ChangeJpTab : COMPONENT_EVENT.ChangeLyTab;
  $bus?.$emit(event, instance?.proxy);
}

// 刷新红包墙信息
async function refreshRedPacketsWall() {
  redPacketWallInfo.value = await getRedPacketsWallInfo();
  console.info('[refreshRedPacketsWall] 红包墙信息', redPacketWallInfo.value);
}

// 获取默认展示的tab
function getInitActiveTab() {
  if (inYSDK) {
    return RedPacketsWallType.Ly;
  }

  if (onlyShowTab) {
    return isOnlyShowJpTab ? RedPacketsWallType.Jp : RedPacketsWallType.Ly;
  }

  if (anchorTab) {
    return anchorTab === AnchorTabType.JpTab ? RedPacketsWallType.Jp : RedPacketsWallType.Ly;
  }

  // 在玩游戏判断
  const { userMaxRechargeGame } = supportGameInfo.value || {};
  if (userMaxRechargeGame) {
    return userMaxRechargeGame.gameType === 1 ? RedPacketsWallType.Jp : RedPacketsWallType.Ly;
  }

  return RedPacketsWallType.Ly;
}

/** 执行红包抽奖 */
async function doLotteryFromEvent(componentInstance: any, params: ShowLotteryPacketDialogParams) {
  console.log('[doLotteryFromEvent] 抽奖触发事件', params);

  await loginReady;
  // 未登录拉起三合一登录
  if (!isLogin.value) {
    (window as any).vipCommonLogic?.openLogin();
    return;
  }

  // 判断是否已激活
  const isActivated = await (window as any).vipCommonLogic?.tryActivate?.();
  if (!isActivated) {
    console.log('[doLotteryFromEvent] 用户未激活');
    return;
  }

  // 判断是否已授权
  const isAuthed = await (window as any).vipCommonLogic.tryAuthGame?.();
  if (!isAuthed) {
    console.log('[doLotteryFromEvent] 用户未授权');
    return;
  }

  void showLotteryPacketDialog({
    ...params,
    openButtonImage: props.config.openWordPic,
    // 固定第一档
    mask: props.config.canOpenPics[0],
  });
}

let isInit = false;
function showIOSUserMainStateDialog() {
  if (!isIOS(navigator.userAgent) || isInit) return;

  const { shareKey, boostID } = getURLParams(location.href);
  // 客态不展示
  if (shareKey && boostID) {
    return;
  }

  isInit = true;
  void showDialog({
    title: '活动小提示',
    message: () => h('span', { style: 'color: #000' }, '当前活动仅支持安卓设备参与，敬请谅解！'),
    closeOnClickOverlay: false,
    showConfirmButton: false,
  }).then(() => {})
    .catch(() => {});
}

showIOSUserMainStateDialog();

const visibility = useDocumentVisibility();
watch(visibility, async (cur, per) => {
  if (cur === 'visible' && per === 'hidden') {
    void refreshRedPacketsWall();
    supportGameInfo.value = await getSupportGames();
  }
});

defineExpose({
  [COMPONENT_METHODS.DoLottery]: doLotteryFromEvent,
});
</script>
<style lang="scss" src="./index.scss" scoped />
