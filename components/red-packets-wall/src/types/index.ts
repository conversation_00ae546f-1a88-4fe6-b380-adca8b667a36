import type { LotteryOnline, PropertyResult } from '@tencent/moka-ui-domain';

/** 红包状态 */
export enum RedPacketStatus {
  /** 不满足拆封条件 */
  NOT_SATISFY_OPEN = 0,
  /** 可拆封 */
  CAN_OPEN = 1,
  /** 已拆封 */
  OPENED = 2,
}

/** 抽奖后端模块类型 */
export interface LotteryBackMods {
  /** 抽奖模块 ID */
  modID: string;
  /** 不可拆封第一张 */
  noOpenableFirstPic: string;
  /** 不可拆封第二张 */
  noOpenableSecondPic: string;
  /** 不可拆封第三张 */
  noOpenableThirdPic: string;
  /** 顶部标签 */
  tag: string;
}

/** 组件配置 */
export interface ComponentConfig {
  /** 组件 ID */
  id: string;
  /** 红包活动配置 */
  redpacketsConfig: string;
  /** tab 标题 */
  tabTitles: {
    title: string;
    subTitle: string;
  }[];
  /** 提示配置 */
  tipConfig: {
    jpTip: string;
    lyTip: string;
  };
  /** 红包背景板图片数组 */
  redPacketBgs: string[];
  /** 档位头部图片 */
  gearHead: string;
  /** 档位标签 */
  tag: string;
  /** 可拆封状态图片数组 */
  canOpenPics: string[];
  /** 精品游戏抽奖后端模块 */
  jpLotteryBackMods: LotteryBackMods[];
  /** 联运游戏抽奖后端模块 */
  lyLotteryBackMods: LotteryBackMods[];
  /** 已领取状态背景板 */
  obtainedPic: string;
  /** "开" 字背景图 */
  openWordPic: string;
  /** 福气值背景图 */
  luckPointPic: string;
  /** 福气值联运背景图 */
  luckPointLyPic: string;
  /** 发货中背景图 */
  deliveryPic: string;
  /** 未拆封按钮图 */
  lockPic: string;
}

/** 红包档位数据 */
export interface RedPacketsGearData {
  /** 组件 ID */
  id: string;
  /** 档位抽奖模块 ID */
  modID: string;
  /** 档位抽奖模块对应后端 CIID */
  backendLotteryId: string;
  /** 不可拆封红包图片数组 */
  noOpenablePics: string[];
  /** 已领取状态背景图 */
  obtainedPic: string;
  /** "开" 字背景图 */
  openWordPic: string;
  /** 福气值背景图 */
  luckPointPic: string;
  /** 发货中背景图 */
  deliveryPic: string;
  /** 档位头部标签图片 */
  gearHeadPic: string;
  /** 可拆封状态红包图 */
  canOpenPic: string;
  /** 档位抽奖次数 */
  lotteryTimes: number;
  /** 档位抽奖需要消耗的积分 */
  gearLotteryPoints: number;
  /** 档位红包解锁充值进度门槛数组 */
  gearRechargeThresholds: number[];
  /** 档位解锁充值进度最高门槛值(用于进度条) */
  gearRechargeMaxThreshold: number;
  /** 抽奖物品所在红包位置 */
  lotteryIIDPos: string;
  /** 红包墙类型 */
  type: RedPacketsWallType;
  /** 红包背景板 */
  redPacketBg: string;
  /** 未拆封按钮图 */
  lockPic: string;
  /** 抽奖配置 */
  lotteryOnline?: LotteryOnline;
}

/** 红包墙数据 */
export type RedPacketsWallData = RedPacketsGearData[];

/** 红包墙类型 */
export enum RedPacketsWallType {
  Jp = 0,
  Ly = 1,
}

/** 仅展示 tab 类型 */
export enum OnlyShowTabType {
  /** 精品 tab */
  JpTab = '1',
  /** 联运 tab */
  LyTab = '2',
}

/** 锚定 tab 类型 */
export enum AnchorTabType {
  /** 精品 tab */
  JpTab = '1',
  /** 联运 tab */
  LyTab = '2',
}

/** 应用基本信息 */
export interface AppBaseInfo {
  /** 应用 appid */
  appId: number;
  /** 名字 */
  name: string;
  /** icon */
  icon: string;
}

/** 红包墙后端配置 */
export interface RedPacketsWallConfig {
  lotteryPoints: number;
  rechargeGear: number[];
  yybPointsId: string;
  lotteryBackendId: string;
}

/** 查询用户红包墙组件的信息响应 */
export interface GetRedPacketWallInfoResponse {
  /** 用户当前精品游戏累计的充值 */
  userRechargeCountJp: number;
  /** 用户当前联运游戏累计的充值 */
  userRechargeCountLy: number;
  /** 精品抽奖配置 */
  tencentConfig: string;
  /** 联运抽奖配置 */
  cooperationConfig: string;
  /** 精品游戏参与列表 */
  tencentGames: AppBaseInfo[];
  /** 联运游戏参与列表 */
  cooperationGames: AppBaseInfo[];
  /** 每一个奖池，当前抽奖次数，key 是抽奖模块 id */
  lotteryTimes: { [key: string]: number };
  /** 用户开启红包墙位置信息，key 是抽奖模块 id，value 是个英文符号分割字符串，示例：1,0,2 代表用户点击的位置和次序 */
  lotteryIidPos: { [key: string]: string };
  /** 抽奖每个奖池配置信息，key 是抽奖模块 id */
  lotteryOnlines: { [key: string]: LotteryOnline };
}

/** 抽奖后端单个奖池配置信息 */
export interface BackendLotteryItem {
  /** 抽奖消耗积分 */
  lottery_points: number;
  /** 充值档位 */
  recharge_gear: number[];
  /** 抽奖次数对于积分 ID */
  yyb_points_id: string;
  /** 抽奖后端 ID */
  lottery_backend_id: string;
}
/** 奖励类型 */
export enum RewardType {
  /** 其他 */
  Other = 0,
  /** 积分 */
  Point = 1,
  /** QB */
  QB = 2,
}

/** 红包状态 */
export enum PacketStatus {
  /** 锁定状态 */
  Lock = 1,
  /** 可抽奖状态 */
  Lottery = 2,
  /** 已领取状态 */
  Received = 3,
}

/** 消耗积分类型，TODO: 后续与后台协议对齐 */
export enum ConsumePointType {
  /** 通用积分 */
  Common = 1,
  /** 王者积分 */
  WZ = 2,
}

interface GameInfo {
  appid: string;
  icon: string;
  name: string;
}

interface UserMaxRechargeGame {
  appid: string;
  gameType: number;
  rechargeAmount: string;
}

/** 获取支持的游戏列表响应 */
export interface GetSupportGamesResp {
  cooperationGames: GameInfo[];
  msg: string;
  ret: number;
  tencentGames: GameInfo[];
  userCooperationGames: GameInfo[];
  userCooperationRecharge: string;
  userMaxRechargeGame: UserMaxRechargeGame;
  userTencentGames: GameInfo[];
  userTencentRecharge: string;
}

/** 红包数据 */
export interface RedPacketInfo {
  key: string;
  noOpenablePic: string;
  status: RedPacketStatus;
  property?: PropertyResult;
  threshold: number;
  type: RedPacketsWallType;
  obtainedPic: string;
  redPacketBg: string;
  lockPic: string;
  openWordPic: string;
  lotteryPoints: number;
  deliveryPic: string;
  canOpenPic: string;
}

export interface Progress {
  currentProgress: number;
  tips: string;
  rewardInfo: {
    name: string;
    icon: string;
    price: number;
  }[];
  pointDesc?: string;
  progressConfig?: {
    width: number;
    mileStone: number;
  }[];
}
