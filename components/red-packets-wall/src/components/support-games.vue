<template>
  <div
    v-if="supportGames.length"
    :class="$style.supportGamesContainer"
    dt-eid="button"
    dt-params="button_title=查看活动支持游戏list"
    @click="jumpToSupportGames(props.type)"
  >
    <div :class="$style.supportGames">
      <!-- 不支持滑动点击跳二级页 -->
      <div
        v-for="game in supportGames.slice(0,7)"
        :key="game.appid"
        :class="$style.gameIcon"
      >
        <img
          :src="replaceURLProtocol(game.icon)"
          alt=""
        >
      </div>
    </div>
    <div :class="$style.supportGamesCount">
      <span>{{ supportGames.length }}款游戏参与</span>
      <van-icon
        style="font-weight: 700"
        name="arrow"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import emitter from 'tiny-emitter/instance';
import { Icon as VanIcon } from 'vant';
import { computed, onUnmounted } from 'vue';
import { utils } from '@tencent/moka-ui-domain';
import { addParamsToURL, replaceURLProtocol } from '@tencent/mole-utils-lib';

import { COMPONENT_EMIT_EVENT } from '../constant';
import { type GetSupportGamesResp, RedPacketsWallType } from '../types';

const props = defineProps<{
  type: RedPacketsWallType;
  supportGameInfo?: GetSupportGamesResp;
}>();

const supportGames = computed(() => {
  const { cooperationGames = [], tencentGames = [] } = props.supportGameInfo || {};
  return props.type === RedPacketsWallType.Ly ? cooperationGames : tencentGames;
});
const playingGames = computed(() => {
  const { userCooperationGames = [], userTencentGames = [] } = props.supportGameInfo || {};
  return props.type === RedPacketsWallType.Ly ? userCooperationGames : userTencentGames;
});

const jumpToSupportGames = (type: RedPacketsWallType) => {
  const url = `https://m${utils.isTestEnv() ? '-test' : ''}.iwan.yyb.qq.com/game-private/points-mall/act-games`;
  utils.jumpUrl(addParamsToURL(url, {
    appids: supportGames.value.map(game => game.appid).join(','),
    playing_appids: playingGames.value.map(game => game.appid).join(','),
    game_type: type,
  }));
};

// 监听组件所有内部事件以触发外部事件
emitter.on(COMPONENT_EMIT_EVENT.JumpToJoinGamesPage, jumpToJoinGamesPageByEmitEvent);

// 取消监听事件
onUnmounted(() => {
  emitter.off(COMPONENT_EMIT_EVENT.JumpToJoinGamesPage, jumpToJoinGamesPageByEmitEvent);
});

function jumpToJoinGamesPageByEmitEvent(type: RedPacketsWallType) {
  // 精品游戏不支持事件跳转
  if (props.type === RedPacketsWallType.Jp) return;
  jumpToSupportGames(type);
}
</script>
<style lang="scss" module>
.supportGamesContainer {
  margin-top: 0.11rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.supportGames {
  display: flex;
  overflow-x: hidden;
  height: 0.371rem;
}
.gameIcon {
  flex-shrink: 0;
  width: 0.351rem;
  height: 0.371rem;
  border-radius: 0.09rem;
  border: 0.01rem solid #fff;
  &:not(:last-child) {
    margin-right: 0.039rem;
  }

  img {
    width: 100%;
    height: 100%;
  }
}
.supportGamesCount {
  position: relative;
  flex-shrink: 0;
  display: flex;
  height: 0.371rem;
  justify-content: flex-end;
  align-items: center;
  font-family: Noto Sans SC;
  font-size: 0.10rem;
  font-weight: 500;
  line-height: 0.1448rem;
  color: rgba(112, 45, 0, 1);
  &::after {
    position: absolute;
    right: 100%;
    width: 0.44rem;
    height: 100%;
    content: "";
    opacity: 1;
    pointer-events: none;
    background: linear-gradient(270deg,rgba(255, 226, 203) 20.47%,rgba(255, 226, 203, 0) 100%)
  }
}
</style>
