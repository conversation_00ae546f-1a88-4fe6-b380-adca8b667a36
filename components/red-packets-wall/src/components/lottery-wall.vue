<template>
  <div :class="$style.lotteryWall">
    <lottery-gear
      v-for="(redPacketsGearData, idx) in redPacketsWallData"
      :key="redPacketsGearData.modID"
      :red-packets-gear-data="redPacketsGearData"
      :gear-position="idx"
      :progress="progress"
    />
  </div>
</template>
<script lang="ts" setup>
import type { Progress, RedPacketsWallData } from '../types';
import LotteryGear from './lottery-gear.vue';

defineProps<{
  redPacketsWallData: RedPacketsWallData;
  progress: Progress;
}>();
</script>
<style lang="scss" module>
.lotteryWall {
  display: flex;
}
</style>
