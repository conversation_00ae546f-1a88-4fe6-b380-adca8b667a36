<template>
  <div
    :class="$style.redPacket"
    :style="{ backgroundImage: `url(${backgroundImage})` }"
  >
    <!-- 未开：红包字体 -->
    <img
      v-if="!isOpened"
      :class="$style.redPacketWord"
      :src="redPacketInfo.noOpenablePic"
      alt=""
    >
    <!-- 红包皮封面 -->
    <template v-if="isRedPacketCoverVisible">
      <img
        :class="$style.openImg"
        :src="isCanOpen ? redPacketInfo.openWordPic : redPacketInfo.lockPic"
      >
      <img
        :class="$style.canOpenCover"
        :src="redPacketInfo.canOpenPic"
      >
      <div
        v-if="redPacketInfo.lotteryPoints > 0 && isCanOpen"
        :class="$style.lotteryPoints"
      >
        需要{{ redPacketInfo.lotteryPoints }}积分
      </div>
    </template>
    <!-- 已开且有物品信息 -->
    <div
      v-if="isOpened && redPacketInfo.property"
      :class="[isFallbackReward ? $style.fallbackProperty : $style.property]"
    >
      <template v-if="!isFallbackReward">
        <img
          :src="redPacketInfo.property.instanceInfo.picUrl"
          :class="$style.propertyImg"
          alt=""
        >
        <div :class="$style.name">
          {{ redPacketInfo.property.instanceInfo.name }}
        </div>
        <div
          :class="[$style.btnText, { [$style.disable]: isShowButtonDisable }]"
          @click.stop="handleReceiveBtnClick"
        >
          {{ buttonText }}
        </div>
      </template>
      <template v-else>
        <div :class="$style.fallback">
          新春大吉
        </div>
      </template>
    </div>
    <div
      v-if="isDeliveryHandling"
      :class="$style.deliveryHandlingContainer"
    >
      <div
        :class="$style.deliveryHandlingImage"
        :style="{ backgroundImage: `url(${redPacketInfo.deliveryPic})`}"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, watch } from 'vue';
import { OrderStatus, type PropertyResult } from '@tencent/moka-ui-domain';
import { debounce } from '@tencent/mole-utils-lib';

import { usePacketRewardHook } from '../hooks/use-lottery-packet-reward';
import { RedPacketInfo, RedPacketStatus } from '../types';

const props = defineProps<{
  redPacketInfo: RedPacketInfo;
}>();

const emits = defineEmits<{
  (e: 'refresh'): void;
  (e: 'wxSecondeReceiveSuccess', result?: PropertyResult): void;
}>();

const isOpened = computed(() => props.redPacketInfo.status === RedPacketStatus.OPENED);
const isCanOpen = computed(() => props.redPacketInfo.status === RedPacketStatus.CAN_OPEN);
/** 正在处理发货中：用户已抽奖但是查询因为超时等原因查不回来 */
const isDeliveryHandling = computed(() => isOpened.value && !props.redPacketInfo.property);
const backgroundImage = computed(() => (isOpened.value || isDeliveryHandling.value
  ? props.redPacketInfo.obtainedPic
  : props.redPacketInfo.redPacketBg));
const isRedPacketCoverVisible = computed(() => !isOpened.value && !isDeliveryHandling.value);

const {
  originInfo,
  buttonText,
  isQB,
  orderStatus,
  isFallbackReward,
  isShowButtonDisable,
  handleBtnClick,
} = usePacketRewardHook(props.redPacketInfo.property);

/** 处理按钮点击 */
const handleReceiveBtnClick = debounce(() => {
  handleBtnClick({
    handleAfterClick: (data?: PropertyResult) => {
      // 非 QB 礼包无需额外处理
      if (!isQB.value) {
        return;
      }

      // 订单终态无需刷新
      if ([
        OrderStatus.ORDER_STATUS_DELIVERED_FAILED,
        OrderStatus.ORDER_STATUS_DELIVERED,
      ].includes(orderStatus.value ?? OrderStatus.ORDER_STATUS_UNKNOWN)) {
        return;
      }

      // 非微信二次绑定领取（!data）或者二次领取状态不为发货中则需要请求接口刷新
      if (!data || data.orderDetail.status !== OrderStatus.ORDER_STATUS_DELIVERING) {
        emits('refresh');
      }
    },
    handleWXSecondeReceiveSuccess: (result?: PropertyResult) => {
      emits('wxSecondeReceiveSuccess', result);
    },
    handleWXSecondeReceiveFailed() {
      emits('refresh');
    },
  });
}, 100);

watch(() => props.redPacketInfo.property, (newVal) => {
  originInfo.value = newVal;
});
</script>
<style lang="scss" module>
.redPacket {
  display: flex;
  justify-content: center;
  position: relative;
  font-family: Noto Sans SC;
  width: 0.72rem;
  height: 0.88rem;
  background: linear-gradient(30.65deg, #FECA89 2.01%, #FDE6CF 98.02%);
  border-radius: 0.1rem;
  z-index: 1;

  background-size: 100% 100%;

  &:not(:last-child) {
    margin-bottom: 0.04rem;
  }
}
.redPacketWord {
  position: absolute;
  top: 0.2rem;
  width: 0.4478rem;
  height: 0.4354rem;
}
.canOpenCover {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0.32rem;
}
.lotteryPoints {
  width: 100%;
  text-align: center;
  position: absolute;
  font-size: 0.05rem;
  font-weight: 400;
  bottom: 0.03rem;
  color: rgba(255, 255, 255, 0.9);
}
.openImg {
  position: absolute;
  z-index: 10;
  bottom: 0.13rem;
  width: 0.24rem;
  height: 0.24rem;
}
.property {
  position: absolute;
  bottom: 0.08rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
// 已开物品样式适配
.propertyImg {
  width: 0.3rem;
  height: 0.3rem;
}
.name {
  margin-bottom: 0.02rem;
  width: 0.62rem;
  font-size: 0.12rem;
  font-weight: 500;
  line-height: 0.1738rem;
  text-align: center;
  color: rgba(112, 45, 0, 1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.btnText {
  font-size: 0.1rem;
  font-weight: 500;
  line-height: 0.1448rem;
  text-align: center;
  color: rgba(199, 39, 0, 1);
}
.disable {
  color: rgba(199, 39, 0, 0.3) !important;
}

.fallbackProperty {
  width: 0.72rem;
  height: 0.88rem;
}

.fallback {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: Noto Sans SC;
  font-size: 0.12rem;
  font-weight: 500;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: rgba(112, 45, 0, 1);
}

.deliveryHandlingContainer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.deliveryHandlingImage {
  width: 0.49rem;
  height: 0.31rem;
  background-size: 100% 100%;
}
</style>
