import { type App, createApp, h } from 'vue';
import type { PropertyResult } from '@tencent/moka-ui-domain';

import { getPoints } from '../../api/point';
import LotteryPacketDialog from './dialog.vue';

export interface HandleLotterySuccessResult {
  originResult: {
    // TODO: 此处类型需要下划线转换，domain 为小驼峰，暂时先不做处理
    property_results: any;
  };
}

/** 展示弹窗参数 */
export interface ShowLotteryPacketDialogParams {
  /** 红包背景 */
  redPacketBg: string;
  /** 红包封面 */
  coverFont: string;
  /** 底部遮罩 */
  mask: string;
  /** 开启按钮图片  */
  openButtonImage: string;
  /** 消耗积分 */
  points: number;
  /** 抽奖模块 ID */
  lotteryModID: number;
  /** 组件 ID */
  componentID: string;
  /** 组件类型 */
  componentType: string;
  /** 处理抽奖成功：包括成功和条件不通过, 类型待补充 */
  handleLotterySuccess?: (result: HandleLotterySuccessResult) => Promise<void> | void;
  /** 处理抽奖失败 */
  handleLotteryFailed?: (e: any) => Promise<void> | void;
  /** 处理微信登录 QQ 号绑定逻辑 */
  handleWXSecondeReceiveSuccess?: (result?: PropertyResult) => Promise<void> | void;
  /** 关闭弹窗刷新逻辑 */
  refreshWhenCloseDialog?: () => Promise<void> | void;
}

let lotteryPacketDialogInstance: App<Element> | undefined;
let lotteryPacketDialogContainer: HTMLElement | undefined;

/**
 * 展示红包抽奖弹窗
 * @param params 红包弹窗参数
 */
export async function showLotteryPacketDialog(params: ShowLotteryPacketDialogParams) {
  const container = getLotteryPacketDialogContainer();

  // 确保红包弹窗是单例
  if (lotteryPacketDialogInstance) {
    lotteryPacketDialogInstance.unmount();
  }

  let isUseCommonPoint = true;
  const { points } = params;
  if (points > 0) {
    const { commonPoints = 0, wzPoints = 0 } = await getPoints() || {};
    // 通用积分不足且王者积分足够
    if (commonPoints < points && wzPoints >= points) {
      isUseCommonPoint = false;
    }
  }

  lotteryPacketDialogInstance = createApp({
    render() {
      return h(LotteryPacketDialog, {
        ...params,
        isUseCommonPoint,
        onClose: (needRefresh?: boolean) => {
          enableScroll();
          lotteryPacketDialogInstance?.unmount();
          lotteryPacketDialogInstance = undefined;
          // 需要关闭弹窗后刷新数据
          if (needRefresh) {
            void params.refreshWhenCloseDialog?.();
          }
        },
      });
    },
  });

  disableScroll();
  lotteryPacketDialogInstance?.mount(container);
}

/** 获取红包弹窗容器 */
function getLotteryPacketDialogContainer() {
  // 不存在容器先创建
  if (!lotteryPacketDialogContainer) {
    lotteryPacketDialogContainer = document.createElement('div');
    document.documentElement.appendChild(lotteryPacketDialogContainer);
  }

  return lotteryPacketDialogContainer;
}

/** 禁用 body 滚动，用于解决滚动穿透问题 */
function disableScroll() {
  document.body.style.overflow = 'hidden';
}

/** 重新启用 body 滚动 */
function enableScroll() {
  document.body.style.overflow = '';
}
