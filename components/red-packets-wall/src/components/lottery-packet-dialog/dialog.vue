<template>
  <van-overlay
    class="lottery-packet-overlay"
    z-index="1001"
    :show="isShow"
  >
    <div
      class="dialog-container"
      :style="inYSDK ? { transform: 'scale(0.6)' } : popupStyle"
    >
      <!-- 红包弹窗主体 -->
      <div
        class="lottery-packet"
        :style="backgroundStyle"
      >
        <!-- 红包弹窗奖励展示 -->
        <div
          v-if="packetStatus === PacketStatus.Received"
          class="lottery-packet-reward"
        >
          <div class="lottery-packet-reward-title">
            {{ isFallbackReward ? '感谢参与' : '恭喜获得' }}
          </div>
          <div class="lottery-packet-reward-sub-title">
            {{ isFallbackReward ? '祝你' : '应用宝会员福气红包' }}
          </div>
          <div
            v-if="isFallbackReward"
            class="lottery-packet-reward-fallback"
          />
          <div
            v-if="!isFallbackReward"
            class="lottery-packet-reward-content"
          >
            <span class="num">{{ numText }}</span>
            <span class="uit">{{ uitText }}</span>
          </div>
          <div
            v-if="!isFallbackReward"
            class="lottery-packet-reward-tips"
          >
            <span class="value">{{ valueText }}</span>
            <span>{{ tipsText }}</span>
          </div>
          <div
            class="lottery-packet-reward-button"
            @click="handleReceive"
          >
            {{ buttonText }}
          </div>
        </div>
        <!-- 红包弹窗未拆状态展示 -->
        <div
          v-else
          class="lottery-packet-open"
          :style="{
            backgroundImage: `url(${mask ?? ''})`,
          }"
        >
          <div
            class="lottery-packet-open-button"
            :class="{
              spinning: isDoingLottery
            }"
            :style="{
              backgroundImage: `url(${openButtonImage ?? ''})`,
            }"
            @click="doLottery"
          />
          <div
            v-if="points"
            class="lottery-packet-open-points"
          >
            开启红包需消耗<span style="font-weight: 900;">{{ consumePointText }}</span>
            <div
              class="switch"
              @click="switchPointType"
            >
              切换积分
            </div>
          </div>
        </div>
        <img
          v-if="coverFont && packetStatus !== PacketStatus.Received"
          :src="coverFont"
          class="font-cover"
        >
      </div>
      <!-- 关闭按钮 -->
      <div
        class="close-button"
        @click="handleCloseDialog"
      />
    </div>
  </van-overlay>
</template>

<script setup lang="ts">
import 'vant/es/toast/style';
import 'vant/es/overlay/style';
import { Overlay as VanOverlay, showToast } from 'vant';
import { computed, ref, toRef, watch } from 'vue';
import { Lottery, ParamCollect } from '@tencent/moka-athena-next-v2';
import { type PropertyResult, UserLotteryResultStatus, utils } from '@tencent/moka-ui-domain';
import { camelizeKeys, debounce, isYSDK, safeJsonStringify } from '@tencent/mole-utils-lib';

import { usePacketRewardHook } from '../../hooks/use-lottery-packet-reward';
import { usePopupResize } from '../../hooks/use-popup-resize';
import { PacketStatus } from '../../types';

const props = defineProps<{
  /** 红包背景 */
  redPacketBg: string;
  /** 红包 */
  coverFont: string;
  /** 底部遮罩 */
  mask: string;
  /** 开启按钮图片  */
  openButtonImage: string;
  /** 消耗积分 */
  points: number;
  /** 抽奖模块 ID */
  lotteryModID: number;
  /** 组件 ID */
  componentID: string;
  /** 组件类型 */
  componentType: string;
  /** 是否使用通用积分 */
  isUseCommonPoint: boolean;
  /** 处理抽奖成功：包括成功和条件不通过, 类型待补充 */
  handleLotterySuccess?: (result: any) => Promise<void> | void;
  /** 处理抽奖失败 */
  handleLotteryFailed?: (e: any) => Promise<void> | void;
  /** 处理微信登录 QQ 号绑定逻辑 */
  handleWXSecondeReceiveSuccess?: (result?: PropertyResult) => void;
}>();

const emits = defineEmits<(e: 'close', isNeedRefresh?: boolean) => void>();
const inYSDK = isYSDK(navigator.userAgent);
/** 消耗的积分类型 */
const isUseCommonPoint = toRef(props.isUseCommonPoint);
/** 红包状态 */
const packetStatus = ref(PacketStatus.Lottery);
/** 消耗积分文本 */
const consumePointText = computed(() => `${props.points ?? 0}${isUseCommonPoint.value ? '通用' : '王者'}积分`);
/** 是否展示弹窗 */
const isShow = ref(true);
/** 背景样式 */
const backgroundStyle = computed(() => {
  if (packetStatus.value === PacketStatus.Received) {
    return {
      background: 'linear-gradient(30.65deg, #FFF7EC 2.01%, #FFFBF8 98.02%)',
      borderRadius: '0.24rem',
    };
  }

  return {
    backgroundImage: `url(${props.redPacketBg ?? ''})`,
  };
});

const { popupStyle } = usePopupResize(349);

/** 抽奖结果 */
const lotteryResult = ref<PropertyResult>();

const {
  originInfo,
  isPoint,
  isQB,
  handleBtnClick,
  isFallbackReward,
  uitText,
  numText,
  valueText,
} = usePacketRewardHook(lotteryResult.value);
watch(() => lotteryResult.value, (newVal) => {
  originInfo.value = newVal;
});

const { getLoginType, ELoginType } = utils;
/** 提示文案 */
const tipsText = computed(() => {
  if (isPoint.value) {
    return '可兑换海量豪礼';
  }

  const isWxLogin = getLoginType() === ELoginType.Wx;
  if (isQB.value) {
    // QB 奖励根据微信和 QQ 进行差异化展示处理
    return isWxLogin ? '领取后发放' : '稍后将自动领取';
  }

  return '';
});

/** 是否需要关闭后刷新 */
const isNeedRefreshWhenClose = computed(() => {
  // 非 QB 物品无需关闭时再进行查询，领取成功后已经请求后台接口并填充奖品数据
  if (!isQB.value) return false;
  const isWxLogin = getLoginType() === ELoginType.Wx;

  // 微信登录态的情况不需要关闭弹窗的时候刷新，因为在输入 QQ 号成功后执行领取后奖励弹窗已经关闭，此处会手动更新状态为发货中
  if (isWxLogin) return false;
  return true;
});

/** 按钮文本 */
const buttonText = computed(() => {
  if (isFallbackReward.value) return '收下祝福';
  return isPoint.value ? '去兑换' : '立即领取';
});
/** 是否正在抽奖 */
const isDoingLottery = ref(false);
/** 抽奖 */
const doLottery = async () => {
  if (isDoingLottery.value) return;
  isDoingLottery.value = true;

  const {
    lotteryModID,
    componentID,
    componentType,
    handleLotterySuccess,
    handleLotteryFailed,
  } = props;

  let lotteryErrorCallback;
  try {
    let qualifierParams = [];
    let paramCollect = null;
    paramCollect = new ParamCollect();

    const [
      params,
      isAuth,
      isCancel,
      successCallbacks,
      errorCallbacks,
    ] = await paramCollect.getQualifierParamByModId(lotteryModID, 'DoLottery');

    lotteryErrorCallback = errorCallbacks;
    qualifierParams = params;

    if (!isAuth || isCancel) {
      return;
    }

    // 王者积分需要将参数透传
    if (!isUseCommonPoint.value) {
      qualifierParams.push({
        biz_name: 'yyd',
        param_name: 'appid',
        value: '12127266',
        // @ts-ignore 后台要求特殊王者积分消耗需要携带
        udf_name: 'QueryPoint',
      });
    }

    console.log(`[doLottery] 开始执行红包抽奖，
      modID: ${lotteryModID},
      isUseCommonPoint: ${isUseCommonPoint.value},
      qualifierParams: ${safeJsonStringify(qualifierParams)}`);

    const lottery = Lottery.default.create(lotteryModID, componentID, componentType);
    const result = await lottery.getExecResult(qualifierParams);
    console.log('[doLottery] 执行红包抽奖成功，抽奖结果：', result);
    const { originResult } = result;

    successCallbacks?.forEach((fn) => {
      if (typeof fn === 'function') {
        void fn();
      }
    });

    // 抽中物品已达到限量，关闭弹窗并提示
    if (originResult.status !== UserLotteryResultStatus.WON || !originResult.property_results[0]) {
      console.error('[doLottery] 抽奖异常', result);
      showToast({
        message: '礼包发放异常，请稍后再试或查看活动规则联系客服协助。',
        zIndex: 2000,
      });

      isDoingLottery.value = false;
      void handleLotteryFailed?.(result);
      handleCloseDialog();
      return;
    }

    // 获取抽奖的物品
    lotteryResult.value = camelizeKeys<PropertyResult>(originResult.property_results[0]);
    // 已领取状态
    packetStatus.value = PacketStatus.Received;
    await handleLotterySuccess?.(result);
  } catch (e: any) {
    showToast({
      message: e.tip || e.msg || '礼包发放异常，请稍后再试或查看活动规则联系客服协助。',
      zIndex: 2000,
    });

    console.error('[doLottery] 执行红包抽奖异常', e);

    // 抽奖出现异常需要执行的回调函数
    lotteryErrorCallback?.forEach((fn) => {
      if (typeof fn === 'function') {
        void fn();
      }
    });

    void handleLotteryFailed?.(e);
    // 中奖异常情况需要关闭弹窗
    emits('close', isNeedRefreshWhenClose.value);
  } finally {
    isDoingLottery.value = false;
  }
};

/** 处理领取 */
const handleReceive = debounce(() => {
  isDoingLottery.value = false;
  if (!lotteryResult.value) {
    console.error('[handleReceive] 抽奖奖品信息为空');
    return;
  }

  // 兜底奖励
  if (isFallbackReward.value) {
    handleCloseDialog();
    return;
  }

  handleBtnClick({
    // 按钮点击操作完成后都需要关闭弹窗
    handleAfterClick: handleCloseDialog,
    // 处理微信二次领取刷新逻辑
    handleWXSecondeReceiveSuccess: props.handleWXSecondeReceiveSuccess,
  });
}, 100);

/** 切换积分类型 */
const switchPointType = () => {
  isUseCommonPoint.value = !isUseCommonPoint.value;
  showToast(`已切换至${isUseCommonPoint.value ? '通用' : '王者'}积分`);
};

/** 关闭弹窗 */
const handleCloseDialog = () => {
  // 正在抽奖中不允许关闭弹窗
  if (isDoingLottery.value) return;

  emits('close', isNeedRefreshWhenClose.value);
};

</script>

<style lang="scss" scoped>
@mixin backgroundImageCenter {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.dialog-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.lottery-packet {
  display: flex;
  justify-content: center;
  position: relative;
  width: 2.45rem;
  height: 3rem;
  background-size: 100% 100%;
  .font-cover {
    position: absolute;
    top: 0.54rem;
    width: 1.42rem;
    height: 1.381rem;
  }

  &-overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  &-reward {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 0.22rem 0.2rem 0.18rem;

    &-title {
      font-family: MFFangHei;
      font-size: 0.22rem;
      font-weight: 700;
      line-height: 0.33rem;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: rgba(112, 45, 0, 1);
      margin-bottom: 0.07rem;
    }

    &-sub-title {
      font-family: Noto Sans CJK SC;
      font-size: 0.16rem;
      font-weight: 500;
      line-height: 0.12rem;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: rgba(112, 45, 0, 1);
    }

    &-fallback {
      width: 0.86rem;
      height: 0.88rem;
      margin-top: 0.36rem;
      background-image: url('https://cdn.yyb.gtimg.com/wupload/xy/yybtech/0s6e2F4Z.png');
      @include backgroundImageCenter;
    }

    &-content {
      height: fit-content;
      line-height: 0.68rem;
      margin-top: 0.15rem;

      .num {
        font-family: YYB;
        font-size: 0.68rem;
        font-weight: 400;
        line-height: 0.2356rem;
        text-align: right;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgba(195, 162, 107, 1);
        margin-right: 0.02rem;
      }

      .uit {
        font-family: Noto Sans SC;
        font-size: 0.14rem;
        font-weight: 700;
        line-height: 0.2027rem;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgba(195, 162, 107, 1);
      }
    }

    &-tips {
      margin-top: 0.08rem;
      color: rgba(195, 162, 107, 1);
      font-family: Noto Sans CJK SC;
      font-size: 0.14rem;
      font-weight: 400;
      line-height: 0.2rem;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;

      .value {
        font-weight: 700;
        margin-right: 0.03rem;
      }
    }

    &-button {
      width: 1.86rem;
      height: 0.48rem;
      line-height: 0.48rem;
      border-radius: 0.24rem;
      margin-top: 0.41rem;
      background: linear-gradient(180deg, #FE4A2E 0%, #FF1D1C 100%);
      color: rgba(255, 239, 216, 1);
      font-family: Noto Sans CJK SC;
      font-size: 0.16rem;
      font-weight: 500;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }

    .disable {
      background: linear-gradient(180deg, rgba(254, 74, 46, 0.35) 0%, rgba(255, 29, 28, 0.35) 100%) !important;
      color: rgba(255, 239, 216, 1) !important;
    }
  }

  &-open {
    width: 100%;
    height: 1rem;
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    background-size: 100% 100%;

    &-button {
      position: relative;
      top: -0.19rem;
      width: 0.75rem;
      height: 0.75rem;
      @include backgroundImageCenter;
    }

    &-points {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      bottom: 0.15rem;
      font-family: Noto Sans SC;
      font-size: 0.12rem;
      font-weight: 400;
      line-height: 0.1738rem;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: rgba(253, 224, 192, 1);

      .switch {
        margin-left: 0.02rem;
        text-decoration: underline;
      }
    }
  }
}

.spinning {
  // TODO: 待确认动画速率
  animation: spin 1s linear infinite;
}

.close-button {
  width: 0.25rem;
  height: 0.25rem;
  margin-top: 0.27rem;
  background-image: url('https://cms.myapp.com/xy/yybtech/2XVHfEM9.svg');
  @include backgroundImageCenter;
}

@keyframes spin {
  0% {
    transform: rotateY(0deg);
  }
  100% {
    transform: rotateY(360deg);
  }
}
</style>
