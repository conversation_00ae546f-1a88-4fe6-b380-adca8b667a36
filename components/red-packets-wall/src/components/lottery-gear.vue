<template>
  <div :class="$style.lotteryGear">
    <!-- 顶部标签 每个档位一致 -->
    <div
      :class="$style.tag"
      :style="{backgroundImage: `url(${tag})`}"
    />
    <!-- 圆角三角形 -->
    <img
      v-if="redPacketsGearData.gearRechargeMaxThreshold || inMagic"
      src="https://yyb.qpic.cn/moka-imgs/1736429243912-857760gyudh.svg"
      alt=""
      :class="$style.triangle"
    >
    <red-packet
      v-for="(redPacket, idx) in redPackets"
      :key="redPacket.key"
      :red-packet-info="redPacket"
      dt-eid="red_packet"
      :dt-params="utils.getReportParamsStr({
        rpacket_id:redPacketsGearData.backendLotteryId,
        trpacket_status: String(redPacket.status),
        small_position: String(idx + 1),
      }, inMagic)"
      @click="doLottery(redPacket, idx)"
      @refresh="refresh"
      @wx-seconde-receive-success="handleWXSecondeReceiveSuccess"
    />
    <div :class="$style.threshold">
      <img
        :src="redPacketsGearData.luckPointPic"
        alt=""
      >
      <div>{{ redPacketsGearData.gearRechargeMaxThreshold }}</div>
    </div>
    <div
      v-if="redPacketsGearData.gearRechargeMaxThreshold || inMagic"
      :class="$style.arrow"
    />
  </div>
</template>
<script lang="ts" setup>
import '@tencent/moka-athena-next-v2/dist/style.css';
import emitter from 'tiny-emitter/instance';
import { showDialog } from 'vant';
import { computed, ref, useCssModule } from 'vue';
import { actUtils } from '@tencent/moka-data-core';
import { type GetUserLotteryResp, hooks, lottery, type PropertyResult, UserLotteryResultStatus, utils } from '@tencent/moka-ui-domain';
import { camelizeKeys, deepClone, isUndefined } from '@tencent/mole-utils-lib';

import { saveUserClickPositionWithRetry } from '../api';
import { COMPONENT_EMIT_EVENT, COMPONENT_EVENT, COMPONENT_TYPE } from '../constant';
import { IN_YSDK, ORDER_STATUS_PRIORITY } from '../constant/const';
import { type Progress, type RedPacketInfo, type RedPacketsGearData, RedPacketStatus, RedPacketsWallType } from '../types';
import { HandleLotterySuccessResult, showLotteryPacketDialog } from './lottery-packet-dialog/index';
import RedPacket from './red-packet.vue';

const props = defineProps<{
  redPacketsGearData: RedPacketsGearData;
  gearPosition: number;
  progress: Progress;
}>();

const modId = computed(() => Number(props.redPacketsGearData.modID));
const lotteryResult = ref<GetUserLotteryResp>();
// 红包列表
const redPackets = computed(() => {
  const {
    lotteryTimes,
    lotteryIIDPos,
    noOpenablePics,
    gearRechargeThresholds,
    type,
    obtainedPic,
    redPacketBg,
    openWordPic,
    deliveryPic,
    canOpenPic,
    lockPic,
    gearLotteryPoints,
  } = props.redPacketsGearData;
  const backendPosition = lotteryIIDPos.split(',');
  // 筛选出有效位置：即有物品信息的位置
  const validBackedPosition = backendPosition.filter(item => !!item);
  const validSavePosition = savePosition.value.filter(item => !!item);
  const position = validBackedPosition.length < validSavePosition.length ? savePosition.value : backendPosition;

  const baseStatus = lotteryTimes > 0 ? RedPacketStatus.CAN_OPEN : RedPacketStatus.NOT_SATISFY_OPEN;

  const drawnProperties = deepClone(lotteryResult.value?.propertyResults ?? []);
  const receivedPacket: RedPacketInfo[] = noOpenablePics.map((noOpenablePic, idx) => {
    // 已拆红包：即保存了位置信息
    const hasOpened = Boolean(position[idx]);
    const status = hasOpened ? RedPacketStatus.OPENED : baseStatus;
    const index = drawnProperties.findIndex(item => item.orderDetail.orderId === position[idx]);
    let property;
    if (index !== -1) {
      [property] = drawnProperties.splice(index, 1);
    }

    return ({
      noOpenablePic,
      status,
      property,
      threshold: gearRechargeThresholds[idx],
      type,
      obtainedPic,
      redPacketBg,
      openWordPic,
      deliveryPic,
      canOpenPic,
      lockPic,
      lotteryPoints: gearLotteryPoints,
      key: `${property?.orderDetail.orderId}-${idx}-${gearRechargeThresholds[idx]}-${status}-${type}`,
    });
  });

  // 无剩余抽中商品：已抽中商品数量和已开红包数量一致 直接返回
  if (drawnProperties.length === 0) return receivedPacket;

  // 否则计算没有位置信息的商品
  drawnProperties.forEach((drawnProperty) => {
    // 寻找未拆红包
    const index = receivedPacket.findIndex(item => item.status !== RedPacketStatus.OPENED);
    if (index === -1) return receivedPacket;
    receivedPacket[index] = {
      ...receivedPacket[index],
      status: RedPacketStatus.OPENED,
      property: drawnProperty,
    };
  });

  return receivedPacket;
});

const maxPriceProperty = computed(() => {
  const { properties } = props.redPacketsGearData.lotteryOnline || {};
  return properties?.sort((a, b) => b.property.instanceInfo.price - a.property.instanceInfo.price)?.[0]?.property;
});
const tag = computed(() => (redPackets.value.some(item => item.status !== RedPacketStatus.OPENED)
  ? props.redPacketsGearData.tag
  : props.redPacketsGearData.gearHeadPic));

const { inMagic, loginReady, isLogin } = hooks.useMokaInject();
const init = async () => {
  if (inMagic) return;
  // 三合一登录判断时序有问题 直接通过 cookie 判断
  if (!utils.getLoginUserOpenID()) return;
  const result = await lottery.getUserLotteryResultByModID(String(modId.value));
  setLotteryResult(result);
  console.log(`[获取第${props.gearPosition}档抽奖结果]`, lotteryResult.value);
};

void init();

const style = useCssModule();
async function doLottery(redPacket: RedPacketInfo, redPacketIndex: number) {
  await loginReady;
  // 未登录拉起三合一登录
  if (!isLogin.value) {
    console.log('[doLottery] 用户未登录');
    (window as any).vipCommonLogic?.openLogin();
    return;
  }

  // 判断是否已激活
  const isActivated = await (window as any).vipCommonLogic?.tryActivate?.();
  if (!isActivated) {
    console.log('[doLottery] 用户未激活');
    return;
  }

  // 判断是否已授权
  const isAuthed = await (window as any).vipCommonLogic.tryAuthGame?.();
  if (!isAuthed) {
    console.log('[doLottery] 用户未授权');
    return;
  }

  // 已拆
  if (redPacket.status === RedPacketStatus.OPENED) {
    return;
  }

  // 不满足规则弹窗
  if (redPackets.value[redPacketIndex].status === RedPacketStatus.NOT_SATISFY_OPEN) {
    console.log('[doLottery] 不满足可开状态');

    const isJpGame = redPacket.type === RedPacketsWallType.Jp;
    const confirmButtonText = isJpGame ? '去商城' : '去游戏充值';
    const title = isJpGame ? '福气值不足' : '好运值不足';
    const { currentProgress } = props.progress;
    const openedCount = redPackets.value.filter(item => item.status === RedPacketStatus.OPENED).length;
    const distance = props.redPacketsGearData.gearRechargeThresholds[openedCount] - currentProgress;
    const dialogMessage = isJpGame
      ? `特惠商城再消费${distance}元，可开当前红包（需使用${redPacket.lotteryPoints}积分）\n*本轮最高得【${maxPriceProperty.value?.instanceInfo.name}】`
      : `游戏内再充${distance}元，可开当前红包\n*本轮最高得【${maxPriceProperty.value?.instanceInfo.name}】` ;

    void showDialog({
      title,
      message: dialogMessage,
      confirmButtonText,
      showCancelButton: true,
      cancelButtonText: '知道了',
      cancelButtonColor: 'rgba(15, 15, 15, 0.45)',
      confirmButtonColor: 'rgba(238, 60, 56, 1)',
      className: style.lotteryDialogText,
    }).then(() => {
      // 精品游戏
      if (isJpGame) {
        emitter.emit(COMPONENT_EMIT_EVENT.InnerEvent, COMPONENT_EVENT.NeedAnchorJpZone);
        return;
      }

      // 联运游戏 YSDK 直接关闭弹窗 非 YSDK 跳转二级页
      if (IN_YSDK) return;
      // 跳转联运游戏二级页
      emitter.emit(COMPONENT_EMIT_EVENT.JumpToJoinGamesPage, redPacket.type);
    })
      .catch(() => {
      // 点击了取消按钮
      });

    return;
  }

  void showLotteryPacketDialog({
    redPacketBg: props.redPacketsGearData.redPacketBg,
    coverFont: redPacket.noOpenablePic,
    mask: props.redPacketsGearData.canOpenPic,
    openButtonImage: props.redPacketsGearData.openWordPic,
    points: props.redPacketsGearData.gearLotteryPoints,
    lotteryModID: modId.value,
    componentID: props.redPacketsGearData.id,
    componentType: COMPONENT_TYPE,
    /** 抽奖成功 */
    handleLotterySuccess: async (result: HandleLotterySuccessResult) => {
      console.log('[handleLotterySuccess] 处理抽奖成功结果', result);
      const lotterySuccessResult = result?.originResult?.property_results?.[0];
      const orderID = lotterySuccessResult?.order_detail?.order_id;
      let position = savePosition.value;
      if (orderID) {
        position = await saveLotteryRedpacketPosition(orderID, redPacketIndex);
      }

      emitter.emit(COMPONENT_EMIT_EVENT.InnerEvent, COMPONENT_EMIT_EVENT.RefreshRedPacketsWall, async () => {
        await updateLotteryResult(lotterySuccessResult);
        savePosition.value = position;
      });

      emitter.emit(COMPONENT_EMIT_EVENT.InnerEvent, COMPONENT_EVENT.NeedRefreshPoints);
    },
    /** 抽奖失败 */
    handleLotteryFailed: () => {
      refresh();
    },
    /** 处理 WX 输入 QQ 号成功后本地状态变更 */
    handleWXSecondeReceiveSuccess,
    /** 更新 */
    refreshWhenCloseDialog: () => {
      refresh();
    },
  });
}

/** 刷新微信二次领取 */
async function handleWXSecondeReceiveSuccess(result?: PropertyResult) {
  if (!result || !lotteryResult.value) {
    console.error('[handleWXSecondeReceiveSuccess] 奖品信息为空');
    return;
  }

  const propertyID = result.instanceInfo.iid;
  console.log('[handleWXSecondeReceiveSuccess] propertyID', propertyID);
  const index = lotteryResult.value.propertyResults?.findIndex(res => res.instanceInfo.iid === propertyID);
  // 找不到抽奖信息直接刷新
  if (isUndefined(index) || index === -1) {
    console.log('[handleWXSecondeReceiveSuccess] 未找到对应的中奖信息，直接刷新');
    refresh();
    return;
  }

  // 存在抽奖结果更新抽奖结果
  lotteryResult.value.propertyResults[index].orderDetail.status = result.orderDetail.status;
  console.log('[handleWXSecondeReceiveSuccess] 更新抽奖结果状态', lotteryResult.value.propertyResults[index], result);
}

function refresh() {
  // 刷新抽奖结果
  void init();
  // 刷新红包墙信息
  emitter.emit(COMPONENT_EMIT_EVENT.InnerEvent, COMPONENT_EMIT_EVENT.RefreshRedPacketsWall);
}

/**
 * 更新抽奖结果
 * @param propertyResults 奖励结果
 */
async function updateLotteryResult(propertyResults: any) {
  const lotteryPropertyInfo: PropertyResult = camelizeKeys(propertyResults) ;
  const result = await lottery.getUserLotteryResultByModID(String(modId.value));
  console.log(`[获取第${props.gearPosition}档抽奖结果]: updateLotteryResult`, lotteryResult.value);
  // 获取抽奖结果列表数据失败: 直接把抽中数据填充到墙上
  if (!result) {
    console.error('[updateLotteryResult] 抽奖结果为空或者无数据');
    addPropertyResultDirectly(lotteryPropertyInfo);
    return;
  }

  // 初始数据拉取失败 但是抽奖时拉成功
  if (!lotteryResult.value) {
    const isLotteryPropertyInResult = result.propertyResults
      .some(res => res.orderDetail.orderId === lotteryPropertyInfo?.orderDetail.orderId);

    // 抽奖结果中有当前抽奖数据
    if (isLotteryPropertyInResult) {
      console.info('[updateLotteryResult] 原抽奖结果为空：抽奖时拉到了所有数据-直接填充数据');
      lotteryResult.value = result;
      return;
    }

    // 抽奖结果中无当前抽奖数据
    console.info('[updateLotteryResult] 原抽奖结果为空：且未拉取到当前抽奖数据-组装数据');
    addPropertyResultDirectly(lotteryPropertyInfo);
    return;
  }

  // 判断接口是否更新
  const isUpdated = result.propertyResults.length > lotteryResult.value.propertyResults.length;
  if (isUpdated) {
    console.info('[updateLotteryResult] 接口已更新 直接填充数据');
    setLotteryResult(result);
    return;
  }

  addPropertyResultDirectly(lotteryPropertyInfo);
}

/** 直接把抽奖结果物品添加到列表中 */
function addPropertyResultDirectly(propertyResult: PropertyResult) {
  const propertyID = propertyResult?.instanceInfo.iid;
  console.log('[addPropertyResult] propertyID', propertyID);
  if (!propertyID) return;
  if (!lotteryResult.value) {
    lotteryResult.value = {
      propertyResults: [propertyResult],
      status: UserLotteryResultStatus.UNKNOWN,
    };

    return;
  }

  lotteryResult.value.propertyResults = [
    propertyResult,
    ...(lotteryResult.value.propertyResults ?? []),
  ];

  console.info('[updateLotteryResult] 接口未更新且抽奖物品存在，直接设置', lotteryResult.value.propertyResults);
}

/**
 * 设置抽奖结果
 * @param result 新的抽奖结果
 */
function setLotteryResult(result?: GetUserLotteryResp) {
  // 新的抽奖结果为空
  if (!result) return;

  // 当前值为空，直接设置
  if (!lotteryResult.value?.propertyResults?.length) {
    lotteryResult.value = result;
    return;
  }

  const missingResult: PropertyResult[] = [];
  // 当前值不为空，判断更新
  lotteryResult.value.propertyResults.forEach((propertyResult) => {
    if (!propertyResult?.orderDetail || !result.propertyResults) return;

    const curOrderID = propertyResult.orderDetail.orderId;
    const index = result.propertyResults.findIndex(res => res?.orderDetail?.orderId === curOrderID);
    if (index === -1) {
      console.error('[setLotteryResult] 最新的抽奖结果不存在当前订单信息', curOrderID);
      // 记录并保存原来的结果
      missingResult.push(propertyResult);
      return;
    }

    const newResult = result.propertyResults[index];
    // 按照状态优先级，保留原来的状态，避免回退
    if (ORDER_STATUS_PRIORITY[newResult.orderDetail.status]
      <= ORDER_STATUS_PRIORITY[propertyResult.orderDetail.status]) {
      console.log('[setLotteryResult] 新结果抽奖状态优先级低于当前状态，保持当前状态', newResult.orderDetail.status, propertyResult.orderDetail.status);
      newResult.orderDetail.status = propertyResult.orderDetail.status;
    }
  });

  // 保留结果丢失的结果
  result.propertyResults.push(...missingResult);
  lotteryResult.value = result;
}

const savePosition = ref(['']);
// 保存成功位置信息 idx 表示第几个位置抽中, 对应 idx 存储抽中物品的订单 id
const saveLotteryRedpacketPosition = async (orderID: string, idx: number) => {
  const { lotteryIIDPos, modID } = props.redPacketsGearData;
  const prePosition = lotteryIIDPos ? lotteryIIDPos.split(',') : new Array(3).fill('');
  prePosition[idx] = orderID;
  const position = { [actUtils.getCIIDByModID({ modID })]: prePosition.join(',') };
  console.log(`[saveLotteryRedpacketPosition] 保存第${idx}个红包`, position);

  await saveUserClickPositionWithRetry(position);
  return prePosition;
};

</script>
<style lang="scss" module>
.lotteryGear {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  &:not(:last-child) {
    margin-right: 0.08rem;
  }
  .arrow {
    content: '';
    position: absolute;
    top: 0.39rem;
    left: calc(50% - 0.015rem);
    width: 0.03rem;
    height: 2.407rem;
    background-color: rgba(199, 39, 0, 1);
  }
}

.triangle {
  position: absolute;
  top: 2.777rem;
  width: 0.07rem;
}

.tag {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 0.5rem;
  height: 0.12rem;
  z-index: 10;
  background-size: 100% 100%;
  top: -0.02rem;
}
.threshold {
  margin-top: 0.13rem;
  display: flex;
  align-items: center;
  font-family: Noto Sans CJK SC;
  font-size: 0.12rem;
  font-weight: 700;
  line-height: 0.144rem;
  color: rgba(202, 40, 2, 1);

  img {
    width: 0.1488rem;
    height: 0.1488rem;
  }
}
.lotteryDialogText {
  width: 2.96rem;
  :global(.van-dialog__header) {
    font-size: 0.18rem;
    font-weight: 500;
  }
  :global(.van-dialog__message--has-title) {
    font-family: Noto Sans CJK SC;
    font-size: 0.16rem;
    font-weight: 400;
    line-height: 0.24rem;
    color: #000;
    text-align: left;
    padding-top: 0.13rem;
    padding-left: 0.24rem;
    padding-right: 0.24rem;
  }
  :global(.van-button__text) {
    font-weight: 500;
    font-size: 0.16rem;
  }
}
</style>
