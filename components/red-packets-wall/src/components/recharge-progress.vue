<template>
  <div class="recharge-progress">
    <div class="recharge-progress-bar">
      <div
        v-for="(config, index) in progressConfig"
        :key="index"
        :style="{
          width: `${barWidth(config.width)}`
        }"
        class="recharge-progress-bar-track"
      >
        <!-- 进度位于当前档位展示进度条和游标 -->
        <van-progress
          v-if="showProgressBarIndex === index"
          :pivot-text="`${currentProgress}`"
          stroke-width="0.03rem"
          text-color="rgba: (255, 243, 216, 1)"
          color="rgba(254, 115, 78, 1)"
          track-color="rgba(254, 115, 78, 0.2)"
          :percentage="getProgressPercentage(config.milestone)"
        />
        <!-- 其余仅展示进度条轨道 -->
        <div
          v-else
          class="recharge-progress-bar-fake-track"
          :style="{
            backgroundColor: currentProgress >= config.milestone ? 'rgba(254, 115, 78, 1)' : 'rgba(254, 115, 78, .2)'
          }"
        />
        <!-- 最后一个进度的里程碑无需展示 -->
        <div
          v-if="index !== progressConfig.length -1"
          class="recharge-progress-bar-milestone"
          :class="[ currentProgress >= config.milestone ? 'activated' : 'no-activate']"
        />
        <!-- 另外两档位圆点 -->
        <div
          v-if="config.thresholds?.length"
          class="dot-container"
        >
          <div
            v-for="threshold,idx in config.thresholds"
            :key="idx"
            :class="['dot', currentProgress >= threshold.value ? 'dot-activated' : 'dot-no-activated']"
            :style="{
              left: `calc(${threshold.left}% - 0.03rem)`,
            }"
          />
        </div>
      </div>
    </div>
    <div
      class="recharge-progress-tip"
      @click="handleClick"
    >
      <div class="recharge-progress-tip-title">
        <div :class="['recharge-progress-tip-title-text']">
          {{ tips }}<span v-if="pointDesc">{{ pointDesc }}</span>
        </div>
        <div
          v-show="maxRatePropertyDesc"
          class="expand-button"
          :style="{ transform: `rotate(${isExpand ? 180 : 0}deg)` }"
        />
      </div>
      <div
        v-show="isExpand && maxRatePropertyDesc"
        class="recharge-progress-tip-reward"
      >
        <div class="desc">
          {{ maxRatePropertyDesc }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import 'vant/es/progress/style';
import { Progress as VanProgress } from 'vant';
import { computed, ref } from 'vue';

const props = defineProps<{
  /** 提示信息 */
  tips: string;
  /** 最大概率物品描述 */
  maxRatePropertyDesc: string;
  /** 当前进度 */
  currentProgress: number;
  /** 进度配置 */
  progressConfig: {
    milestone: number;
    /** 进度条宽度 */
    width?: number;
    thresholds?: {
      left: number;
      value: number;
    }[];
  }[];
  pointDesc: string;
}>();

/** 获取进度条百分比 */
const getProgressPercentage = (lastMilestone: number) => {
  if (props.currentProgress > lastMilestone) return 100;
  const beforeMilestone = props.progressConfig[showProgressBarIndex.value - 1]?.milestone ?? 0;
  return ((props.currentProgress - beforeMilestone) / (lastMilestone - beforeMilestone)) * 100;
};

/** 需要展示进度条的索引 */
const showProgressBarIndex = computed(() => {
  const { progressConfig, currentProgress } = props;
  const { length } = progressConfig;
  // 若大于最后一个里程碑展示最后一段
  if (currentProgress >= progressConfig[length - 1].milestone) {
    return length - 1;
  }

  // 返回当前未达到里程碑 index
  return progressConfig.findIndex(config => config.milestone >= currentProgress);
});

/** 宽度占比 */
const barWidth = computed(() => (width = 0) => `${width / 100}rem`);

const isExpand = ref(false);
const handleClick = () => {
  isExpand.value = !isExpand.value;
};
</script>

<style lang="scss" scoped>
.recharge-progress {
  margin-top: 0.063rem;
  width: 3.12rem;
  &-bar {
    width: 100%;
    display: flex;
    align-items: center;
    :deep(.van-progress__pivot) {
      padding: 0.02rem 0.042rem;
      font-family: Noto Sans CJK SC;
      font-size: 0.07rem;
      font-weight: 700;
      line-height: 0.084rem;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      z-index: 6;
    }

    :deep(.van-progress__pivot::after) {
      content: '';
      position: absolute;
      bottom: -0.12rem;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 0.075rem solid transparent; /* 左边透明边框 */
      border-right: 0.075rem solid transparent; /* 右边透明边框 */
      border-bottom: 0.06rem solid rgba(255, 243, 216, 1); /* 底边颜色 */
    }

    &-milestone {
      box-sizing: border-box;
      position: absolute;
      right: -0.005rem;
      top: 50%;
      transform: translateY(-50%);
      z-index: 5;
    }

    &-track {
      width: 0.8rem;
      position: relative;

      .dot-container {
        position: absolute;
        width: calc(100% - 0.09rem);
        display: flex;
        top: -50%;
      }
      .dot {
        position: relative;
        width: 0.06rem;
        height: 0.06rem;
        border-radius: 50%;
        &-activated {
          background-color: rgba(254, 115, 78, 1);
        }
        &-no-activated {
          background-color: rgba(255, 207, 184, 1);
        }
      }
    }

    &-fake-track {
      width: 100%;
      height: 0.03rem;
      border-radius: 0.1rem;
      background: rgba(254, 115, 78, 0.2);
    }

    .activated {
      width: 0.11rem;
      height: 0.11rem;
      background-image: url('https://cms.myapp.com/xy/yybtech/XBZHQmVv.svg');
      background-position: center center;
      background-size: cover;
      background-repeat: no-repeat;
    }

    .no-activate {
      width: 0.09rem;
      height: 0.09rem;
      border-radius: 50%;
      border: 0.02rem solid rgba(246, 197, 192, 1);
      background: rgba(255, 222, 225, 1);
    }
  }

  &-tip {
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    transition: height 0.3s ease; /* 添加过渡效果 */
    border-radius: 0.11rem;
    background: rgba(255, 243, 216, 1);
    margin-top: 0.14rem;
    font-family: Noto Sans SC;
    font-size: 0.12rem;
    font-weight: 500;
    line-height: 0.12rem;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: rgba(112, 45, 0, 1);
    padding: 0.1rem 0.09rem 0.1rem 0.06rem ;

    &-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      &-shrink {
        width: calc(100% - 0.17rem);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &-text {
        line-height: 0.12rem;
        span {
          font-size: 0.06rem;
        }
      }
    }

    &-reward {
      display: flex;
      flex-direction: column;
      margin-top: 0.06rem;
      .desc {
        font-family: Noto Sans SC;
        font-size: 0.12rem;
        font-weight: 400;
        line-height: 0.1738rem;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: rgba(112, 45, 0, 1);
      }
    }
  }
}

.expand-button {
  width: 0.16rem;
  height: 0.16rem;
  background-image: url('https://cms.myapp.com/xy/yybtech/Op2qUbq5.svg');
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
</style>
