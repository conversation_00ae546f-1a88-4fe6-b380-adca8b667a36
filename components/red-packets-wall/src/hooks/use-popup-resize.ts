import { computed, onMounted, onUnmounted, ref } from 'vue';

/** 默认设备宽度 */
const defaultClientWidth = 375;
/** 默认设备高度 */
const defaultClientHeight = 750;

/**
 * 屏幕尺寸变化
 */
export function usePopupResize(height: number) {
  const clientWidth = ref(globalThis.innerWidth);
  const clientHeight = ref(globalThis.innerHeight);
  const popupStyle = computed(() => {
    // 目前只考虑移动端，以 375*750 为基准，以高度的比例为准，避免高度溢出
    const actualHeight = height * clientWidth.value / defaultClientWidth;
    const targetHeight = clientHeight.value * height / defaultClientHeight;

    return {
      transform: `scale(${targetHeight / actualHeight})`,
    };
  });

  const listener = () => {
    clientWidth.value = globalThis.innerWidth;
    clientHeight.value = globalThis.innerHeight;
  };

  onMounted(() => {
    globalThis.addEventListener('resize', listener);
  });

  onUnmounted(() => {
    globalThis.removeEventListener('resize', listener);
  });

  return {
    clientWidth,
    clientHeight,
    popupStyle,
  };
}
