import emitter from 'tiny-emitter/instance';
import { showToast } from 'vant';
import { computed } from 'vue';
import type { PropertyResult } from '@tencent/moka-ui-domain';
import { lottery, OrderStatus, utils } from '@tencent/moka-ui-domain';

import { COMPONENT_EMIT_EVENT, COMPONENT_EVENT, ORDER_DELIVER_FAILED_TIP, ORDER_DELIVERING_TIP } from '../constant';

const { useLotteryRewardInfoHook } = lottery;

/** 根据订单状态展示文本 */
const textByOrderStatus: Record<number, string | undefined> = {
  [OrderStatus.ORDER_STATUS_DELIVERED]: '已领取',
  [OrderStatus.ORDER_STATUS_DELIVERED_FAILED]: '发货失败',
  [OrderStatus.ORDER_STATUS_DELIVERING]: '发货中',
};

export function usePacketRewardHook(info?: PropertyResult) {
  const {
    isQB,
    originInfo,
    isPoint,
    isDelivered,
    orderStatus,
    handleSecondeRelease,
  } = useLotteryRewardInfoHook(info);
  /** 数值 */
  const numText = computed(() => {
    const { name = '' } = originInfo.value?.instanceInfo ?? {};
    const regex = /(\d+)/;
    const match = regex.exec(name);

    // 提取名称中的第一个数值
    return match ? match[0] : '';
  });
  /** 单位 */
  const uitText = computed(() => {
    if (isQB.value) return 'Q币';
    if (isPoint.value) return '积分';
    return '';
  });
  /** 价值文本 */
  const valueText = computed(() => {
    const { price = '' } = originInfo.value?.instanceInfo ?? {};
    return price ? `价值${price}元` : '';
  });
  /** 按钮文本 */
  const buttonText = computed(() => {
    // 积分商品
    if (isPoint.value) {
      return orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED_FAILED ? '发货失败' : '去兑换';
    }

    // QB 商品
    if (isQB) {
      return textByOrderStatus[orderStatus.value ?? OrderStatus.ORDER_STATUS_UNKNOWN] ?? '去领取';
    }

    return '';
  });

  /** 是否为兜底奖励 */
  const isFallbackReward = computed(() => !isQB.value && !isPoint.value);

  /** 领取按钮禁用样式：仅 Q 币已发货需要展示 */
  const isShowButtonDisable = computed(() => {
    if (isQB.value && isDelivered.value) return true;
    return false;
  });

  /** 处理点击 */
  const handleBtnClick = (params?: {
    handleAfterClick?: (result?: PropertyResult) => void;
    handleWXSecondeReceiveSuccess?: (result?: PropertyResult) => (Promise<void> | void);
    handleWXSecondeReceiveFailed?: (result?: PropertyResult) => (Promise<void> | void);
  }) => {
    const {
      handleAfterClick,
      handleWXSecondeReceiveSuccess,
      handleWXSecondeReceiveFailed,
    } = params || {};

    // 积分奖励
    if (isPoint.value) {
      console.log('[handleBtnClick] 积分领取', originInfo.value);
      handleAfterClick?.();
      // 发货失败提示
      if (orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED_FAILED) {
        console.error('[handleBtnClick] 发货失败状态', originInfo.value);
        showToast({
          message: ORDER_DELIVER_FAILED_TIP,
          zIndex: 1000,
        });
      } else {
        // 将积分兑换组件滑动至可视区域
        emitter.emit(COMPONENT_EMIT_EVENT.InnerEvent, COMPONENT_EVENT.NeedScrollToPointExchange);
      }

      return;
    }

    // QB 奖励
    if (isQB.value) {
      // 已发货
      if (isDelivered.value) {
        console.log('[handleBtnClick] 已发货状态', originInfo.value);
        handleAfterClick?.();
        return;
      }

      // 发货失败
      if (orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERED_FAILED) {
        console.error('[handleBtnClick] 发货失败状态', originInfo.value);
        showToast({
          message: ORDER_DELIVER_FAILED_TIP,
          zIndex: 1000,
        });

        handleAfterClick?.();
        return;
      }

      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { getLoginType, ELoginType } = utils;
      const isWxLogin = getLoginType() === ELoginType.Wx;

      // 非微信登录或发货中直接提示发货中状态
      if (!isWxLogin || orderStatus.value === OrderStatus.ORDER_STATUS_DELIVERING) {
        console.log('[handleBtnClick] 发货中状态', originInfo.value);
        showToast({
          message: ORDER_DELIVERING_TIP,
          zIndex: 1000,
        });

        handleAfterClick?.();
        return;
      }

      // 微信登录需要填写 QQ 号
      handleSecondeRelease(
        () => {
          console.log('[handleSecondeReleaseSuccess] 处理微信二次领取填写 QQ 号成功', originInfo.value);
          if (!originInfo.value) return;
          // 手动设置为发货中状态
          originInfo.value.orderDetail.status = OrderStatus.ORDER_STATUS_DELIVERING;
          // TODO: 待去掉
          showToast({
            message: ORDER_DELIVERING_TIP,
            zIndex: 1000,
          });

          // 输入 QQ 号成功后需要更新订单状态
          void handleWXSecondeReceiveSuccess?.(originInfo.value);
          // 点击后回调
          handleAfterClick?.(originInfo.value);
        },
        () => {
          console.error('[handleSecondeReleaseSuccess] 处理微信二次领取填写 QQ 号失败', originInfo.value);
          // 输入 QQ 号成功后需要更新订单状态
          void handleWXSecondeReceiveFailed?.(originInfo.value);
          // 点击后回调
          handleAfterClick?.(originInfo.value);
        },
      );
    }
  };

  return {
    isPoint,
    isQB,
    originInfo,
    isDelivered,
    buttonText,
    uitText,
    valueText,
    numText,
    orderStatus,
    isShowButtonDisable,
    isFallbackReward,
    handleBtnClick,
  };
}
