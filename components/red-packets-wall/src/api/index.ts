import { actExecForwardRequester } from '@tencent/moka-data-core';
import { camelizeKeys, sleep } from '@tencent/mole-utils-lib';

import type { GetRedPacketWallInfoResponse, GetSupportGamesResp } from '../types';

/**
 * 保存用户点击的位置
 * @param position 点击位置
 */
export async function saveUserClickPosition(position: Record<string, string>) {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const resp = await actExecForwardRequester.request({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/SaveUserClickPos',
      data: {
        lottery_iid_pos: position,
      },
    },
  });

  if (resp.code !== 0) {
    console.error(`[saveUserClickPosition] 保存失败，code: ${resp.code}, tip: ${resp.tip}`);
    return false;
  }

  return true;
}

/**
 * 保存抽奖位置相关信息
 * @param position 抽奖模块 位置信息
 * @param retryTimes 重试次数 默认 3 次
 */
export async function saveUserClickPositionWithRetry(
  position: Record<string, string>,
  retryTimes = 3,
) {
  const res = await saveUserClickPosition(position);
  if (res) {
    return;
  }

  let retryCount = 0;
  while (retryCount < retryTimes) {
    await sleep(100);
    const retryRes = await saveUserClickPosition(position);
    retryCount += 1;
    if (retryRes) {
      return;
    }
  }
}

/**
 * 获取红包墙信息
 * @returns 红包墙信息
 */
export async function getRedPacketsWallInfo(): Promise<GetRedPacketWallInfoResponse | undefined> {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const resp = await actExecForwardRequester.request({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.component_plat.lottery.LotteryService/GetRedPacketWallInfo',
      data: {
        activity_id: backendId,
      },
    },
  });

  if (resp.code !== 0 || !resp.body?.data) {
    console.error(`[getRedPacketsWallInfo] 获取红包墙信息失败，code: ${resp.code}, tip: ${resp.tip}`);
    return;
  }

  return camelizeKeys(resp.body?.data);
}

/**
 * 获取参与游戏（包含在玩游戏）
 * @returns 参与游戏信息
 */
export async function getSupportGames(): Promise<GetSupportGamesResp | undefined> {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const resp = await actExecForwardRequester.request({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.activity.yyb_point.UserRecharge/GetPointActivityGames',
      data: {
        activity_id: backendId,
      },
    },
  });

  if (resp.code !== 0 || !resp.body?.data) {
    console.error(`[getSupportGames] 获取参与游戏失败，code: ${resp.code}, tip: ${resp.tip}`);
    return;
  }

  return camelizeKeys(resp.body?.data);
}
