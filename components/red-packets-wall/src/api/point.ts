import { actExecForwardRequester } from '@tencent/moka-data-core';
import { getCookies } from '@tencent/mole-utils-lib';

import { WZ_APPID } from '../constant/const';

/** 获取会员积分 */
export async function getPoints() {
  const { activity_iid: backendId } = (window as any).mappingInfo || {};
  const { openid, mobileqopenid, yyb_openid: yybOpenid } = getCookies(document.cookie);
  const commid = yybOpenid || mobileqopenid || openid || '';
  const { code, tip, body } = await actExecForwardRequester.request<unknown, {
    ret_code: number;
    ret_msg: string;
    balance: number;
    quota: Record<string, number>;
  }>({
    activity_iid: backendId,
    invocation: {
      name: '/trpc.private_domain.user_point_svr.UserPointSvr/GetUserPointBalance',
      data: {
        commid,
        source: 0, // 0 为由后台判断返回专区积分还是游游多余额
      },
    },
  });

  if (code !== 0 || !body?.data) {
    console.error('[getPoints] 获取积分接口异常', code, tip);
    return;
  }

  const { ret_code: retCode, ret_msg: retMsg, balance, quota = {} } = body.data;
  if (retCode !== 0) {
    console.error('[getPoints] 获取积分业务异常', retCode, retMsg);
    return;
  }

  const wzPoints = quota[WZ_APPID] || 0; // 王者积分

  return {
    commonPoints: Number(balance || 0),
    wzPoints: Number(wzPoints || 0),
  };
}
