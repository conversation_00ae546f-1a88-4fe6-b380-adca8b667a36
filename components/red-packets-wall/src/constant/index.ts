/** 组件内部方法，提供外部调用 */
export const COMPONENT_METHODS = {
  /** 抽奖 */
  DoLottery: 'doLottery',
};

/** 组件内抛出事件 */
export const COMPONENT_EVENT = {
  /** 更改到精品 tab */
  ChangeJpTab: 'redPacketsWall:changeJpTab',
  /** 更改到联运 tab */
  ChangeLyTab: 'redPacketsWall:changeLyTab',
  /** 精品游戏助力抽奖成功 */
  JPBoostedLotterySucceed: 'redPacketsWall:jpBoostedLotterySucceed',
  /** 联运游戏助力抽奖成功 */
  LYBoostedLotterySucceed: 'redPacketsWall:lyBoostedLotterySucceed',
  /** 抽奖失败 */
  LotteryFail: 'redPacketsWall:lotteryFail',
  /** 不满足抽奖条件需要拉规则弹窗 */
  NeedShowRule: 'redPacketsWall:needShowRule',
  /** 不满足精品抽奖条件需要拉规则弹窗 */
  NeedShowJpRule: 'redPacketsWall:needShowJpRule',
  /** 不满足联运抽奖条件需要拉规则弹窗 */
  NeedShowLyRule: 'redPacketsWall:needShowLyRule',
  /** 不满足精品抽奖条件需要锚定 */
  NeedAnchorJpZone: 'redPacketsWall:NeedAnchorJpZone',
  /** 积分兑换-内部锚定 */
  NeedScrollToPointExchange: 'redPacketsWall:NeedScrollToPointExchange',
  /** 积分兑换-精品锚定 */
  NeedScrollToPointExchangeByJp: 'redPacketsWall:NeedScrollToPointExchangeByJp',
  /** 积分兑换-联运锚定 */
  NeedScrollToPointExchangeByLy: 'redPacketsWall:NeedScrollToPointExchangeByLy',
  /** 抽奖成功刷新积分余额 */
  NeedRefreshPoints: 'redPacketsWall:NeedRefreshPoints',
};

/** 组件内部事件 */
export const COMPONENT_EMIT_EVENT = {
  /** 刷新红包墙 */
  RefreshRedPacketsWall: 'refreshRedPacketsWall',
  /** 执行红包抽奖 */
  DoRedPacketLottery: 'doRedPacketLottery',
  /** 组件内部事件触发 */
  InnerEvent: 'redPacketsWall:innerEvent',
  /** 跳转参与游戏二级页 */
  JumpToJoinGamesPage: 'redPacketsWall:jumpToJoinGamesPage',
};

export const COMPONENT_TYPE = 'moka-ui-red-packets-wall';

/** 订单发货中状态提示文案 */
export const ORDER_DELIVERING_TIP = '【领取成功】到账可能存在延迟，稍后可前往QQ钱包查看';

/** 订单发货失败状态提示文案 */
export const ORDER_DELIVER_FAILED_TIP = '【发货异常】 您可通过页面【活动规则］联系客服协助处理';
