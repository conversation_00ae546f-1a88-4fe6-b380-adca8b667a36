import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

import { COMPONENT_EVENT, COMPONENT_METHODS } from '../src/constant';

const noOpenColumnSetting = {
  column2: {
    type: 'void',
    'x-component': 'ArrayTable.Column',
    'x-component-props': {
      title: '不可拆封第一张',
    },
    properties: {
      noOpenableFirstPic: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-validator': [{ required: true, message: '请上传不可拆封状态图片' }],
      },
    },
  },
  column3: {
    type: 'void',
    'x-component': 'ArrayTable.Column',
    'x-component-props': {
      title: '不可拆封第二张',
    },
    properties: {
      noOpenableSecondPic: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-validator': [{ required: true, message: '请上传不可拆封状态图片' }],
      },
    },
  },
  column4: {
    type: 'void',
    'x-component': 'ArrayTable.Column',
    'x-component-props': {
      title: '不可拆封第三张',
    },
    properties: {
      noOpenableThirdPic: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        'x-validator': [{ required: true, message: '请上传不可拆封状态图片' }],
      },
    },
  },
};

const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        redpacketsConfig: {
          type: 'string',
          title: '红包活动配置',
          'x-decorator': 'FormItem',
          'x-component': 'TextLink',
          'x-component-props': {
            linkTitle: '无极配置跳转',
            linkStrOrFunc(curValue: string) {
              const appid = location.pathname.includes('prod') ? 'activityPageEdit' : 'activityPageEdit_test';
              const encodeFilterArr = curValue.trim()
                ? encodeURIComponent(`[{"id":"id","s":"${curValue}","o":"%%","t":100}]`) : encodeURIComponent('[]');

              return `https://wuji.woa.com/p/edit?appid=${appid}&schemaid=t_vip_red_packets_wall&sort=&order=&filterArr=${encodeFilterArr}&filterPreset=default`;
            },
          },
        },
        voidTabTitlesBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            optional: false,
            title: 'tab 标题',
          },
          properties: {
            tabTitles: {
              type: 'array',
              'x-component': 'ArrayItems',
              items: {
                type: 'void',
                'x-component': 'Space',
                properties: {
                  input: {
                    type: 'object',
                    properties: {
                      title: {
                        type: 'string',
                        title: '{{"tab" + (parseInt($self.index) + 1)+ "主标题"}}',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                      },
                      subTitle: {
                        type: 'string',
                        title: '{{"tab" + (parseInt($self.index) + 1) + "副标题"}}',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        voidTipsConfig: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            optional: false,
            title: '提示语配置',
          },
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '提示配置',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  properties: {
                    'tipConfig.jpTip': {
                      type: 'string',
                      title: '精品提示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: 'Tips:前往「特惠商城」在以上游戏任意消费，即可1:1积攒福气值',
                    },
                    'tipConfig.lyTip': {
                      type: 'string',
                      title: '联运提示',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                      default: '*在以上「游戏内」完成充值，可1:1积攒好运值',
                    },
                  },
                },
              },
            },
          },
        },
        voidGearHeadBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            optional: false,
            title: '档位头部全部已领图片',
          },
          properties: {
            gearHead: {
              type: 'string',
              title: '档位标签背景图',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-validator': [{ required: true, message: '请上传档位头部全部已领背景图' }],
            },
          },
        },
        voidRedPacketBgBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            optional: false,
            title: '档位红包背景板图片',
          },
          properties: {
            redPacketBgs: {
              type: 'array',
              'x-component': 'ArrayItems',
              items: {
                type: 'void',
                'x-component': 'Space',
                properties: {
                  input: {
                    type: 'string',
                    title: '{{"第" + (parseInt($self.index) + 1) + "档"}}',
                    'x-decorator': 'FormItem',
                    'x-component': 'Upload',
                  },
                },
              },
            },
          },
        },
        voidCanOpenPicsBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            optional: false,
            title: '可拆封状态图片',
          },
          properties: {
            canOpenPics: {
              type: 'array',
              'x-component': 'ArrayItems',
              items: {
                type: 'void',
                'x-component': 'Space',
                properties: {
                  input: {
                    type: 'string',
                    title: '{{"第" + (parseInt($self.index) + 1) + "档"}}',
                    'x-decorator': 'FormItem',
                    'x-component': 'Upload',
                    // TODO: 待排查校验问题
                    // 'x-validator': [{ required: true, message: '请上传可拆封状态图片' }],
                  },
                },
              },
            },
          },
        },
        /** 关联精品游戏抽奖后端模块配置 */
        voidJpLotteryBackModsBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '精品游戏抽奖后端模块',
          },
          properties: {
            jpLotteryBackMods: {
              type: 'array',
              maxItems: 4,
              'x-decorator': 'FormItem',
              'x-component': 'ArrayTable',
              'x-component-props': {
                emptyText: '暂无数据',
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '抽奖后端模块关联',
                    },
                    properties: {
                      modID: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'ModSelect',
                        'x-component-props': {
                          modType: 11,
                        },
                        'x-validator': [{ required: true, message: '请选择抽奖后端模块' }],
                      },
                    },
                  },
                  columnTag: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '未拆标签',
                    },
                    properties: {
                      tag: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Upload',
                        'x-validator': [{ required: true, message: '请上传未拆封标签状态图片' }],
                      },
                    },
                  },
                  ...noOpenColumnSetting,
                  column5: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '操作',
                      prop: 'operations',
                      width: 50,
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  title: '新增',
                },
              },
            },
          },
        },
        /** 关联联运游戏抽奖后端模块配置 */
        voidLyLotteryBackModsBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            title: '联运游戏抽奖后端模块',
          },
          properties: {
            lyLotteryBackMods: {
              type: 'array',
              maxItems: 4,
              'x-decorator': 'FormItem',
              'x-component': 'ArrayTable',
              'x-component-props': {
                emptyText: '暂无数据',
              },
              items: {
                type: 'object',
                properties: {
                  column1: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '抽奖后端模块关联',
                    },
                    properties: {
                      modID: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'ModSelect',
                        'x-component-props': {
                          modType: 11,
                        },
                        'x-validator': [{ required: true, message: '请选择抽奖后端模块' }],
                      },
                    },
                  },
                  columnTag: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '未拆标签',
                    },
                    properties: {
                      tag: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Upload',
                        'x-validator': [{ required: true, message: '请上传未拆封标签状态图片' }],
                      },
                    },
                  },
                  ...noOpenColumnSetting,
                  column5: {
                    type: 'void',
                    'x-component': 'ArrayTable.Column',
                    'x-component-props': {
                      title: '操作',
                      prop: 'operations',
                      width: 50,
                      fixed: 'right',
                    },
                    properties: {
                      item: {
                        type: 'void',
                        'x-component': 'FormItem',
                        properties: {
                          remove: {
                            type: 'void',
                            'x-component': 'ArrayTable.Remove',
                          },
                        },
                      },
                    },
                  },
                },
              },
              properties: {
                add: {
                  type: 'void',
                  'x-component': 'ArrayTable.Addition',
                  title: '新增',
                },
              },
            },
          },
        },
        voidOtherSettingBlock: {
          type: 'void',
          'x-component': 'OptionalBlock',
          'x-component-props': {
            optional: false,
            title: '其他配置项',
          },
          properties: {
            obtainedPic: {
              type: 'string',
              title: '已领取状态背景板',
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-decorator-props': {
                labelWrap: true,
              },
              'x-validator': [{ required: true, message: '请上传已领取状态背景板' }],
            },
            openWordPic: {
              type: 'string',
              title: '"开" 字背景图',
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-validator': [{ required: true, message: '请上传"开" 字背景图' }],
            },
            luckPointPic: {
              type: 'string',
              title: '福气值背景图',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-validator': [{ required: true, message: '请上传福气值背景图' }],
            },
            luckPointLyPic: {
              type: 'string',
              title: '好运值背景图',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-validator': [{ required: true, message: '请上传好运值背景图' }],
            },
            deliveryPic: {
              type: 'string',
              title: '发货中背景图',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-validator': [{ required: true, message: '请上传发货中背景图' }],
            },
            lockPic: {
              type: 'string',
              title: '未解锁背景图',
              required: true,
              'x-decorator': 'FormItem',
              'x-component': 'Upload',
              'x-validator': [{ required: true, message: '请上传未解锁背景图' }],
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [
      {
        label: '切换到精品 tab',
        value: COMPONENT_EVENT.ChangeJpTab,
        desc: '红包墙切换到精品 tab 变更触发其他 tab 组件变更',
      },
      {
        label: '切换到联运 tab',
        value: COMPONENT_EVENT.ChangeLyTab,
        desc: '红包墙切换到联运 tab 变更触发其他 tab 组件变更',
      },
      {
        label: '不满足精品抽奖条件拉规则弹窗',
        value: COMPONENT_EVENT.NeedShowJpRule,
        desc: '不满足精品抽奖条件拉规则弹窗',
      },
      {
        label: '不满足联运抽奖条件拉规则弹窗',
        value: COMPONENT_EVENT.NeedShowLyRule,
        desc: '不满足联运抽奖条件拉规则弹窗',
      },
      {
        label: '不满足精品抽奖条件锚定精品商城区域',
        value: COMPONENT_EVENT.NeedAnchorJpZone,
        desc: '不满足精品抽奖条件锚定精品商城区域',
      },
      {
        label: '积分兑换-精品锚定',
        value: COMPONENT_EVENT.NeedScrollToPointExchangeByJp,
        desc: '积分兑换-精品锚定',
      },
      {
        label: '积分兑换-联运锚定',
        value: COMPONENT_EVENT.NeedScrollToPointExchangeByLy,
        desc: '积分兑换-联运锚定',
      },
      {
        label: '精品游戏助力抽奖成功',
        value: COMPONENT_EVENT.JPBoostedLotterySucceed,
        desc: '用于更新助力抽奖状态',
      },
      {
        label: '联运游戏助力抽奖成功',
        value: COMPONENT_EVENT.LYBoostedLotterySucceed,
        desc: '用于更新助力抽奖状态',
      },
      {
        label: '抽奖失败',
        value: COMPONENT_EVENT.LotteryFail,
        desc: '用于打开活动规则弹窗',
      },
      {
        label: '抽奖成功刷新积分',
        value: COMPONENT_EVENT.NeedRefreshPoints,
        desc: '用于抽奖成功刷新积分',
      },
    ],
    /** 组件内部方法，提供外部调用 */
    methods: [
      {
        label: '抽奖',
        value: COMPONENT_METHODS.DoLottery,
        desc: '点击助力模块抽奖后发起抽奖事件',
      },
    ],
  },
};

export default meta;
