import type { ComponentConfig, IPublicComponentReportSchema } from '@tencent/moka-schema/dist/component-meta-types';
import { BuiltInReportEvent } from '@tencent/mole-report';

export default function getReportConfig(componentConfig: ComponentConfig): IPublicComponentReportSchema {
  return {
    schema: {
      // 自定义事件
      publicRules: [],
      nodes: [{
        description: '点位描述',
        rule: {
          events: [BuiltInReportEvent.Exposure],
          selector: `.${componentConfig.id}`,
          data: {
            action: BuiltInReportEvent.Exposure,
            pointName: '点位名称',
          },
        },
      }],
    },
  };
}
