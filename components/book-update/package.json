{"name": "@tencent/moka-ui-book-update", "version": "0.0.3", "description": "活动中台组件模板", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "scripts": {"dev": "vite"}, "author": "junfengchen<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-book-update"], "repository": {"type": "git", "url": "", "directory": "components/book-update"}, "dependencies": {"@tencent/moka-athena-next-v2": "latest", "@tencent/moka-data-core": "latest", "@tencent/moka-ui-domain": "^0.0.7", "@tencent/mole-jsbridge": "^1.0.4", "@tencent/mole-report": "^1.0.0", "radash": "^12.1.0", "vant": "^4.6.8", "vue": "^3.3.4"}, "devDependencies": {"@formily/core": "^2.3.2", "husky": "^8.0.3", "sass": "^1.43.4", "turbo": "^1.5.6"}}