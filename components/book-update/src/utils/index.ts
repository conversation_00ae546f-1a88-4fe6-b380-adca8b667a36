import { showConfirmDialog } from 'vant';
import { utils } from '@tencent/moka-ui-domain';

/**
 * h5 模态模态弹框，支持 await 等待弹框关闭
 * @param params VantDialog 参数
 * @param callback 业务会调函数
 * @returns Promise<void>
 */
export const showDialog = async (
  params: Record<string, unknown>,
  callback: (resolve: () => void) => void,
) => new Promise<void>((resolve) => {
  showConfirmDialog({
    ...params,
    width: '2.96rem',
    className: 'book-update-dialog',
  }).then(async () => {
    callback(resolve);
  })
    .catch(() => {
      resolve();
    });
});

/**
 * 检查应用宝版本是否大于指定版本号
 * @param versionCode 指定版本号
 * @returns 是否符合
 */
export function checkYybVersionGreaterThanVersionCode(versionCode: number) {
  const yybVersion = ((/yyb_version\/(\d+)\//i.exec(navigator.userAgent)) || [])[1];
  if (parseInt(yybVersion, 10) >= versionCode) return true;
  return false;
}

/**
 * 活动回流领取页 url
 * @param params 页面参数
 * @returns
 */
export function getObtainUrl(params: Record<string, string>) {
  let host = 'm.iwan.yyb.qq.com';
  if (utils.isTestEnv()) {
    host = 'm-test.iwan.yyb.qq.com';
  }

  return `https://${host}/game-private/points-mall/receive-gifts?${new URLSearchParams(params)}`;
}

/**
 * JSON 序列化
 * @param {String} str JSON 字符串
 * @returns 对象
 */
export function parseJSON<T>(str: string): T | null {
  try {
    return JSON.parse(str);
  } catch (err) {
    return null;
  }
}

/**
 * JSON 反序列化
 * @param {Object} obj 对象
 * JSON 字符串
 */
export function jsonStringify<T>(obj: T): string {
  try {
    return JSON.stringify(obj);
  } catch (err) {
    return '';
  }
}
