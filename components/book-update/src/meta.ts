import { type Field } from '@formily/core';
import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

/**
 * 状态文案和背景图联动校验 —— 文案和背景图是必须配一个
 * @param field 字段
 * @param linkageField 联动字段名
 */
const textBgImageValidator = (field: Field, linkageField: string) => {
  const curField = field;
  if (!field.value && !field.query(linkageField).value()) {
    curField.selfErrors = ['文案和背景图是必须配一个'];
  } else {
    curField.selfErrors = [];
  }
};

/**
 * 表单配置协议
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      appInfo: {
        type: 'string',
        title: '应用宝游戏app',
        'x-decorator': 'FormItem',
        'x-validator': [{ required: true, message: '请选择游戏' }],
        'x-component': 'AppSelect',
        'x-decorator-props': {
          labelWrap: true,
          extra: '此功能需要配置同一个appId的下载组件，并打开「开启换包」',
        },
        'x-component-props': {
          bindType: 'appInfo',
        },
      },
      previewStatus: {
        type: 'number',
        title: '预览组件状态',
        default: 'unBook',
        enum: [
          {
            label: '未预约',
            value: 'unBook',
          },
          {
            label: '已预约',
            value: 'book',
          },
          {
            label: '未领取',
            value: 'unObtain',
          },
          {
            label: '已领取',
            value: 'obtain',
          },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Radio.Group',
      },
      voidBookTimeBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '预约期配置',
        },
        properties: {
          voidUnBookBlock: {
            type: 'void',
            'x-component': 'OptionalBlock',
            'x-component-props': {
              optional: false,
              title: '未预约状态配置',
            },
            properties: {
              unBookTitle: {
                type: 'string',
                title: '按钮文案',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-reactions': field => textBgImageValidator(field, 'unBookBgImage'),
              },
              unBookBgColor: {
                type: 'string',
                title: '背景颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
              unBookBgImage: {
                type: 'string',
                title: '背景图',
                'x-decorator': 'FormItem',
                'x-component': 'Upload',
                'x-component-props': {
                  drag: true,
                  lockImgNorm: {
                    width: 0.5,
                    height: 0.5,
                  },
                },
                'x-reactions': field => textBgImageValidator(field, 'unBookTitle'),
              },
              unBookFontColor: {
                type: 'string',
                title: '文案颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
            },
          },
          voidBookBlock: {
            type: 'void',
            'x-component': 'OptionalBlock',
            'x-component-props': {
              optional: false,
              title: '已预约状态配置',
            },
            properties: {
              bookTitle: {
                type: 'string',
                title: '按钮文案',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-reactions': field => textBgImageValidator(field, 'bookBgImage'),
              },
              bookBgColor: {
                type: 'string',
                title: '背景颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
              bookBgImage: {
                type: 'string',
                title: '背景图',
                'x-decorator': 'FormItem',
                'x-component': 'Upload',
                'x-component-props': {
                  drag: true,
                  lockImgNorm: {
                    width: 0.5,
                    height: 0.5,
                  },
                },
                'x-reactions': field => textBgImageValidator(field, 'bookTitle'),
              },
              bookFontColor: {
                type: 'string',
                title: '文案颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
            },
          },
        },
      },
      voidUpdateTimeBlock: {
        type: 'void',
        'x-component': 'OptionalBlock',
        'x-component-props': {
          optional: false,
          title: '更新期配置',
        },
        properties: {
          voidUnObtainBlock: {
            type: 'void',
            'x-component': 'OptionalBlock',
            'x-component-props': {
              optional: false,
              title: '未领取状态配置',
            },
            properties: {
              unObtainTitle: {
                type: 'string',
                title: '按钮文案',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-reactions': field => textBgImageValidator(field, 'unObtainBgImage'),
              },
              unObtainBgColor: {
                type: 'string',
                title: '背景颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
              unObtainBgImage: {
                type: 'string',
                title: '背景图',
                'x-decorator': 'FormItem',
                'x-component': 'Upload',
                'x-component-props': {
                  drag: true,
                  lockImgNorm: {
                    width: 0.5,
                    height: 0.5,
                  },
                },
                'x-reactions': field => textBgImageValidator(field, 'unObtainTitle'),
              },
              unObtainFontColor: {
                type: 'string',
                title: '文案颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
            },
          },
          voidObtainBlock: {
            type: 'void',
            'x-component': 'OptionalBlock',
            'x-component-props': {
              optional: false,
              title: '已领取状态配置',
            },
            properties: {
              obtainTitle: {
                type: 'string',
                title: '按钮文案',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-reactions': field => textBgImageValidator(field, 'obtainBgImage'),
              },
              obtainBgColor: {
                type: 'string',
                title: '背景颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
              obtainBgImage: {
                type: 'string',
                title: '背景图',
                'x-decorator': 'FormItem',
                'x-component': 'Upload',
                'x-component-props': {
                  drag: true,
                  lockImgNorm: {
                    width: 0.5,
                    height: 0.5,
                  },
                },
                'x-reactions': field => textBgImageValidator(field, 'obtainTitle'),
              },
              obtainFontColor: {
                type: 'string',
                title: '文案颜色',
                'x-decorator': 'FormItem',
                'x-component': 'ColorPocker',
              },
            },
          },
        },
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
