import { Ref } from 'vue';
import { isAuthYyb } from '@tencent/moka-athena-next-v2';
import { getStateByID } from '@tencent/moka-data-core';
import { hooks } from '@tencent/moka-ui-domain';

import {
  HAS_OBTAIN,
  UPDATE_YYB_VERSION_URL,
  YYB_VERSION_876,
} from '../constant';
import {
  type BookUpdateInfo,
  ChangePackageState,
  type Config,
  DownloadState,
  type DownloadStateInfo,
  GiftType,
} from '../types';
import {
  checkYybVersionGreaterThanVersionCode,
  getObtainUrl,
  showDialog,
} from '../utils';

/**
 * 更新期领取逻辑
 * @param bookUpdateInfo 预约更新信息
 * @param config 组件配置
 * @param yybMoleJSBridge js桥
 * @returns
 */
export function useUpdate(
  bookUpdateInfo: Ref<BookUpdateInfo | undefined>,
  config: Ref<Config>,
  // @tencent/mole-jsbridge/yyb类型导出不生效，临时 any
  yybMoleJSBridge: any,
) {
  const { toast, app } = hooks.useMokaInject();

  /** 接口物品类型和回流领取页的礼包类型映射 */
  const ObtainGiftType = {
    /** 仅礼包 */
    [GiftType.AMS]: 2,
    /** 仅Q币 */
    [GiftType.QB]: 1,
  };

  /**
   * 授权检查
   */
  const isAuth = async () => {
    const authInfos = [{
      authInfo: {
        game_data: {
          detail: {
            // GAME_TYPE_H5 = 5, 无用数据仅为满足ts校验
            type: 5,
            mobile_game: {
              yyb_appid: config.value.appInfo.appId,
            },
          },
        },
        type: 2,
      },
    }];
    return isAuthYyb(authInfos);
  };

  /**
   * 获取游戏包的渠道号和版本号
   * @param pkgName 游戏包名
   * @returns 游戏包渠道和版本信息
   */
  async function getChannelIdInfo(pkgName: string): Promise<{
    pkgVersion: number;
    channel: string;
  }> {
    let pkgVersion = 0;
    let channel = '';
    try {
      const res = await yybMoleJSBridge.call('getChannelIdInfo', { packagenames: pkgName });
      if (!res?.data?.[pkgName]) {
        throw res;
      }

      const { channeldId, verCode } = res.data[pkgName];
      pkgVersion = verCode || 0;
      channel = channeldId || '';
    } catch (error) {
      console.error(`[moka-ui-book-update] yybMoleJSBridge getChannelIdInfo error: ${error}`);
    }

    return {
      pkgVersion,
      channel,
    };
  }

  /**
   * 执行领取
   * @param callBack 拉去领取浮层之后的回调
   */
  const doObtaining = async (callBack: () => void) => {
    if (bookUpdateInfo.value?.isObained) {
      toast?.(HAS_OBTAIN);
      return;
    }

    if (!checkYybVersionGreaterThanVersionCode(YYB_VERSION_876)) {
      await showDialog(
        {
          message: '更新应用宝后，可领取奖励',
          confirmButtonText: '更新应用宝',
        },
        (resolve) => {
          yybMoleJSBridge.call('openNewWindow', { url: UPDATE_YYB_VERSION_URL });
          resolve();
        },
      );

      return;
    }

    const curDownloadState = getDownloadState();
    if (!curDownloadState) return;

    // 下载条件：常规下载状态 !== 可更新、已安装
    if (
      ![DownloadState.Update, DownloadState.Installed].includes(curDownloadState.state)
    ) {
      // 弹窗引导游戏"下载"
      await showDialog(
        {
          message: '下载游戏并登录后，可领取奖励',
          confirmButtonText: '下载游戏',
        },
        (resolve) => {
          doDownload();
          resolve();
        },
      );

      return;
    }

    // 更新条件：常规下载状态 === 可更新  || （常规下载状态 === 已安装 && 需要换包）
    if (
      curDownloadState.state === DownloadState.Update
      || (curDownloadState.state === DownloadState.Installed
        && curDownloadState.isNeedChangePackage)
    ) {
      // 弹窗引导游戏"更新"
      await showDialog(
        {
          message: '更新游戏并登录后，可领取奖励',
          confirmButtonText: '更新游戏',
        },
        (resolve) => {
          doDownload();
          resolve();
        },
      );

      return;
    }

    const authResult = await isAuth();
    if (!authResult) {
      return;
    }

    const { giftType, giftValue, taskID } = bookUpdateInfo.value!;
    const {
      appInfo: { appId, appName, pkgName },
    } = config.value;
    const { channel } = await getChannelIdInfo(pkgName);

    const obtainUrlParams = {
      gift_type: ObtainGiftType[giftType]?.toString() || '',
      qqb_value: giftValue.toString() || '',
      send_code: '0',
      task_id: taskID,
      appid: appId,
      app_name: appName,
      channel_id: channel,
      pkg_name: pkgName,
      report_context: JSON.stringify({
        uni_card_title_name: '',
        uni_cardid: '',
        uni_related_appid: '',
        uni_task_detail: '',
        uni_task_id: '',
        uni_task_type_id: '',
      }),
      recommendid: '',
      sourcescene: '10788',
      sourcemodeltype: '',
      sourcesceneslotid: '',
    };

    callBack();
    const obtainUrl = getObtainUrl(obtainUrlParams);
    location.href = `tmast://transwebviewV2?url=${encodeURIComponent(obtainUrl)}`;
  };

  /**
   * 获取同appid的游戏下载状态
   */
  const getDownloadState = () => {
    const downloadState = getStateByID('yybDownload')?.downloadStatesByAppID?.[
      config.value.appInfo.appId
    ];
    if (!downloadState) {
      // 如果下载状态找不到，上报异常
      console.error(`[moka-ui-book-update] getDownloadState error: ${downloadState}`);
    }

    return downloadState as unknown as DownloadStateInfo;
  };

  /**
   * 执行游戏下载
   */
  const doDownload = () => {
    const curDownloadState = getDownloadState();
    if (!curDownloadState) {
      return;
    }

    // 常规下载状态 === 未下载、可更新、等待中、暂停中、已下载、等Wi-Fi「不含下载中、安装中」 ||
    // (常规下载状态 === 已安装 && 需要换包 && 换包下载状态 === 未知、初始状态、拷贝完成、暂停状态、等待中「不含换包中、安装完成」)
    const needHandleDownload = [
      DownloadState.Ready,
      DownloadState.Update,
      DownloadState.Queuing,
      DownloadState.Pausing,
      DownloadState.Downloaded,
      DownloadState.Wifi,
    ].includes(curDownloadState.state)
      || (curDownloadState.state === DownloadState.Installed
        && curDownloadState.isNeedChangePackage
        && [
          ChangePackageState.Unknown,
          ChangePackageState.Ready,
          ChangePackageState.Replaced,
          ChangePackageState.Pausing,
          ChangePackageState.Queuing,
        ].includes(curDownloadState.changePackageState));

    if (!needHandleDownload) {
      return;
    }

    const downloadComp = getDownloadComp();
    const { left, top } = downloadComp!.$el.getBoundingClientRect();
    // 模拟点击下载组件，clientX、clientY依赖下载组件的realHandleClick逻辑
    downloadComp?.realHandleClick({
      clientX: left + 8,
      clientY: top + 8,
    });
  };

  /**
   * 获取同appid的下载组件实例
   */
  const getDownloadComp = () => {
    const downloadComp = (app as unknown as {
      components: {
        isOpenChangePackage: boolean;
        realHandleClick: (event: { clientX: number; clientY: number }) => void;
        $el: HTMLElement;
        config: {
          appId: string;
          componentName: string;
          changePackage: Record<string, number>;
        };
      }[];
    })?.components?.find(item => item.config?.appId === config.value.appInfo.appId.toString()
      && item.config?.componentName === 'yybDownload');

    if (!downloadComp?.config?.changePackage?.isOpenChangePackage) {
      // 如果下载组件找不到或未开启换包，上报异常
      console.error(`[moka-ui-book-update] getDownloadComp error: ${downloadComp?.isOpenChangePackage}`);
    }

    return downloadComp;
  };

  return {
    doObtaining,
    getDownloadComp,
    getChannelIdInfo,
  };
}
