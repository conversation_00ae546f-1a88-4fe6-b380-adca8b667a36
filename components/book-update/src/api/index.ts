import { yybClientRequester } from '@tencent/moka-data-core';

import { CDM_DO_BOOKING, CMD_GET_BOOK_UPDATE_INFO } from '../constant';
import { jsonStringify, parseJSON } from '../utils';

const componentType = 'book-update';

interface JsApiCMDResp {
  code: number;
  tip: string;
  body?: {
    errCode: number;
    /** 和具体接口有关 */
    data: any;
  };
}

interface BaseResp {
  code: number;
  tip: string;
  /** 和具体接口有关 */
  data: any;
}

/**
 * 获取用户预约更新信息
 * @param params 预约接口信息
 * @param componentID 组件id
 * @returns 用户预约更新信息
 */
export async function getBookUpdateInfo(params: {
  pkg_names: string[];
  versions: Record<string, number>;
  channels: Record<string, string>;
}, componentID: string): Promise<BaseResp> {
  const result = await yybClientRequester.request({
    componentType,
    componentID,
    /** 命令字 */
    cmd: CMD_GET_BOOK_UPDATE_INFO,
    /** 接口数据 */
    params: {
      request: jsonStringify(params),
    },
  }) as unknown as JsApiCMDResp;

  const { code, tip, body } = result || {};
  const {
    book_h5_pages: data,
  } = (parseJSON(body?.data?.data || '{}') || {}) as { book_h5_pages: null };
  return {
    code,
    tip,
    data,
  };
}

/**
 * 执行预约
 * @param appID 游戏appid
 * @param taskID 预约任务id
 * @returns 返回响应数据
 */
export async function doBooking(
  appID: string,
  taskID: string,
  componentID: string,
): Promise<BaseResp> {
  const result = await yybClientRequester.request({
    componentType,
    componentID,
    /** 命令字 */
    cmd: CDM_DO_BOOKING,
    /** 接口数据 */
    params: {
      appid: appID,
      booked_update_id: taskID,
      source: 'h5',
    },
  }) as unknown as JsApiCMDResp;

  const { code, tip, body } = result || {};
  return {
    code,
    tip,
    data: body?.data,
  };
}
