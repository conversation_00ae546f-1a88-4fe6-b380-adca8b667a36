/** 组件展示状态 */
export enum ShowStatus {
  /** 未预约 */
  UnBook = 'unBook',
  /** 已预约 */
  Book = 'book',
  /** 未领取 */
  UnObtain = 'unObtain',
  /** 已领取 */
  Obtain = 'obtain',
}

/** 礼包类型 */
export enum GiftType {
  /** AMS */
  AMS = 2,
  /** Q币 */
  QB = 4,
}

/** 活动类型 */
export enum PageType {
  /** 预约期 */
  Book = 0,
  /** 更新期 */
  Obtain = 1,
  /** 未知，兜底 */
  Unknown = -1,
}

/** 预约更新信息 */
export interface BookUpdateInfo {
  /** 任务id */
  taskID: string;
  /** 0 预约期、1 更新期 */
  pageType: PageType;
  /** 是否预约 */
  isBooked: boolean;
  /** 是否领取 */
  isObained: boolean;
  /** 礼包类型 */
  giftType: GiftType;
  /** 礼包价值 */
  giftValue: number;
}

/** 组件配置信息 */
export interface Config {
  /** 组件id */
  id: string;
  /** 游戏信息 */
  appInfo: {
    appId: string;
    appName: string;
    pkgName: string;
  };
  /** 预览状态 */
  previewStatus: ShowStatus;
  [key: string]: string | object;
}

/** 下载状态 */
export interface DownloadStateInfo {
  /** 下载状态 */
  state: DownloadState;
  /** 下载进度 */
  percentage: number;
  /** 是否换包 */
  isNeedChangePackage: boolean;
  /** 换包下载状态 */
  changePackageState: ChangePackageState;
  /** 换包下载进度 */
  changePackagePercentage: number;
}

/** 常规下载状态 */
export enum DownloadState {
  /** 未下载 */
  Ready = 1,
  /** 可更新 */
  Update = 2,
  /** 等待中 */
  Queuing = 3,
  /** 下载中 */
  Downloading = 4,
  /** 暂停中 */
  Pausing = 5,
  /** 已下载 */
  Downloaded = 6,
  /** 安装中 */
  Installing = 7,
  /** 已安装 */
  Installed = 8,
  /** 等Wi-Fi */
  Wifi = 'WISE_NO_WIFI_BOOKING_DOWNLOAD',
}

/** 换包下载状态 */
export enum ChangePackageState {
  /** 未知 */
  Unknown = -1,
  /** 换包中 */
  Changing = 0,
  /** 初始状态 */
  Ready = 1,
  /** 拷贝完成 */
  Replaced = 2,
  /** 安装完成 */
  Installed = 3,
  /** 暂停状态 */
  Pausing = 5,
  /** 等待中 */
  Queuing = 6,
}
