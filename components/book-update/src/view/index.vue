<template>
  <button
    v-if="isValid"
    :style="buttonStyle"
    @click="handleBookUpdate"
  >
    {{ buttonTitle }}
  </button>
</template>
<script setup lang="ts">
import 'vant/es/dialog/style';
import { throttle } from 'radash';
import { computed, defineProps, onMounted, ref, toRefs } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';
import YYBMoleJSBridge from '@tencent/mole-jsbridge/yyb';

import {
  doBooking as doBookingApi,
  getBookUpdateInfo as getBookUpdateInfoApi,
} from '../api';
import {
  BOOK_SUCCESS,
  ERROR_LOW_VERSION,
  HAS_BOOK,
  INVALID_EVENT_NAME,
  NEED_IN_YYB,
  SUCCESS_CODE,
  VALID_EVENT_NAME,
} from '../constant';
import { useUpdate } from '../hooks/use-update';
import {
  type BookUpdateInfo,
  type Config,
  PageType,
  ShowStatus,
} from '../types';

const yybMoleJSBridge = new YYBMoleJSBridge();

const props = defineProps<{
  config: Config;
}>();
const { config } = toRefs(props);

/** 是否有效用户（玩过或在装） */
const isValid = ref<boolean>(false);
/** 预约更新信息 */
const bookUpdateInfo = ref<BookUpdateInfo | undefined>(undefined);
/** 是否加载中 */
const isLoading = ref(false);

const { isLogin, toast, inMagic, openLogin, $bus } = hooks.useMokaInject();
const { doObtaining, getDownloadComp, getChannelIdInfo } = useUpdate(
  bookUpdateInfo,
  config,
  yybMoleJSBridge,
);

const buttonStyle = computed(() => {
  if (inMagic) {
    return getStyle(config.value.previewStatus);
  }

  // 数据为空时，兜底未预约状态（实际组件会隐藏）
  if (!bookUpdateInfo.value) {
    return getStyle(ShowStatus.UnBook);
  }

  const { pageType, isBooked, isObained } = bookUpdateInfo.value;
  if (pageType === PageType.Book) {
    return isBooked ? getStyle(ShowStatus.Book) : getStyle(ShowStatus.UnBook);
  }

  if (pageType === PageType.Obtain) {
    return isObained
      ? getStyle(ShowStatus.Obtain)
      : getStyle(ShowStatus.UnObtain);
  }

  return getStyle(ShowStatus.UnBook);
});

const getStyle = (prefix: string) => ({
  backgroundColor: config.value[`${prefix}BgColor`],
  backgroundImage: `url(${config.value[`${prefix}BgImage`]})`,
  color: config.value[`${prefix}FontColor`],
});

const buttonTitle = computed(() => {
  if (inMagic) {
    return config.value[`${config.value.previewStatus}Title`];
  }

  const { unBookTitle, bookTitle, unObtainTitle, obtainTitle } = config.value;

  if (!bookUpdateInfo.value) {
    return unBookTitle;
  }

  const { pageType, isBooked, isObained } = bookUpdateInfo.value;
  if (pageType === PageType.Book) {
    return isBooked ? bookTitle : unBookTitle;
  }

  if (pageType === PageType.Obtain) {
    return isObained ? obtainTitle : unObtainTitle;
  }

  return unBookTitle;
});

onMounted(() => {
  if (inMagic) {
    isValid.value = true;
    return;
  }

  void init();
});

/**
 * 组件初始化
 */
const init = async () => {
  if (yybMoleJSBridge.appEnv !== 'yyb') {
    toast?.(NEED_IN_YYB);
    return;
  }

  await getBookUpdateInfo();

  if (!bookUpdateInfo.value) {
    // 没有返回数据或接口异常：按无效处理（接口异常已在 data-core 上报）
    $bus?.$emit(INVALID_EVENT_NAME, { config: config.value });
    return;
  }

  const { pageType, isBooked } = bookUpdateInfo.value;

  if (![PageType.Book, PageType.Obtain].includes(pageType)) {
    // 不在预约期、更新期范围内：属于异常数据，按无效处理 + 上报异常
    console.error(`[moka-ui-book-update] bookUpdateInfo error: ${bookUpdateInfo.value}`);

    $bus?.$emit(INVALID_EVENT_NAME, { config: config.value });
    return;
  }

  if (pageType === PageType.Obtain && !isBooked) {
    // 在更新期但未预约：属于合理数据，也按无效处理
    $bus?.$emit(INVALID_EVENT_NAME, { config: config.value });
    return;
  }

  isValid.value = true;
  $bus?.$emit(VALID_EVENT_NAME, { config: config.value });

  setTimeout(() => {
    // 初始化时延迟3s获取下载组件，仅为检测下载组件是否存在，用于异常上报；非业务逻辑
    getDownloadComp();
  }, 3000);
};

/**
 * 预约更新按钮事件
 */
const handleBookUpdate = throttle({ interval: 500 }, async () => {
  if (!bookUpdateInfo.value || isLoading.value) return;

  isLoading.value = true;

  // 执行预约逻辑
  if (bookUpdateInfo.value.pageType === PageType.Book) {
    await doBooking();
  } else if (bookUpdateInfo.value.pageType === PageType.Obtain) {
    // 只有领取状态是和账号相关的，需要拉起登录
    if (!isLogin.value) {
      openLogin?.();
      isLoading.value = false;
      return;
    }

    // 执行领取逻辑
    await doObtaining(() => {
      yybMoleJSBridge.event.on('pageAppear', getBookUpdateInfo);
    });
  }

  isLoading.value = false;
});

/**
 * 执行预约
 */
const doBooking = async () => {
  if (bookUpdateInfo.value?.isBooked) {
    toast?.(HAS_BOOK);
    return;
  }

  const { code, tip } = await doBookingApi(
    config.value.appInfo.appId,
    bookUpdateInfo.value!.taskID,
    config.value.id,
  );
  if (code !== SUCCESS_CODE) {
    // 接口异常已在 data-core 上报
    toast?.(tip);
    return;
  }

  toast?.(BOOK_SUCCESS);

  await getBookUpdateInfo();
};

/**
 * 获取预约更新信息
 */
const getBookUpdateInfo = async () => {
  yybMoleJSBridge.event.off('pageAppear', getBookUpdateInfo);

  const { pkgName } = config.value.appInfo;
  const { pkgVersion, channel } = await getChannelIdInfo(pkgName);

  const { code, tip, data } = await getBookUpdateInfoApi(
    {
      pkg_names: [pkgName],
      versions: {
        [pkgName]: pkgVersion,
      },
      channels: {
        [pkgName]: channel,
      },
    },
    config.value.id,
  );

  if (code !== SUCCESS_CODE) {
    // 接口异常按无效处理（接口异常已在 data-core 上报）
    if (code !== ERROR_LOW_VERSION) {
      toast?.(tip);
    }

    return;
  }

  if (!data?.length) {
    return;
  }

  const {
    page_type: pageType,
    book_update_info: {
      id: taskID,
      booked: isBooked,
      book_update_reward: { giftType, value: giftValue, received: isObained },
    },
  } = data[0];

  bookUpdateInfo.value = {
    taskID,
    pageType,
    isBooked,
    isObained,
    giftType,
    giftValue,
  };
};
</script>

<style lang="scss" src="./index.scss" />
