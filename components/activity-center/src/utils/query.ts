/**
 * 向 URL 上添加 Query 参数
 * @param url  URL
 * @param keyValues 查询参数
 * @returns
 */
export function appendQueryParam(url: string, keyValues: Record<string, string | number>): string {
  try {
    // 创建一个新的 URL 对象
    const urlObj = new URL(url);

    // 使用 URLSearchParams 对象来处理查询字符串
    const params = new URLSearchParams(urlObj.search);

    Object.keys(keyValues).forEach((key) => {
      // 添加新的查询参数
      params.append(key, `${keyValues[key]}`);
    });

    // 重新设置 URL 对象的查询字符串
    urlObj.search = params.toString();

    // 返回更新后的 URL 字符串
    return urlObj.toString();
  } catch (error) {
    console.error('Invalid URL:', error);
    return url;
  }
}
