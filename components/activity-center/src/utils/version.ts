/**
 * 获取应用宝版本号和构建号
 * @param ua 用户 UA
 * @returns 应用宝版本号和构建号
 */
export function getYYBVersion(ua: string) {
  const versionRegExp = /yyb_version\/(\d+)/;
  const buildRegExp = /yyb_build\/(\d+)/;
  const versionMatch = versionRegExp.exec(ua);
  const buildMatch = buildRegExp.exec(ua);

  const yybVersion = versionMatch?.[1] ?? '0';
  const yybBuild = buildMatch?.[1] ?? '0';
  return {
    // FIXME: 应用宝版本号超过 10 之后需要修改截取规则
    // 截掉后四位固定数字，保留前三位或四位的版本号
    version: String(yybVersion.slice(0, -4)),
    // 构建号
    build: String(yybBuild),
  };
}
