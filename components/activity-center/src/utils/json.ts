/**
 * 将 JSON 字符串解析为指定类型的对象, 如果解析失败则返回 null
 * @param jsonString 要解析的 JSON 字符串
 * @returns 解析后的对象或 null
 */
export function safeJsonParse<T>(jsonString: string): T | null {
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error('JSON Parse 异常, msg: s%', (error as Error).message);
    return null;
  }
}

/**
 * 将对象序列化为 JSON 字符串, 如果序列化失败则返回 null
 * @param data 要序列化的对象
 * @returns 序列化后的 JSON 字符串或 null
 */
export function safeJsonStringify(data: unknown): string | null {
  try {
    return JSON.stringify(data);
  } catch (error) {
    console.error('JSON Stringify 异常, msg: s%', (error as Error).message);
    return null;
  }
}
