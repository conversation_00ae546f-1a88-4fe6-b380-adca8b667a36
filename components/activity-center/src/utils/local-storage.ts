import type { StorageWithExpiry } from '../types';
import { safeJsonParse, safeJsonStringify } from './json';

/**
 * 设置本地缓存（有效期为当天）
 * @param key 缓存 Key
 * @param value 缓存的值
 */
export function setLocalStorageWithDailyExpiry<T>(key: string, value: T) {
  const now = new Date();
  const expiryDate = new Date(now);
  // 设置当前有效的缓存时间
  expiryDate.setHours(24, 0, 0, 0);

  const item = {
    value,
    expiry: expiryDate.getTime(),
  };

  const jsonStr = safeJsonStringify(item);
  if (!jsonStr) {
    return;
  }

  localStorage.setItem(key, jsonStr);
}

/**
 * 获取有效的 LocalStorage 缓存
 * @param key 缓存 Key
 * @returns 缓存的值，过期则返回 null
 */
export function getLocalStorageWithExpiry(key: string) {
  const itemStr = localStorage.getItem(key);
  if (!itemStr) {
    return null;
  }

  const item = safeJsonParse<StorageWithExpiry>(itemStr);
  const now = new Date();

  // 检查当前时间是否已经过了存储的过期时间
  if (!item || now.getTime() > item.expiry) {
    localStorage.removeItem(key);
    return null;
  }

  return item.value;
}
