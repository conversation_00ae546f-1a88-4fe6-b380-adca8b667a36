<template>
  <div
    v-if="isNeedToShow"
    dt-eid="activity-center"
    class="activity-center"
    :style="activityCenterStyle"
    @click="handleClick"
  >
    <div
      class="activity-center__icon"
      :style="iconStyle"
    />
    <div
      class="activity-center__text"
      :style="textColorStyle"
    >
      活动中心
    </div>
  </div>
</template>

<script setup lang="ts">
import { debounce } from 'radash';
import { computed, defineProps, ref } from 'vue';
import { hooks } from '@tencent/moka-ui-domain';
import MoleJSBridge from '@tencent/mole-jsbridge';

import { ACTIVITY_CENTER_URL, HAS_TODAY_VISIT_LOCAL_STORAGE_KEY, SUPPORT_KUILKLY_VERSION } from '../constant';
import type { ComponentConfig } from '../types';
import { safeJsonParse } from '../utils/json';
import { getLocalStorageWithExpiry, setLocalStorageWithDailyExpiry } from '../utils/local-storage';
import { appendQueryParam } from '../utils/query';
import { getYYBVersion } from '../utils/version';

const { inMagic } = hooks.useMokaInject();

const props = defineProps<{
  config: ComponentConfig;
}>();

/** 是否需要展示组件 */
const isNeedToShow = ref(false);

/** 活动中心组件样式 */
const activityCenterStyle = computed(() => {
  const { backgroundColor, backGroundUrl } = props.config;
  if (!backGroundUrl) {
    // 如果仅配置背景色，需要设置圆角
    return {
      background: backgroundColor || 'linear-gradient(180deg, #FFD75C 0%, #FFECB0 100%)',
      borderRadius: '22.5px 0 0 22.5px',

    };
  }

  return {
    background: `url('${backGroundUrl}')`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
  };
});

/** 文本颜色样式 */
const textColorStyle = computed(() => ({
  color: props.config.textColor || 'rgba(82, 62, 0, 1)',
}));

/** 按钮图片样式 */
const iconStyle = computed(() => {
  const defaultUrl = 'https://cdn.yyb.gtimg.com/wupload/xy/yybtech/P6U1zuT9.png';
  return {
    backgroundImage: `url(${props.config.iconUrl || defaultUrl})`,
  };
});

const jsbridge = MoleJSBridge.getJSBridge();

/**
 * 设置数据，是否允许 H5 消费返回操作
 * @param value 是否允许 H5 消费（0 不允许，1 允许）
 */
const saveJSBCanGoBack = async (value: number) => {
  // saveData jsb 不存在回调，因此需要将超时设置比较少，避免等待默认的 2s 超时
  await jsbridge?.call('saveData', {
    key: 'jsb_can_go_back',
    value,
    mode: 1,
  }, { timeout: 200 });
};

/** 处理点击 */
const handleClick = debounce({ delay: 250 }, () => {
  void saveJSBCanGoBack(0).then(() => {
    // 终端使用 kuilkly 页面，无低版本要求，此处需要添加参数供终端上报，1 表示组件主动进入活动中心页
    location.href = appendQueryParam(ACTIVITY_CENTER_URL, { uni_entrance_type: 1 });
  });
});

/** 组件初始化 */
const init = async () => {
  // 在编辑器时显示出来
  if (inMagic) {
    isNeedToShow.value = true;
    return;
  }

  // 低版本组件不展示
  if (jsbridge?.appEnv !== 'yyb' || Number(getYYBVersion(navigator.userAgent).version) <= SUPPORT_KUILKLY_VERSION) {
    return;
  }

  isNeedToShow.value = true;
  const data = getLocalStorageWithExpiry(HAS_TODAY_VISIT_LOCAL_STORAGE_KEY);
  if (data) {
    console.log('用户当天已访问过活动页面');
    return;
  }

  // 此处设置 jsb_can_go_back 为 1 后，后续点击返回，终端会通过 jsbUserAction 回调给 H5 消费本次返回操作
  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  window.addEventListener('jsbUserAction', async (event) => {
    // @ts-ignore 终端 JSB 回调函数的数据
    const jsbCallbackData = event?.data as {
      method: string;
      data: string;
    };

    const methodData = safeJsonParse<{ action: number }>(jsbCallbackData.data ?? '{}');
    // 如果是 goBack 回调事件且 action 是 1，表示允许 H5 消费返回操作
    if (jsbCallbackData.method === 'goBack' && methodData?.action === 1) {
      // 关闭 webview 并跳转至 kuilkly 页面
      void jsbridge?.ui?.closeWebview();
      // 此处需要添加参数供终端上报，1 表示组件主动进入活动中心页
      location.href = appendQueryParam(ACTIVITY_CENTER_URL, { uni_entrance_type: 2 });
    }
  });

  // 首次访问设置
  await saveJSBCanGoBack(1);
  setLocalStorageWithDailyExpiry(HAS_TODAY_VISIT_LOCAL_STORAGE_KEY, true);
};

void init();
</script>

<style lang="scss" src="./index.scss" scoped />
