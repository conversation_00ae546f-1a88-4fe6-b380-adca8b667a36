import { IPublicComponentMetaSchema } from '@tencent/moka-schema';
/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [{
    type: 'object',
    properties: {
      iconUrl: {
        type: 'string',
        title: '图标背景图上传',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        default: 'https://cdn.yyb.gtimg.com/wupload/xy/yybtech/P6U1zuT9.png',
      },
      backGroundUrl: {
        type: 'string',
        title: '背景图上传(尺寸为 45 × 25)',
        'x-decorator-props': {
          labelWrap: true,
        },
        'x-decorator': 'FormItem',
        'x-component': 'Upload',
        default: '',
      },
      textColor: {
        type: 'string',
        title: '文案颜色',
        'x-decorator': 'FormItem',
        'x-component': 'ColorPocker',
        'x-decorator-props': {
          labelWrap: true,
        },
        default: 'rgba(82, 62, 0, 1)',
      },
      backgroundColor: {
        type: 'string',
        title: '背景颜色（不配置背景图时生效）',
        'x-decorator': 'FormItem',
        'x-component': 'ColorPocker',
        'x-decorator-props': {
          labelWrap: true,
        },
        default: 'linear-gradient(180deg, #FFD75C -18%, #FFECB0 100%);',
      },
    },
  }],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
