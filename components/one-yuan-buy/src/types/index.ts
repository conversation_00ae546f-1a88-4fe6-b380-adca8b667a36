import { GoodsStatus } from '@tencent/one-yuan-buy';

export interface Config {
  offerId: string;
  goodsId: string;
  buyConfig: ButtonConfig;
  receiveConfig: ButtonConfig;
  receivedConfig: ButtonConfig;
  unavailableConfig: ButtonConfig;
  previewType: PreviewType;
}
export enum PreviewType {
  Buy = GoodsStatus.Buy,
  Receive = GoodsStatus.Receive,
  Received = GoodsStatus.Received,
  Unavailable = GoodsStatus.Limit,
}
export interface ButtonConfig {
  text: string;
  btnBg: string;
  bgColor: string;
  fontColor: string;
}
