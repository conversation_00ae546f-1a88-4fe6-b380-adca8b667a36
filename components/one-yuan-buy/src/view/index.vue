<template>
  <div
    ref="button"
    :style="buttonStyle"
    class="one-yuan-buy"
    @click="onClickButton"
  >
    {{ buttonConfig.text }}
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, ref, toRefs, watch } from 'vue';
import { fakeToast } from '@tencent/hdzt-utils';
import { hooks } from '@tencent/moka-ui-domain';
import { ErrorType, GoodsStatus, type OnErrorConfig, OneYuanBuy } from '@tencent/one-yuan-buy';

import type { Config, PreviewType } from '../types';
import { getIsTest } from '../utils';

const props = defineProps<{
  config: Config;
}>();

const { openLogin: openActLogin, inMagic } = hooks.useMokaInject();

const { config } = toRefs(props);
const status = ref(GoodsStatus.Unknown);
let oneYuanBuyInstance: OneYuanBuy;

const init = async () => {
  if (inMagic) {
    watch(() => config.value.previewType, (val: PreviewType) => {
      status.value = val as unknown as GoodsStatus;
    }, {
      immediate: true,
    });

    return;
  }

  oneYuanBuyInstance = new OneYuanBuy({
    isTest: getIsTest(),
    offerId: config.value.offerId,
    goodsId: config.value.goodsId,
    onStatusRefresh: (val: GoodsStatus) => {
      status.value = val;
    },
  });

  try {
    await oneYuanBuyInstance.getOneYuanBuyStatus();
  } catch (e) {
    console.error(`[oneYuanBuy] getOneYuanBuyStatus error, config: ${JSON.stringify(config)}, url: ${location.href}, e:${JSON.stringify(e)}`);
    fakeToast('网络繁忙，请重试');
  }
};

void init();

const buttonConfig = computed(() => {
  if (status.value === GoodsStatus.Received) {
    return config.value.receivedConfig;
  }

  if (status.value === GoodsStatus.Receive) {
    return config.value.receiveConfig;
  }

  if (status.value === GoodsStatus.Buy) {
    return config.value.buyConfig;
  }

  return config.value.unavailableConfig;
});

const buttonStyle = computed(() => ({
  backgroundColor: buttonConfig.value.bgColor,
  color: buttonConfig.value.fontColor,
  backgroundImage: `url(${buttonConfig.value.btnBg})`,
}));

const onClickButton = () => {
  if (inMagic) {
    return;
  }

  void oneYuanBuyInstance.exec({
    onError,
    onReceiveSuccess,
    onBuySuccess,
    openLogin,
  });
};

async function onError(errConfig: OnErrorConfig) {
  const { type, msg } = errConfig;
  console.error(`[oneYuanBuy] exec error, config: ${JSON.stringify(config)}, url: ${location.href}, e: ${JSON.stringify(errConfig)}`);
  if (type === ErrorType.BuyError || type === ErrorType.ReceiveError) {
    fakeToast(msg);
    return;
  }

  if (type === ErrorType.MidasCancel) {
    fakeToast('未完成支付');
    return;
  }

  fakeToast('网络繁忙，请重试');
}

async function onBuySuccess() {
  status.value = await oneYuanBuyInstance.getOneYuanBuyStatus();
}

async function openLogin() {
  // 未登陆拉起登陆
  openActLogin?.();
  return false;
}

async function onReceiveSuccess() {
  fakeToast('道具已经发放到游戏内 在游戏邮箱内查看');
}
</script>

<style lang="scss" src="./index.scss" scoped />
