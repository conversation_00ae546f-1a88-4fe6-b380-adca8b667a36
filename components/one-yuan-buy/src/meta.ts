import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

const getEnv = () => (/testsite/.test(location.host) ? 'test' : 'prod');
/**
 * 采用 Formily 协议的表单配置
 * https://designable-antd.formilyjs.org/
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      title: '页面功能',
      type: 'object',
      properties: {
        offerId: {
          type: 'string',
          title: 'offer_id',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'TextLink',
          'x-component-props': {
            linkTitle: '一元购配置',
            linkStrOrFunc() {
              return `https://xy.woa.com/xy/app/${getEnv()}/db_private_domain/qualification-of-commodity-details`;
            },
          },
        },
        goodsId: {
          type: 'string',
          title: 'goods_id',
          required: true,
          'x-decorator': 'FormItem',
          'x-component': 'TextLink',
          'x-component-props': {
            linkTitle: '一元购配置',
            linkStrOrFunc() {
              return `https://xy.woa.com/xy/app/${getEnv()}/db_private_domain/qualification-of-commodity-details`;
            },
          },
        },
        previewType: {
          title: '预览类型',
          name: '预览类型',
          'x-component': 'Select',
          'x-decorator': 'FormItem',
          default: 1,
          enum: [
            {
              children: [],
              label: '未购买',
              value: 1,
            },
            {
              children: [],
              label: '已购买未领取',
              value: 7,
            },
            {
              children: [],
              label: '已领取',
              value: 8,
            },
            {
              children: [],
              label: '不可购买',
              value: 3,
            },
          ],
        },
        collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '未购买',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'buyConfig.text': {
                      type: 'string',
                      title: '按钮文案',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                    },
                    'buyConfig.btnBg': {
                      type: 'string',
                      title: '背景图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                    },
                    'buyConfig.bgColor': {
                      type: 'string',
                      title: '背景色',

                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                    'buyConfig.fontColor': {
                      type: 'string',
                      title: '字体颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                  },
                },
              },
            },
            tab2: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '已购买未领取',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'receiveConfig.text': {
                      type: 'string',
                      title: '按钮文案',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                    },
                    'receiveConfig.btnBg': {
                      type: 'string',
                      title: '背景图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                    },
                    'receiveConfig.bgColor': {
                      type: 'string',
                      title: '背景色',

                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                    'receiveConfig.fontColor': {
                      type: 'string',
                      title: '字体颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                  },
                },
              },
            },
            tab3: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '已领取',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'receivedConfig.text': {
                      type: 'string',
                      title: '按钮文案',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                    },
                    'receivedConfig.btnBg': {
                      type: 'string',
                      title: '背景图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                    },
                    'receivedConfig.bgColor': {
                      type: 'string',
                      title: '背景色',

                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                    'receivedConfig.fontColor': {
                      type: 'string',
                      title: '字体颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                  },
                },
              },
            },
            tab4: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '不可购买',
              },
              properties: {
                layout: {
                  type: 'void',
                  'x-component': 'FormLayout',
                  'x-component-props': {
                    labelCol: 8,
                    labelWrap: true,
                  },
                  properties: {
                    'unavailableConfig.text': {
                      type: 'string',
                      title: '按钮文案',
                      'x-decorator': 'FormItem',
                      'x-component': 'Input',
                    },
                    'unavailableConfig.btnBg': {
                      type: 'string',
                      title: '背景图',
                      'x-decorator': 'FormItem',
                      'x-component': 'Upload',
                    },
                    'unavailableConfig.bgColor': {
                      type: 'string',
                      title: '背景色',

                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                    'unavailableConfig.fontColor': {
                      type: 'string',
                      title: '字体颜色',
                      'x-decorator': 'FormItem',
                      'x-component': 'ColorPocker',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: [],
    /** 组件内部方法，提供外部调用 */
    methods: [],
  },
};

export default meta;
