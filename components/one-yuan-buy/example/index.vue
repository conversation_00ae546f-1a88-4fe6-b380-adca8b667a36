<script setup lang="ts">
import Example from '../src/view/index.vue';

const config = {
  type: 'moka-ui-one-yuan-buy',
  goodsId:'LY1YUANGOU-TEST',
  offerId:'66600083',
  style: {
    left: '150',
    width: '120',
    height: '32',
  },
  buyConfig: {
    text: '购买',
    btnBg: 'https://vfiles.gtimg.cn/wupload/fileupload/20221012_mlbznmwvl6egtnutyl8e8fjxbsyh60sw.png',

  },
  receiveConfig: {
    text: '领取',
  },
  receivedConfig: {
    text: '已领取',
  },
  unavailableConfig: {
    text: '达限量',
  },

}
</script>

<template>
  <Example :config="config" />
</template>

