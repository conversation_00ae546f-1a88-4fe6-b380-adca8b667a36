{"name": "@tencent/moka-ui-partition-role-bind", "version": "0.0.9", "description": "活动中台组件模板", "main": "./dist/index.es.js", "scripts": {"dev": "vite", "lint": "eslint src --ext .js,.jsx,.ts,.tsx,.vue", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx,.vue --fix"}, "author": "ziyanou<<EMAIL>>", "license": "ISC", "keywords": ["moka", "moka-component", "moka-ui-partition-role-bind"], "repository": {"type": "git", "url": "", "directory": "components/partition-role-bind"}, "dependencies": {"@tencent/moka-data-core": "latest", "@tencent/moka-ui-domain": "0.0.32-beta.2", "@tencent/mole-report": "^1.0.11", "@tencent/moka-editor-helper": "^0.0.13", "humps": "^2.0.1", "js-cookie": "^3.0.5", "vue": "^3.4.37", "radash": "^12.1.0", "@tencent/yyb-login-core": "^1.0.8"}, "devDependencies": {"@types/humps": "^2.0.6", "@types/js-cookie": "^3.0.6", "husky": "^8.0.3", "postcss-pxtorem": "^6.1.0", "sass": "^1.77.8", "turbo": "^1.13.4"}}