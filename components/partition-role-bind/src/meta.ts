import { IPublicComponentMetaSchema } from '@tencent/moka-schema';

import events from './event';

/**
 * 该组件使用魔方协议
 * 因 moka 编辑器未实装 formily 编辑器
 */
const meta: IPublicComponentMetaSchema = {
  /** 表单配置 */
  configure: [
    {
      type: 'object',
      properties: {
        appInfo: {
          type: 'object',
          title: '游戏配置',
          required: true,
          default: '',
          'x-decorator': 'FormItem',
          'x-component': 'AppSelect',
          'x-component-props': {
            test: 10,
            bindType: 'appInfo',
          },
        },
        packageName: {
          type: 'string',
          title: '应用包名',
          required: true,
          'x-disabled': true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-reactions': {
            dependencies: ['.appInfo'],
            fulfill: {
              schema: {
                'x-value': '{{$deps[0].pkgName}}',
              },
            },
          },
        },
        unBoundTip: {
          type: 'string',
          title: '未绑定文案提示',
          default: '领取礼包需要先绑定角色',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
        },
        needPartitionAndRole: {
          type: 'boolean',
          title: '是否强制选区服',
          default: false,
          'x-decorator-props': {
            labelWidth: 'auto',
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
        },
        gameIcon: {
          type: 'string',
          title: '游戏图标',
          'x-decorator-props': {
            labelWrap: true,
          },
          'x-disabled': true,
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-reactions': {
            dependencies: ['.appInfo'],
            fulfill: {
              schema: {
                'x-value': '{{$deps[0].smallIcon}}',
              },
            },
          },
        },
        hideGameIcon: {
          type: 'boolean',
          title: '是否隐藏游戏icon',
          default: false,
          'x-decorator-props': {
            labelWidth: 'auto',
            labelWrap: true,
          },
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
        },
        collapse: {
          type: 'void',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            labelWidth: '0',
          },
          'x-component': 'FormCollapse',
          properties: {
            tab1: {
              type: 'void',
              'x-component': 'FormCollapse.Item',
              'x-component-props': {
                title: '样式配置',
              },
              properties: {
                position: {
                  title: '位置',
                  name: '位置',
                  'x-component': 'Select',
                  'x-decorator': 'FormItem',
                  default: 1,
                  enum: [
                    {
                      children: [],
                      label: '居中',
                      value: 1,
                    },
                    {
                      children: [],
                      label: '居左',
                      value: 2,
                    },
                    {
                      children: [],
                      label: '居右',
                      value: 3,
                    },
                  ],
                },
                margin: {
                  title: '边距',
                  name: '边距',
                  'x-component': 'InputNumber',
                  'x-decorator': 'FormItem',
                  default: 0,
                },
                textColor: {
                  type: 'string',
                  title: '文案颜色',
                  'x-decorator': 'FormItem',
                  'x-component': 'ColorPocker',
                  'x-decorator-props': {
                    labelWrap: true,
                  },
                  name: 'pointsModuleBgColor',
                  default: 'rgba(255, 255, 255, 1)',
                },
                fontSize: {
                  title: '字体大小',
                  name: '字体大小',
                  'x-component': 'InputNumber',
                  'x-decorator': 'FormItem',
                  default: 11,
                },
                backgroundColor: {
                  type: 'string',
                  title: '背景颜色',
                  'x-decorator': 'FormItem',
                  'x-component': 'ColorPocker',
                  'x-decorator-props': {
                    labelWrap: true,
                  },
                  name: 'pointsModuleBgColor',
                  default: 'rgba(0, 0, 0, 0.12)',
                },
                backgroundImage: {
                  type: 'string',
                  title: '区服组件背景图',
                  'x-decorator-props': {
                    labelWrap: true,
                  },
                  'x-decorator': 'FormItem',
                  'x-component': 'Upload',
                },
                switchIconImage: {
                  type: 'string',
                  default: '',
                  title: '切换按钮 ICON（终端展示）',
                  'x-decorator-props': {
                    labelWrap: true,
                  },
                  'x-decorator': 'FormItem',
                  'x-component': 'Upload',
                },
                leftArrowImage: {
                  type: 'string',
                  default: '',
                  title: '未绑定箭头 ICON（终端展示）',
                  'x-decorator-props': {
                    labelWrap: true,
                  },
                  'x-decorator': 'FormItem',
                  'x-component': 'Upload',
                },
                maskZIndex: {
                  title: '蒙层层级',
                  name: '蒙层层级',
                  'x-component': 'InputNumber',
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    labelWrap: true,
                  },
                  default: 998,
                  description: '设置蒙层的 z-index 值，数值越大层级越高',
                },
              },
            },
          },
        },
      },
    },
  ],
  /** 事件配置 */
  supports: {
    /** 组件内抛出事件 - 编排时提供绑定 */
    events: events.events,
    /** 组件内部方法，提供外部调用 */
    methods: events.methods,
  },
};

export default meta;
