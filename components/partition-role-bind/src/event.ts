import { COMPONENT_EVENT, COMPONENT_METHODS } from './constant';

export default {
  /** 组件内抛出事件 - 编排时提供绑定 */
  events: [
    {
      label: '角色绑定成功',
      value: COMPONENT_EVENT.PARTITION_ROLE_BIND_SUCCESS,
      desc: '当用户成功绑定区服角色后触发',
    },
  ],
  /** 组件内部方法，提供外部调用 */
  methods: [
    {
      label: '拉起区服绑定',
      value: COMPONENT_METHODS.OPEN_PARTITION_ROLE_SELECTOR,
      desc: '外部组件联动拉起区服绑定',
    },
  ],
};
