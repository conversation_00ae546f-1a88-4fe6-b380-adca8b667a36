/** 位置 */
export enum Position {
  center = 1,
  left = 2,
  right = 3,
}
/** 布局模式 */
export const LayoutByPosition = {
  [Position.center]: 'center',
  [Position.left]: 'flex-start',
  [Position.right]: 'flex-end',
};

/** 操作按钮类型 */
export enum OperateBtnType {
  Arrow = 'arrow',
  Switch = 'switch',
}

/** 组件内抛出事件 */
export const COMPONENT_EVENT = {
  /** 角色绑定成功事件 */
  PARTITION_ROLE_BIND_SUCCESS: 'act:component:partition_role_bind_success',
};

/** 组件内抛出事件 */
export const COMPONENT_METHODS = {
  /** 拉起区服选择绑定 */
  OPEN_PARTITION_ROLE_SELECTOR: 'act:component:open_partition_role_selector',
};

export * from './text';
