<template>
  <div
    v-if="isInit && isValid"
    class="partition-role-bind"
    :style="componentStyle"
  >
    <div
      class="partition-role-bind-container"
      :style="containerStyle"
      @click="debounceOpenPartitionRoleSelector"
    >
      <div
        v-if="!config.hideGameIcon"
        class="partition-role-bind-container__game-icon"
        :style="gameIconStyle"
      />
      <div
        class="partition-role-bind-container__info"
        :style="textStyle"
      >
        {{ info }}
      </div>
      <operate-btn
        :type="hasBound ? OperateBtnType.Switch : OperateBtnType.Arrow"
        :color="config.textColor"
        class="partition-role-bind-container__operate-btn"
      />
    </div>
    <!-- 强制选区服蒙层 -->
    <div
      v-if="showForcePartitionMask"
      class="partition-role-bind-mask"
      :style="{ zIndex: props.config.maskZIndex }"
      @click="debounceOpenPartitionRoleSelector"
    />
  </div>
</template>

<script setup lang="ts">
import { debounce } from 'radash';
import { computed, watch } from 'vue';
import { hooks, utils } from '@tencent/moka-ui-domain';
import { isYYB } from '@tencent/mole-utils-lib';
import { getLoginInfoFromCookie, LoginType } from '@tencent/yyb-login-core';
import { openPartitionRoleSelect, PartitionScene, PartitionType, PopupStyle } from '@tencent/yyb-partition-role-select';

import OperateBtn from '../components/operate-btn.vue';
import { BOUND_SUCCESS_TEXT, BUSINESS_ERROR_TEXT, COMPONENT_EVENT, COMPONENT_METHODS, LayoutByPosition, OperateBtnType } from '../constant';
import { usePartitionRoleHook } from '../hooks/index';
import { ComponentConfig } from '../types/index';
import { withEmpty } from '../utils/string';
import { transformPxToRem } from '../utils/style';

const props = defineProps<{
  config: ComponentConfig;
  canJoinActivity: boolean;
}>();

const { toast, isLogin, app, $bus } = hooks.useMokaInject();

const {
  init,
  isInit,
  partitionType,
  boundPartitionAndRole,
  setPartitionRole,
  showOSType,
} = usePartitionRoleHook(props.config.packageName);

/** 是否已绑定 */
const hasBound = computed(() => !!boundPartitionAndRole.value);
/** 是否有效 */
const isValid = computed(() => partitionType.value || partitionType.value !== PartitionType.NOT_PARTITION);
/** 是否显示强制选区服蒙层 */
const showForcePartitionMask = computed(() => props.config.needPartitionAndRole
  && isInit.value
  && isValid.value
  && !hasBound.value);

const componentStyle = computed(() => ({
  'justify-content': LayoutByPosition[props.config.position] || 'center',
}));

// 触发事件
const triggerEvent = (eventName: string) => {
  console.log(`[partition-role-bind] 触发事件: ${eventName}`);
  $bus?.$emit(eventName, { config: props.config });
};

/** 容器样式 */
const containerStyle = computed(() => {
  const { backgroundColor, backgroundImage } = props.config;
  if (backgroundImage) {
    return {
      margin: `0 ${props.config.margin}px`,
      backgroundImage: `url(${backgroundImage})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
      backgroundPosition: 'center center',
    };
  }

  return {
    margin: `0 ${transformPxToRem(props.config.margin)}rem`,
    backgroundColor,
  };
});

/** 游戏图标样式 */
const gameIconStyle = computed(() => {
  if (!props.config.gameIcon) {
    return {};
  }

  const url = new URL(props.config.gameIcon);
  url.protocol = 'https';
  return {
    backgroundImage: `url(${url.toString()})`,
  };
});

/** 文本内容 */
const info = computed(() => {
  if (!boundPartitionAndRole.value) {
    return props.config.unBoundTip || '领取礼包需要先绑定角色';
  }

  if (partitionType.value === PartitionType.ONLY_PARTITION) {
    return `当前区服：${withEmpty(boundPartitionAndRole.value?.partition?.name)}`;
  }

  return `当前角色：${withEmpty(boundPartitionAndRole.value?.partition?.name)} ${withEmpty(boundPartitionAndRole.value?.role?.name)}`;
});

/** 文本样式 */
const textStyle = computed(() => {
  const { textColor, fontSize } = props.config;
  if (!textColor) {
    return {};
  }

  return {
    color: textColor,
    fontSize: `${transformPxToRem(fontSize)}rem`,
  };
});

/** 打开区服选择弹窗 */
const openPartitionRoleSelector = async () => {
  // 未登录需要登录
  if (!isLogin.value) {
    void app?.$openLogin();
    return;
  }

  const { packageName } = props.config;
  if (!partitionType.value || !packageName) {
    console.error(`[openPartitionRoleSelector] 拉起区服参数错误, packageName: ${packageName}, partitionType: ${partitionType.value}`);
    toast?.(BUSINESS_ERROR_TEXT);
    return;
  }

  await openPartitionRoleSelect({
    // 宝外才需要打开系统选择
    isShowOSType: !isYYB(navigator.userAgent),
    partitionType: partitionType.value,
    pkgName: packageName,
    openVirtualList: true,
    needSaveAfterSuccess: true,
    isTest: utils.isTestEnv(),
    popupStyle: utils.isPCStyle() ? PopupStyle.PC : PopupStyle.Mobile,
    onOk: async (partitionRole, onBusinessSuccess, onBusinessError) => {
      try {
        if (!partitionRole) {
          console.error('[openPartitionRoleSelect] 区服信息为空');
          return false;
        }

        // 调用区服选择成功回调
        await onBusinessSuccess?.();
        setPartitionRole(partitionRole);
        toast?.(BOUND_SUCCESS_TEXT);

        // 触发角色绑定成功事件
        console.log('[partition-role-bind] 触发角色绑定成功事件');
        triggerEvent(COMPONENT_EVENT.PARTITION_ROLE_BIND_SUCCESS);

        return true;
      } catch (err) {
        console.error('[openPartitionRoleSelect] 保存绑定信息异常', err);
        toast?.(BUSINESS_ERROR_TEXT);
        await onBusinessError?.();
        return false;
      }
    },
    // 目前移动端外、PC 端内、PC 端外三者逻辑是保持一致的，iOS 封禁仅选中安卓，否则展示双端
    showOSTypesByScene: {
      [PartitionScene.MobileYYBOutSide]: () => showOSType,
      [PartitionScene.PCInSide]: () => showOSType,
      [PartitionScene.PCOutSide]: () => showOSType,
    },
  });
};

/** 节流函数 */
const debounceOpenPartitionRoleSelector = debounce({ delay: 100 }, openPartitionRoleSelector);

/** 初始化组件 */
const initPartitionRoleComponent = () => {
  const loginInfo = getLoginInfoFromCookie();
  // 未登录情况下仅展示默认状态，不请求接口
  if (loginInfo.loginType === LoginType.None) {
    console.warn('[initPartitionRoleComponent] 用户未登录，不获取用户区服角色信息');
    isInit.value = true;
    return;
  }

  void init();
};

// 初始化组件
initPartitionRoleComponent();

// 组件挂载后检查是否需要强制选区服
watch(
  [showForcePartitionMask, isLogin],
  ([newShowForcePartitionMask, newIsLogin]) => {
    if (newShowForcePartitionMask && newIsLogin) {
      void debounceOpenPartitionRoleSelector();
    }
  },
  { immediate: true },
);

defineExpose({
  [COMPONENT_METHODS.OPEN_PARTITION_ROLE_SELECTOR]: debounceOpenPartitionRoleSelector,
});
</script>

<style lang="scss" src="./index.scss" scoped />
