import type { Partition, PartitionType, Role } from '@tencent/yyb-partition-role-select';

import type { OSType } from '../utils/os';

/** 获取区服列表响应 */
export interface GetPartitionsResp {
  /** 区服列表 */
  partitionList?: Partition[] | null;
  /** 默认区服 */
  defaultPartition?: Partition | null;
  /** 默认角色 */
  defaultRole?: Role | null;
  /** 区服类型 */
  partitionType?: PartitionType;
  /** 用户保存区服信息 */
  savedInfo?: {
    lastSavedOsType: OSType;
    data: Record<string, { osType: OSType; partition?: Partition; role?: Role }>;
  };
}

/** 获取角色列表 */
export interface GetRoleResp {
  /** 角色列表 */
  roleList?: Role[] | null;
  /** 默认区服 */
  defaultPartition?: Partition | null;
  /** 默认角色 */
  defaultRole?: Role | null;
}
