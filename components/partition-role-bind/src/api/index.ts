import { camelizeKeys } from 'humps';
import { accessRequester } from '@tencent/moka-data-core';
import { utils } from '@tencent/moka-ui-domain';
import type { Partition } from '@tencent/yyb-partition-role-select';

import type { OSType } from '../utils/os';
import { SUCCESS_CODE } from './code';
import type { GetPartitionsResp, GetRoleResp } from './type';

/** API 路径 */
enum ApiPath {
  GetPartition = 'get_game_partition_v3',
  GetRole = 'get_game_role_v3',
}

/** 接入层鉴权 */
const BUSINESS_ID = 'yyb_h5';
const ACCESS_KEY = 'yyb_h5_123';

/**
 * 获取区服列表
 * @param pkgName 游戏包名
 * @param osType 操作系统类型
 */
export async function getPartitions(pkgName: string, osType: OSType) {
  const resp = await accessRequester.request({
    cmd: ApiPath.GetPartition,
    businessID: BUSINESS_ID,
    accessKey: ACCESS_KEY,
    needAuthHeader: true,
    isTest: utils.isTestEnv(),
    data: {
      package_name: pkgName,
      os_type: osType,
    },
  });

  if (resp.code !== SUCCESS_CODE) {
    console.error('[getPartitions] 获取区服列表失败', resp);
    return;
  }

  return camelizeKeys<GetPartitionsResp>(resp.body as any);
}

/**
 * 获取角色列表
 * @param pkgName 包名
 * @param osType 操作系统类型
 * @param partition 区服信息
 */
export async function getRoles(
  pkgName: string,
  osType: OSType,
  partition: Partition,
) {
  const resp = await accessRequester.request({
    cmd: ApiPath.GetRole,
    businessID: BUSINESS_ID,
    accessKey: ACCESS_KEY,
    needAuthHeader: true,
    isTest: utils.isTestEnv(),
    data: {
      package_name: pkgName,
      os_type: osType,
      partition_id: partition.id,
    },
  });

  if (resp.code !== SUCCESS_CODE) {
    console.error('[getRoles] 获取角色列表失败', resp);
    return;
  }

  return camelizeKeys<GetRoleResp>(resp.body as any);
}
