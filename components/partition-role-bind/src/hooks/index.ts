import { computed, ref } from 'vue';
import { useMokaEditor } from '@tencent/moka-editor-helper';
import { store, utils } from '@tencent/moka-ui-domain';
import { isYYB, safeJsonStringify } from '@tencent/mole-utils-lib';
import { getLoginInfoFromCookie, LoginType } from '@tencent/yyb-login-core';
import { type Partition, type PartitionRoleQQ, type Role, PartitionType, ShowOSType } from '@tencent/yyb-partition-role-select';

import { getPartitions, getRoles } from '../api';
import { getOSType, getShowOSTypeBySupportScene, OS_TYPE_NAME, OSType } from '../utils/os';
import { getSupportSpecialScene } from '../utils/scene';

const { usePartitionRoleStore } = store;

export function usePartitionRoleHook(packageName: string) {
  const scenes = getSupportSpecialScene();
  const showOSType = getShowOSTypeBySupportScene(scenes);

  /** 设备操作系统类型 */
  const osType = getOSType(showOSType);
  /** 区服类型 */
  const partitionType = ref<PartitionType>();
  /** 区服选择 Store */
  const partitionStore = usePartitionRoleStore();
  /** 是否已经初始化 */
  const isInit = ref(false);
  /** 已绑定的角色 */
  const boundPartitionAndRole = computed(() => {
    const openID = utils.getLoginUserOpenID();
    return partitionStore.getPartitionRole(packageName, openID);
  });

  /**
   * 校验并设置角色和区服
   * @param partitions 区服列表
   * @param defaultPartition 默认区服
   * @param defaultRole 默认角色
   */
  async function validatePartitionRole(
    partitions: Partition[],
    defaultPartition?: Partition | null,
    defaultRole?: Role | null,
  ) {
    if (!defaultPartition) {
      console.log('[validatePartitionRole] 未绑定默认区服和角色');
      return;
    }

    const validPartition = partitions.find(partition => partition.id === defaultPartition.id);
    if (!validPartition) {
      console.error('[validatePartitionRole] 校验区服失败', packageName, defaultPartition);
      return;
    }

    if (partitionType.value !== PartitionType.PARTITION_AND_ROLE) {
      return {
        validPartition,
        validRole: null,
      };
    }

    const resp = await getRoles(packageName, osType, validPartition);
    const validRole = resp?.roleList?.find(role => role.id === defaultRole?.id);
    if (!validRole) {
      console.error('[validatePartitionRole] 校验区服角色失败', packageName, defaultPartition, defaultRole);
      return;
    }

    return {
      validRole,
      validPartition,
    };
  }

  /**
   * 设置区服角色信息
   * @param data 区服角色信息
   * @returns
   */
  function setPartitionRole(data: PartitionRoleQQ) {
    const loginInfo = getLoginInfoFromCookie();
    // 未登录情况下仅展示默认状态，不请求接口
    if (loginInfo.loginType === LoginType.None || !loginInfo.openID) {
      console.warn('[setPartitionRole] 用户未登录');
      return;
    }

    partitionStore.setPartitionRole(
      packageName,
      loginInfo.openID,
      data,
    );
  }

  /** 初始化 */
  async function init() {
    console.log(`[init] 区服组件初始化 packageName: ${packageName}, osType: ${osType}, showOSType:${showOSType}`);
    const { inEditor } = useMokaEditor();
    // 在编辑器中不初始化
    if (inEditor) {
      isInit.value = true;
      partitionType.value = PartitionType.PARTITION_AND_ROLE;
      return;
    }

    if (!packageName) {
      console.error('[init] 组件配置的包名为空，请检查组件配置是否正确', packageName);
      return;
    }

    const data = await getPartitions(packageName, osType);

    if (!data) {
      console.error('[init] 获取区服信息为空', packageName);
      return;
    }

    const {
      partitionType: type,
      partitionList,
      defaultPartition,
      defaultRole,
      savedInfo,
    } = data;
    partitionType.value = type;
    isInit.value = true;

    if (partitionType.value === PartitionType.NOT_PARTITION) {
      console.error('[init] 当前游戏无需选择区服，请检查组件配置是否正确', packageName);
      return;
    }

    if (!partitionList) {
      console.error('[init] 当前需要选择区服的游戏不存在区服', packageName);
      return;
    }

    // 标记存在区服绑定组件
    partitionStore.setPartitionRoleBind(packageName);

    // 应用宝内只能选择安卓，应用宝外才支持 IOS、安卓两种
    let lastOsType = isYYB(navigator.userAgent) ? osType : (savedInfo?.lastSavedOsType || osType);
    // 当前活动允许选择的区服与最后一次选中区服不匹配
    if (
      (lastOsType === OSType.ANDROID && showOSType === ShowOSType.IOS)
      || (lastOsType === OSType.IOS && showOSType === ShowOSType.Android)
    ) {
      // 重置为活动允许选择的区服进行选中角色的匹配
      lastOsType = osType;
    }

    const lastSavedInfo = Object.values(savedInfo?.data || {}).find(item => item.osType === lastOsType);
    let validPartition = lastSavedInfo?.partition || defaultPartition || null;
    let validRole = lastSavedInfo?.role || defaultRole || null;
    console.log(`[init] 上一次选中的区服和角色: lastOsType: ${lastOsType}, validPartition: ${safeJsonStringify(validPartition)}, validRole:${safeJsonStringify(validRole)}`);
    // 如果 lastSavedInfo 存在则不校验，原因如下：
    // 若最后一次选择 IOS，与默认拉取的安卓 partitionList 肯定不匹配
    // 考虑 lastSavedInfo 数据是正确的，校验需要重复获取 IOS partitionList；故非必要不校验
    if (!lastSavedInfo) {
      const validRes = await validatePartitionRole(
        partitionList,
        defaultPartition,
        defaultRole,
      );

      if (!validRes) {
        console.error('[init] 区服校验不通过', packageName);
        return;
      }

      ({ validPartition, validRole } = validRes);
    }

    setPartitionRole({
      osType: {
        id: lastOsType,
        name: OS_TYPE_NAME[lastOsType],
      },
      partition: validPartition,
      role: validRole,
    });
  }

  return {
    init,
    isInit,
    partitionType,
    setPartitionRole,
    boundPartitionAndRole,
    actSpecialScenes: scenes,
    showOSType,
  };
}
