import { isYYB } from '@tencent/mole-utils-lib';
import { ShowOSType } from '@tencent/yyb-partition-role-select';

import { SpecialScene } from './scene';

/** 系统类型 */
export enum OSType {
  /** 安卓系统 */
  ANDROID = 1,
  /** IOS系统 */
  IOS = 2,
}

/** 系统类型 */
export const OS_TYPE_NAME = {
  [OSType.IOS]: '苹果',
  [OSType.ANDROID]: '安卓',
};

/**
 * 获取系统类型
 * 根据 UA 获取系统类型，非 IOS 统一认定为安卓
 * @returns 系统类型
 */
export const getOSType = (showOSType: ShowOSType = ShowOSType.Both): OSType => {
  // 应用宝直接返回安卓
  const ua = navigator.userAgent;
  if (isYYB(ua)) {
    return OSType.ANDROID;
  }

  // 仅展示安卓
  if (showOSType === ShowOSType.Android) {
    return OSType.ANDROID;
  }

  // 仅展示 iOS
  if (showOSType === ShowOSType.IOS) {
    return OSType.IOS;
  }

  // 默认
  const isIOS = /\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/.test(ua);
  if (isIOS) {
    return OSType.IOS;
  }

  return OSType.ANDROID;
};

/**
 * 根据支持的特殊场景获取允许的 OS 类型
 * @param supportScenes 活动特殊场景
 * @returns 活动允许的 OS 类型
 */
export const getShowOSTypeBySupportScene = (supportScenes: SpecialScene[]): ShowOSType => {
  // 宝内默认仅安卓
  if (isYYB(navigator.userAgent)) {
    return ShowOSType.Android;
  }

  // 不支持 IOS，仅支持选择安卓的区服
  if (!supportScenes.includes(SpecialScene.IOS)) {
    return ShowOSType.Android;
  }

  // 兜底走默认逻辑
  return ShowOSType.Both;
};
