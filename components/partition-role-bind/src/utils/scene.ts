/** 活动投放特殊场景 */
export enum SpecialScene {
  /** iOS */
  IOS = '1',
}

/**
 * 获取支持访问活动页的特殊场景
 * @returns 支持访问活动页的特殊场景，默认值为都支持
 */
export function getSupportSpecialScene(): SpecialScene[] {
  const config = (globalThis as any).magicUiconfig?.[0];
  if (!config) return [SpecialScene.IOS];

  const { actSpecialScene } = config;
  return (actSpecialScene as string)?.split(',').filter(Boolean) as SpecialScene[] ?? [SpecialScene.IOS];
}
