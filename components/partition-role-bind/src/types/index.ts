import { Position } from '../constant';

/** 组件配置 */
export interface ComponentConfig {
  /** 游戏包名 */
  packageName: string;
  /** 位置，居中、居左、居右 */
  position: Position;
  /** 边距 */
  margin: number;
  /** 未绑定的提示文案 */
  unBoundTip: string;
  /** 游戏 ICON */
  gameIcon: string;
  /** 是否隐藏游戏 ICON */
  hideGameIcon: boolean;
  /** 文本颜色 */
  textColor: string;
  /** 字体大小 */
  fontSize: number;
  /** 背景图颜色 */
  backgroundColor: string;
  /** 背景图 */
  backgroundImage: string;
  /** 切换按钮背景图，给终端预留配置项 */
  switchIconImage: string;
  /** 左箭头背景图，给终端预留配置项 */
  leftArrowImage: string;
  /** 是否需要强制选区服 */
  needPartitionAndRole: boolean;
  /** 蒙层层级 */
  maskZIndex?: number;
}
