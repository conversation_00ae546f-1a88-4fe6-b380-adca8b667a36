<!-- eslint-disable max-len -->
<template>
  <div
    :style="btnIconStyle"
    class="svg-icon"
  >
    <svg
      v-if="type === OperateBtnType.Arrow"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      :style="btnIconStyle"
    >
      <rect
        x="4.04492"
        y="5.88959"
        width="2.5"
        height="10"
        rx="1.25"
        transform="rotate(-49.0977 4.04492 5.88959)"
        fill="currentColor"
        style="fill-opacity:1;"
      />
      <rect
        width="2.5"
        height="10"
        rx="1.25"
        transform="matrix(0.654772 0.755827 0.755827 -0.654772 4 15.5637)"
        fill="currentColor"
        style="fill-opacity:1;"
      />
    </svg>
    <svg
      v-else-if="type === OperateBtnType.Switch"
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      :style="btnIconStyle"
    >
      <path
        d="M0.0299222 5.75198C0.0299223 5.43798 0.149921 5.13198 0.373922 4.90598L4.60592 0.663977C5.05192 0.215977 5.75792 0.233977 6.18592 0.699977C6.61392 1.16598 6.59792 1.90598 6.15192 2.35398L3.92992 4.57998L16.8019 4.57998C17.4199 4.57998 17.9199 5.10398 17.9199 5.75198C17.9199 6.39998 17.4199 6.92198 16.8019 6.92198L1.14592 6.92198C0.689922 6.92198 0.279922 6.63198 0.109922 6.18798C0.0559213 6.04598 0.0299222 5.89798 0.0299222 5.75198ZM0.0299219 13.128C0.029922 12.482 0.529922 11.958 1.14792 11.958L16.8019 11.958C17.2579 11.958 17.6679 12.248 17.8379 12.692C18.0079 13.136 17.9039 13.642 17.5739 13.974L13.3419 18.216C12.8959 18.664 12.1899 18.646 11.7619 18.18C11.3339 17.714 11.3499 16.974 11.7959 16.526L14.0179 14.3L1.14392 14.3C0.529922 14.298 0.0299219 13.774 0.0299219 13.128Z"
        fill="currentColor"
        style="fill-opacity:1;"
      />
    </svg>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed } from 'vue';

import { OperateBtnType } from '../constant';

const props = defineProps({
  /** 按钮类型 */
  type: {
    type: String as PropType<OperateBtnType>,
    default: OperateBtnType.Switch,
  },
  /** 按钮颜色 */
  color: {
    type: String,
    default: '#fff',
  },
  /** 按钮宽度 */
  width: {
    type: String,
    default: '0.1rem',
  },
  /** 按钮高度 */
  height: {
    type: String,
    default: '0.1rem',
  },
});

/** 按钮样式 */
const btnIconStyle = computed(() => ({
  color: props.color,
  width: props.width,
  height: props.height,
  lineHeight: props.height,
}));

</script>

<style scoped>
.svg-icon {
  text-align: center;
}
</style>
