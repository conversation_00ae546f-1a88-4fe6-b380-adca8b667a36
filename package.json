{"name": "moka-ui", "version": "1.0.0", "private": true, "scripts": {"bootstrap": "pnpm clean && pnpm install && pnpm build", "build": "turbo run build", "clean": "pnpm -r exec rm -rf node_modules dist", "lint": "turbo run lint"}, "author": "yamachen<<EMAIL>>", "dependencies": {"@tencent/mesh-sdk": "^1.1.15", "@tencent/moka-data-core": "1.0.40", "@tencent/moka-ui-domain": "moka-latest", "@tencent/mole-utils-lib": "^2.0.1", "@tencent/mole-jsbridge": "^1.0.4", "@tencent/yyb-partition-role-select": "^2.0.13", "@tencent/txv-utils": "^1.2.1", "humps": "^2.0.1", "lodash": "^4.17.21", "vue": "^3.2.25"}, "devDependencies": {"@tencent/eslint-config-mole": "^1.0.12", "@tencent/eslint-config-tencent": "^1.0.4", "@tencent/moka-plugin-extend": "^0.0.1", "@tencent/moka-schema": "^0.0.4", "@tencent/mole-report": "^1.0.11", "@types/humps": "^2.0.6", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "@vitejs/plugin-vue": "^4.0.0", "cross-env": "^7.0.3", "eslint": "^8.44.0", "eslint-plugin-vue": "^9.15.1", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jest": "^24.7.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-jsdoc": "^32.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-import-curly": "^1.0.2", "husky": "^8.0.3", "turbo": "^1.10.16", "typescript": "^4.5.4", "vite": "^4.0.0", "vite-plugin-externals": "^0.6.2", "vite-plugin-lib-inject-css": "^2.0.0", "vitest": "^1.4.0", "vue-tsc": "^0.29.8"}, "pnpm": {"overrides": {"vue-eslint-parser": "9.3.2", "@tencent/yyb-partition-role-select": "^2.0.13", "@tencent/yyb-login-core": "^1.0.9"}}, "license": "ISC"}