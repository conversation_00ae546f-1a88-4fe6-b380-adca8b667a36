import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { address } from 'ip';
import vue from '@vitejs/plugin-vue';

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
  },
  server: {
    open: '/example/index.html',
    port: 5173,
    hmr: {
      host: address(), // 支持手机代理调试中的hmr
      // host: '127.0.0.1', // 支持手机代理调试中的hmr
      protocol: 'wss',
    },
  },
  plugins: [
    dts({
      outputDir: 'dist',
      entryRoot: 'src',
    }),
    vue(),
  ],
});
