import { accessRequester } from '@tencent/moka-data-core';

import { YSDKLoginInfo, YSDKLoginType } from '../types';

/** API 路径 */
enum ApiPath {
  /** 判断 ysdk openid 是否能转为应用宝的 openid */
  HasYYBOpenid = '/has_yyb_openid',
}

/** 活动接入层业务 id */
const ACTIVITY_PLAT_BUSINESS_ID = 'yyb_activity_plat';

/** 活动平台接入 key */
const ACTIVITY_PLAT_ACCESS_KEY = 'yyb_activity_plat_h5';

/** 用户类型 */
export enum UserType {
  /** 未知用户 */
  UNKNOWN = 0,
  /** QQ用户 */
  QQ = 1,
  /** 微信用户 */
  WX = 2,
  /** 单机用户 */
  GUEST = 3,
  /** 手机用户 */
  PHONE = 4,
}

const userTypeByLoginType = {
  [YSDKLoginType.QQ]: UserType.QQ,
  [YSDKLoginType.WX]: UserType.WX,
  [YSDKLoginType.PHONE]: UserType.PHONE,
  [YSDKLoginType.None]: UserType.UNKNOWN,
};

interface CheckYsdkUserYYBOpenidResp {
  /** 错误码 */
  result: number;
  /** 错误信息 */
  err_msg: string;
  /** 是否能转换出 yybOpenID */
  has_yyb_open_id: boolean;
}

/**
 * 检查 ysdk 用户的 openid 是否能转换为应用宝 openid
 * @param loginInfo ysdk 的登录信息
 * @returns 是否能转换
 */
export async function checkYSDKUserYYBOpenid(loginInfo: YSDKLoginInfo): Promise<Boolean> {
  const { loginType, openId, appId, token } = loginInfo;
  const userType = userTypeByLoginType[loginType] || UserType.UNKNOWN;
  const resp = await accessRequester.request<unknown, CheckYsdkUserYYBOpenidResp>({
    cmd: ApiPath.HasYYBOpenid,
    data: {
      user_type: userType,
      game_open_id: openId,
      app_id: appId,
      access_token: token,
    },
    businessID: ACTIVITY_PLAT_BUSINESS_ID,
    accessKey: ACTIVITY_PLAT_ACCESS_KEY,
    needAuthHeader: true,
  });

  const { code, tip, body: res } = resp;

  if (!res) {
    console.error(`[accessRequest: checkYsdkUserYYBOpenid] 请求异常, code: ${code}, tip: ${tip}`);
    return false;
  }

  const { result, err_msg: errMsg, has_yyb_open_id: hasYybOpenid } = res;

  if (result !== 0) {
    console.error(`[accessRequest: checkYsdkUserYYBOpenid] 请求异常，code: ${result}, msg: ${errMsg}`);
    return false;
  }

  return hasYybOpenid;
}
