import { closeDialog, showDialog } from 'vant';
import { type App } from 'vue';
import MoleJsBridge from '@tencent/mole-jsbridge';
import {
  addParamsToURL,
  debounce,
  getCookies,
  isIOS,
  isPC,
  isPCYYB,
  isSYZS,
  safeJsonStringify,
  sleep,
} from '@tencent/mole-utils-lib';
import { urlToJson } from '@tencent/txv-utils';

import { checkYSDKUserYYBOpenid } from './anti-corruption/ysdk';
import {
  DEFAULT_SPECIAL_SCENES,
  EHE_PACKAGE_NAME,
  JSB_NO_PERMISSION,
  YYB_FIRST_PAGE_TMAST,
  YYB_MICRO_DOWNLOAD_URL,
  YYB_OPEN_WEBVIEW_DEEP_LINK,
  YYB_PACKAGE_NAME,
} from './const';
import {
  ActScene,
  ActSpecialScene,
  OtherSceneComponentEventType,
  OtherSceneEventType,
  TransferType,
  YSDKLoginInfo,
  YSDKLoginType,
  YYBActConfig,
} from './types';

/** 避免多次调用 jsb 做合并处理 */
let getJsbInstallStatusPromise: any;

export default {
  envScene: ActScene.Other,
  actSpecialScene: [] as ActSpecialScene[],
  /** 是否禁用插件 */
  disabled: false,
  /** 应用已安装 */
  isAppInstalled: false,
  /** 配置 */
  yybPageId: '',
  yybPageUrl: '',
  /** 微下载中转页渠道号 */
  mobileYYBGrowthChannel: '',
  yybType: TransferType.YYBTransferPage,
  yybRedirectPageUrl: '',
  /** 缓存已拦截的容器，避免添加拦截事件 */
  clickInterceptDoms: [] as HTMLElement[],
  /** 大同上报方法 */
  $report: null,

  /** 初始化 */
  install(app: App) {
    console.log('[plugins:yybAct] init');
    this.envScene = this.getEnvScene();

    const config: YYBActConfig = {
      useYYBActPlugin: false,
      yybPageId: '',
      yybPageUrl: '',
      yybType: TransferType.YYBTransferPage,
      yybRedirectPageUrl: '',
      otherSceneEventType: OtherSceneEventType.None,
      ...((window as any)?.magicUiconfig?.[0] ?? {}),
    };

    // 活动特殊场景：若 actSpecialScene 字段为空，默认需要已选中 iOS
    this.actSpecialScene = (config?.actSpecialScene?.split(',').filter(Boolean) ?? DEFAULT_SPECIAL_SCENES) as ActSpecialScene[];
    this.yybPageId = config.yybPageId;
    this.yybPageUrl = config.yybPageUrl;
    this.yybRedirectPageUrl = config.yybRedirectPageUrl;
    this.mobileYYBGrowthChannel = config.mobileYYBGrowthChannel;
    this.yybType = config.yybType;

    this.$report = app.config.globalProperties.$report;

    console.log('[plugins:yybAct] canUse', config.useYYBActPlugin);

    if (!config.useYYBActPlugin) {
      this.disabled = true;
      return;
    }

    this.bindGlobalEvent(app);

    void this.checkYSDKUser();

    // 环境判断处理
    this.handleAllowScene(config);

    setTimeout(() => {
      this.handleActSpecialScenes();
    }, 100); // 延迟 100 ms 执行，防止 dom 元素未创建
  },

  handleAllowScene(config: YYBActConfig) {
    // 环境是允许，不需要处理
    if (this.checkAllowScene(config.actScene)) {
      console.log('[plugins:yybAct] actScene allow');
      return;
    }

    // 环境不允许，按在配置策略处理
    this.handleOtherSceneEvent(config);
  },

  showBanDialog(message: string) {
    // 关闭所有现在的弹窗
    closeDialog();
    void showDialog({
      title: '温馨提醒',
      message,
      showConfirmButton: false,
    });
  },

  /** 活动访问设备场景处理 */
  handleActSpecialScenes() {
    console.log(`[handleAllowDeviceScene] 处理应用宝特殊场景，scenes: ${this.actSpecialScene}`);
    // 不支持 iOS 访问
    if (isIOS(navigator.userAgent) && !this.actSpecialScene.includes(ActSpecialScene.IOS)) {
      this.showBanDialog('iOS用户无法参与本次活动');

      // 重置根组件的 onClick 绑定的点击事件
      document.body.onclick = null;
      // 在根元素上绑定捕获阶段的点击事件并阻止后续组件所有事件的点击调用
      document.body.addEventListener('click', debounce((event) => {
        // 阻止后续所有子元素、根元素的点击事件捕获、冒泡，不允许点击
        event.stopImmediatePropagation();
        this.showBanDialog('iOS用户无法参与本次活动');
      }, 100), true);

      return;
    }
  },

  /** 获取活动场景类型 */
  getEnvScene(): ActScene {
    const envRuleMaps: Record<number, RegExp | (() => boolean)> = {
      [ActScene.YYB]: /yyb_version|qqdownloader/,
      [ActScene.EHE]: /ehe_version/,
      [ActScene.YSDK]: /YSDKVersion\/([\d.]+)/,
      [ActScene.PcYYB]: /YYBAppClient/,
      [ActScene.SYZS]: () => isSYZS(navigator.userAgent),
      // PC 站外：属于 PC 但是不属于 PC 应用宝和手游助手
      [ActScene.PcOutSide]: () => isPC(navigator.userAgent) && !isPCYYB(navigator.userAgent) && !isSYZS(navigator.userAgent),
    };

    // eslint-disable-next-line no-restricted-syntax
    for (const envKey in envRuleMaps) {
      const envRule = envRuleMaps[envKey];
      // 函数类型
      if (typeof envRule === 'function') {
        if (envRule()) {
          return Number(envKey);
        }
      } else {
        // 正则类型
        if (envRule.test(navigator.userAgent)) {
          return Number(envKey);
        }
      }
    }

    return ActScene.Other;
  },

  /**
   * 检测当前环境是否允许
   * @param {string} actScene uiConfig 中的场景值
   */
  checkAllowScene(actScene = '') {
    // 未开启插件 环境必然允许
    if (this.disabled) {
      return true;
    }

    return actScene.split(',').includes(`${this.envScene}`);
  },

  /**
   * 处理其他场景的处理方式
   * @param {YYBActConfig} config uiConfig配置
   */
  handleOtherSceneEvent(config: YYBActConfig) {
    console.log('[plugins:yybAct] otherSceneEventType', config.otherSceneEventType);
    if (config.otherSceneEventType === OtherSceneEventType.ClickMask) {
      void this.createGlobalMaskClickJump(document.body);
      return;
    }

    if (config.otherSceneEventType === OtherSceneEventType.JumpToRedirectPage) {
      this.jumpToRedirectPage();
      return;
    }
  },

  /** 跳转中转页 */
  jumpToRedirectPage() {
    const thisPageParams = urlToJson(window.location.href);
    let redirectPageUrl = '';
    const isYSDK = this.envScene === ActScene.YSDK;
    // 中转页
    if (this.yybType === TransferType.YYBTransferPage && this.yybPageId) {
      const link = this.yybPageUrl ? addParamsToURL(this.yybPageUrl, {
        ...thisPageParams,
        ...urlToJson(this.yybPageUrl),
      }) : window.location.href;
      const includeBack = isYSDK ? 1 : 0;
      redirectPageUrl = `https://qzs.qq.com/open/mobile/transfer-page/index.html?id=${this.yybPageId}&includeBack=${includeBack}&dest=${encodeURIComponent(link)}`;
    }

    // 微下载中转页
    if (this.yybType === TransferType.MicroDownload) {
      const jumpActTmastURL = addParamsToURL(YYB_OPEN_WEBVIEW_DEEP_LINK, {
        // 需要配置返回路径，否则返回会卡白屏
        back_jump_url: YYB_FIRST_PAGE_TMAST,
        url: window.location.href,
      });

      redirectPageUrl = addParamsToURL(YYB_MICRO_DOWNLOAD_URL, {
        g_f: this.mobileYYBGrowthChannel?.trim() ?? '',
        android_scheme: jumpActTmastURL,
      });
    }

    // 其他中转页
    if (this.yybType === TransferType.Other && this.yybRedirectPageUrl) {
      const link = addParamsToURL(this.yybRedirectPageUrl, {
        ...thisPageParams,
        ...urlToJson(this.yybRedirectPageUrl),
      });
      redirectPageUrl = link;
    }

    console.log('[plugins:yybAct] jumpToRedirectPage, url: ', redirectPageUrl);

    if (!redirectPageUrl) return;

    if (isYSDK) {
      location.href = redirectPageUrl;
    } else if (this.yybType === TransferType.MicroDownload) {
      // 从活动跳转微下载如果马上跳转，微信场景下会出现 JSB 调用超时，推测是 JSB 被污染了，因此延时至异步跳转
      setTimeout(() => {
        window.location.replace(redirectPageUrl);
      });
    } else {
      window.location.replace(redirectPageUrl);
    }
  },

  /**
   * 全局遮罩点击跳转
   * @param {HTMLElement} root 生成遮罩的父容器
   * @param {function} beforeJump 点击跳转前的事件
   */
  async createGlobalMaskClickJump(root: HTMLElement, beforeJump?: () => Promise<void>) {
    // 分享来源
    const shareSource: ActScene = Number(urlToJson(window.location.href).share_source) || ActScene.Other;

    // TODO: 暂时兜底跳转应用宝，鹅盒暂未接入
    let pkgName = YYB_PACKAGE_NAME;
    if ([ActScene.YSDK, ActScene.YYB].includes(shareSource)) {
      pkgName = YYB_PACKAGE_NAME;
    }

    if (shareSource === ActScene.EHE) {
      pkgName = EHE_PACKAGE_NAME;
    }

    const moleJsBridge = MoleJsBridge.getJSBridge();

    if (!getJsbInstallStatusPromise) {
      getJsbInstallStatusPromise = moleJsBridge?.app?.queryAppVersions([{
        packageName: pkgName,
      }]);
    }

    try {
      const appVersionsResp = await getJsbInstallStatusPromise;
      const appVersion = appVersionsResp?.data?.[0];

      // 不存在版本号或者域名没有权限（手 Q）
      if (!appVersion || appVersion === JSB_NO_PERMISSION) {
        this.isAppInstalled = false;
      } else {
        this.isAppInstalled = true;
      }
    } catch (error) {
      console.error('[createGlobalMaskClickJump] err: ', error);
      this.isAppInstalled = false;
    }

    // 若已添加拦截事件，则不做处理
    if (this.clickInterceptDoms.includes(root)) {
      return;
    }

    this.clickInterceptDoms.push(root);

    // 重置根组件的 onClick 绑定的点击事件
    // eslint-disable-next-line no-param-reassign
    root.onclick = null;

    // badCase: 当组件根元素绑定了捕获阶段的点击函数，则无法阻止该点击函数（不过子元素的捕获和冒泡点击都是可以阻止的）
    // 在根元素上绑定捕获阶段的点击事件并阻止后续组件所有事件的点击调用
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    root.addEventListener('click', async (event) => {
      // 阻止后续所有子元素、根元素的点击事件捕获、冒泡
      event.stopImmediatePropagation();
      // 跳转前的拦截
      await beforeJump?.();
      if (!this.isAppInstalled) {
        this.jumpToRedirectPage();
        return;
      }

      if (pkgName === YYB_PACKAGE_NAME) {
        const tmast = `${YYB_OPEN_WEBVIEW_DEEP_LINK}${encodeURIComponent(location.href)}`;
        try {
          await moleJsBridge?.app?.launchApp?.({
            packageName: pkgName,
            deepLink: tmast,
          });
        } catch (error) {
          this.jumpToRedirectPage();
        }
      }
    }, true);
  },

  /** 挂载全局事件 */
  bindGlobalEvent(app: App) {
    // 全局事件 - 获取该场景值是否允许显示
    // eslint-disable-next-line no-param-reassign
    app.config.globalProperties.$isComponentVisibleInActScene = (
      scene: string,
      eventType: OtherSceneComponentEventType,
    ) => {
      // 未开启插件 不处理
      if (this.disabled) {
        return true;
      }

      // 不允许场景，且事件类型为隐藏组件，则该组件不可见
      return !(!this.checkAllowScene(scene) && eventType === OtherSceneComponentEventType.Hide);
    };

    // 全局事件 - 活动场景对组件是适配事件，moka 渲染器主动触发组件的处理方式
    // eslint-disable-next-line no-param-reassign
    app.config.globalProperties.$actSceneComponentAdapter = (options: {
      scene: string;
      eventType: OtherSceneComponentEventType;
      componentId: string;
    }) => {
      // 未开启插件 不处理；
      if (this.disabled) {
        return true;
      }

      const { scene, componentId, eventType } = options;

      // 环境允许
      if (this.checkAllowScene(scene)) {
        return true;
      }

      // 活动中台会给每个组件挂载组件 id 拼接的 class
      const dom = document.querySelector(`.c_${componentId}`);

      // 环境不允许, 组件增加点击蒙层
      if (eventType === OtherSceneComponentEventType.ClickMask && dom) {
        void this.createGlobalMaskClickJump(dom as HTMLElement);
      }

      return false;
    };
  },

  /** 创建 ysdk 点击跳转蒙层 */
  async createYSDKMaskClickJump() {
    return this.createGlobalMaskClickJump(document.body, async () => {
      window.magicApp.toast('您还没有登录过应用宝噢！');
      // 等待三秒
      await sleep(3000);
    });
  },

  /**
   * 校验 ysdk 用户是否可以参与活动
   * @description 手机号登录，或者 ysdk 的 openid 无法转换为应用宝 openid 的用户兜底处理
   */
  async checkYSDKUser() {
    // 非 ysdk 场景
    if (this.envScene !== ActScene.YSDK) {
      return;
    }

    const cookie = getCookies(document.cookie);
    const loginInfo: YSDKLoginInfo = {
      loginType: cookie.logintype as YSDKLoginType,
      appId: cookie.appid,
      openId: cookie.openid,
      token: cookie.access_token,
    };

    // 登录态异常或者手机登录用户
    if (!loginInfo.openId || !loginInfo.token || !loginInfo.appId || loginInfo.loginType === YSDKLoginType.PHONE) {
      console.log('[accessRequest: checkYSDKUser] 登录态异常或者为手机登录用户, loginInfo:', safeJsonStringify(loginInfo));
      return this.createYSDKMaskClickJump();
    }

    if ([YSDKLoginType.QQ, YSDKLoginType.WX].includes(loginInfo.loginType)) {
      const hasYybOpenid = await checkYSDKUserYYBOpenid(loginInfo);
      console.log('[accessRequest: checkYSDKUser] 结果, hasYybOpenid: ', hasYybOpenid);
      if (hasYybOpenid) {
        (this.$report as any)?.report({}, 'ysdkOpenidChangeSuccess');
        return;
      }

      return this.createYSDKMaskClickJump();
    }
  },
};
