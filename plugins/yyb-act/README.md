# moka 活动兼容插件

## 全局依赖参数说明
该插件初始化时会从 window.magicUiconfig[0] 中读取下面字段，以处理展示策略

``` typescript
export interface YYBActConfig {
  /** 是否使用该插件 */
  useYYBActPlugin: boolean;
  /** 允许场景 */
  actScene: string;
  /** 应用宝中转页 id */
  yybPageId: string,
  /** 应用宝端外中转页指定拉起链接 id */
  yybPageUrl: string;
  /** 外部场景处理方式 */
  otherSceneEventType: OtherSceneEventType;
}

/** 外部场景处理方式 */
export enum OtherSceneEventType {
  /** 不处理 */
  None = 0,
  /** 跳转中转页 */
  JumpToRedirectPage = 1,
  /** 全局点击遮罩 */
  ClickMask = 2,
}
```

## 场景适配策略
moka 中配置场景适配的 actScene 格式为 string，如：'1,2';
```typescript
/** 活动场景 */
export enum ActScene {
  /** 应用宝端内 */
  YYB = 1,
  /** 端外 */
  Other = 2,
  /** PC应用宝 */
  PcYYB = 3,
  /** 鹅盒 */
  EHE = 4,
  /** YSDK */
  YSDK = 5,
}
```

## 全局挂载事件

### getActSceneAllow 获取该场景值是否允许
``` typescript
app.config.globalProperties.$isVisibleInActScene('1,2', 2);
```

### actSceneComponentAdapter 给组件挂载场景处理策略
``` typescript
app.config.globalProperties.$actSceneComponentAdapter({
  // 场景值
  scene: '1',
  // 处理类型
  eventType: 2,
  // 组件id
  componentId: 'ffff'
});
```

## 处理策略

### 点击遮罩
1. 页面级别在 body 或 组件级别在组件最外层，判断父容器是否存在 position 属性中的 relative | fixed | absolute | sticky 这些影响定位的属性，如不存在则为父容器添加 position: relative
2. 在该容器内添加透明点击遮罩层
3. 点击后根据当前环境执行跳转策略

### 组件隐藏
moka页面组件显示特征中，在class中有一个组件id： c_yyb-download_5a4aae3d
因此对组件的处理都以此id查询dom

```HTML
<div data-v-82e2e87b="" class="mod-app yyb-download-btn moka-proxy-component c_yyb-download_5a4aae3d" dt-eid="yyb-download" dt-params="scene=10580&amp;ptag=undefined&amp;source_scene=undefined&amp;source_modeltype=undefined&amp;source_passphrase_id=undefined&amp;appid=12127266&amp;_module=下载_yyb-download_5a4aae3d&amp;mod_title=下载_yyb-download_5a4aae3d&amp;btn_title=下载_yyb-download_5a4aae3d" dt-cmd="hold=true" dt-send-beacon="true" visible="[object Object]" id="yyb-download_5a4aae3d" style="font-size: 0.14rem; color: rgb(0, 0, 0); background-color: rgba(255, 255, 255, 0); border-radius: 0px; width: 1.47rem; height: 0.41rem; text-align: center; position: absolute; left: 0.24rem; top: 3.08rem; font-weight: 500; background-repeat: no-repeat; background-size: 100% 100%;"><img data-v-82e2e87b="" src="https://yyb.qpic.cn/moka-imgs/1730366079856-6v4yw28zlqh.png" class="download-font-img"></div>
```


## 开发调试
### 本地调试
1.调试命令
```bash
pnpm dev
```
2.打开网页 http://localhost:5173/example/index.html