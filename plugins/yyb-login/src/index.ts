import type { App } from 'vue';
import {
  AppType,
  AuthType,
  isGrantOneAppAuth,
} from '@tencent/game-auth-sdk';
import { AppName, getAppName, isWeChat } from '@tencent/mole-utils-lib';
import { to } from '@tencent/txv-utils';
import type { BaseLogin, LoginOptions, SwitchLoginOptions } from '@tencent/yyb-login-core';
import { LoginDialog, SwitchDialog } from '@tencent/yyb-login-ui';
import { YYBUniLogin } from '@tencent/yyb-uni-login';

import { getPcViewType, initPCLoginListener, IS_PC_LOGIN } from './pc';

const loginUIConfig = {
  showLoginView: (loginInstance: BaseLogin, loginOptions?: LoginOptions) => {
    const instance = new LoginDialog({
      target: document.body,
      props: {
        loginInstance,
        loginOptions,
      },
    });

    return () => instance.$destroy();
  },
  showSwitchLoginView: (loginInstance: BaseLogin, loginOptions?: LoginOptions) => {
    const instance = new SwitchDialog({
      target: document.body,
      props: {
        loginInstance,
        loginOptions,
      },
    });

    return () => instance.$destroy();
  },
};

YYBUniLogin.setConfig({
  appLoginConfigs: [
    { env: AppName.YYB, ...loginUIConfig },
    { env: AppName.YSDK },
    { env: AppName.PCYYB, ...loginUIConfig },
    { env: AppName.SYZS, ...loginUIConfig },
  ],
  outsideAppLoginConfig: () => {
    // PC 走 PC 应用宝端外登录
    if (IS_PC_LOGIN) return { env: AppName.PCYYB, ...loginUIConfig };

    // 其他场景走移动应用宝的端外登录
    return {
      env: AppName.YYB,
      ...loginUIConfig,
    };
  },
});

const CODE = {
  noLogin: [10001],
};

export default {
  pluginType: 'login',
  name: '@tencent/magic-ui-plugin-yyb-login',
  /** 初始化 */
  install(app: App) {
    const loginInstance = YYBUniLogin.getInstance();

    // eslint-disable-next-line no-param-reassign
    app.config.globalProperties.$magicLogin = {

      /**
       * 检查是否登录
       * @returns 是否登录
       */
      checkLogin: async () => {
        const resp = await loginInstance.isLogin();
        const appName = getAppName(navigator.userAgent);
        // 应用宝内、ysdk、pc 登录直接使用 sdk 的 isLogin 结果
        if ([AppName.YYB, AppName.YSDK].includes(appName) || IS_PC_LOGIN) {
          return !!resp.data;
        }

        // 应用宝端外登录，调用接口判断登录态是否过期
        const [err] = await to(isGrantOneAppAuth({
          appId: '863',
          authAppType: AppType.game,
        }, [
          AuthType.gameRoleData,
          AuthType.gamePayData,
          AuthType.gameBehaviourData,
        ]));
        if (err && CODE.noLogin.includes(err.code)) {
          return false;
        }

        return !!resp.data;
      },

      /**
       * 打开登录
       */
      openLogin: async (loginOptions?: LoginOptions) => {
        const options = { needReload: true, ...(loginOptions || {}) };
        if (IS_PC_LOGIN) {
          options.viewType = getPcViewType();
        }

        if (isWeChat(navigator.userAgent)) {
          options.needUI = true; // 微信场景登录展示登录 ui
        }

        return loginInstance.openLogin(options);
      },

      /**
       * 切换登录
       */
      switchLogin: async (switchLoginOptions?: SwitchLoginOptions) => {
        const options = {
          needUI: true, // 切换登录需要切换登录 UI
          loginOptions: { needReload: true, needUI: false, ...(switchLoginOptions?.loginOptions || {}) },
        };

        if (IS_PC_LOGIN) {
          options.loginOptions.viewType = getPcViewType();
        }

        if (isWeChat(navigator.userAgent)) {
          options.loginOptions.needUI = true; // 微信场景切换登录需要展示登录 ui
        }

        return loginInstance.switchLogin(options);
      },

      /**
       * 避免其他组件使用到出错，实现一个空函数
       */
      getPayVip: async () => {},

      /**
       * 避免其他组件使用到出错，实现一个空函数
       */
      init: async () => {
        if (IS_PC_LOGIN) {
          initPCLoginListener();
        }
      },

      /**
       * 获取用户信息
       */
      getUserInfo: async () => {
        const loginInfoResp = await loginInstance.getLoginInfo(true);

        const { nickName, avatar, ...otherLoginInfo } = loginInfoResp.data || {};

        return {
          ...otherLoginInfo,
          vuserid: 0, // 目前业务没有使用 vuserid，默认为 0
          nickname: nickName,
          headImgUrl: avatar,
        };
      },

      /**
       * 退出登录
       */
      logout: async () => loginInstance.logout(true),
    };
  },
};
