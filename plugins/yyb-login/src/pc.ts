import { isPC, isSYZS, isWeChat } from '@tencent/mole-utils-lib';
import {
  ViewType,
} from '@tencent/yyb-client-login';
import { LoginFlowEvent, YYBLoginReturnCode } from '@tencent/yyb-login-core';
import { YYBUniLogin } from '@tencent/yyb-uni-login';

export const IS_PC_LOGIN = isPC(navigator.userAgent) && !isWeChat(navigator.userAgent);

const loginViewType = {
  /** 移动窄屏 */
  narrowScreen: ViewType.common,
  /** PC 宽屏 */
  wideScreen: ViewType.common2,
};

export function getPcViewType() {
  return window.innerWidth < 768 ? loginViewType.narrowScreen : loginViewType.wideScreen;
}

/** 初始化登录行为的监听 */
export function initPCLoginListener() {
  const loginInstance = YYBUniLogin.getInstance();
  // 登出成功时刷新页面
  loginInstance.on(LoginFlowEvent.LogoutSuccess, () => {
    location.reload();
  });

  // 登录行为异常时上报
  loginInstance.on(LoginFlowEvent.LoginFail, (resp) => {
    if (resp.code === YYBLoginReturnCode.LoginCancel) return;
    console.error(`[pcLogin initLoginEvent] ${isSYZS(navigator.userAgent) ? 'SYZS' : 'PCYYB'}  error`, resp);
  });
}
