<template>
  <div @click="handleLogin">执行登录</div>
  <div @click="getUserInfo">获取用户信息</div>
  <div @click="switchLogin">切换登录</div>
</template>

<script lang="ts" setup>
import { getCurrentInstance } from 'vue';
const instance = getCurrentInstance();

const loginInstance = instance?.appContext.config.globalProperties?.$magicLogin;
const handleLogin = async () => {
  const isLogin = await loginInstance.checkLogin();

  if (!isLogin) {
    await loginInstance.openLogin();
  }
}

const getUserInfo = async () => {
  const isLogin = await loginInstance.checkLogin();

  if (isLogin) {
    await loginInstance.getUserInfo();
  }
}

const switchLogin = async () => {
  await loginInstance.switchLogin();
}
</script>